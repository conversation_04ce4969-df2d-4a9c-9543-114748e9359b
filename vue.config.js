'use strict'
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  publicPath: './',
  productionSourceMap: false, // 设置上线后是否加载webpack文件
  lintOnSave: true,
  devServer: {
    port: 8083,
    open: false, //配置是否自动启动浏览器
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/nssoService': {
        target: 'http://172.18.234.111:8099/',
        changeOrigin: true,
        pathRewrite: {
          ['^/nssoService']: ''
        }
      },
      '/fusion-design-service': {
        target: 'http://172.18.194.143:8891/',// 要跨域的域名
        changeOrigin: true, // 是否开启跨域
        pathRewrite: {
          ['^/fusion-design-service']: ''
        }
      }
    }
  },
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  }
}
