<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<title>ckplayer</title>
		
		<style type="text/css">
			body {
				margin: 0;
				padding: 0px;
				font-family: "Microsoft YaHei", <PERSON><PERSON><PERSON>, "微软雅黑", <PERSON><PERSON><PERSON><PERSON>, "黑体";
				font-size: 18px;
			}
			p{
				padding-left: 2em;
			}
		</style>

	</head>

	<body>
		<div id="video" style="width: 100%; height: 400px;max-width: 600px;">
		</div>
		<script type="text/javascript" src="ckplayer/ckplayer.js" charset="UTF-8"></script>
		<script type="text/javascript">
			var videoObject = {
				container: '#video', //容器的ID或className
				variable: 'player', //播放函数名称
				//loop: true, //播放结束是否循环播放
				autoplay: true,//是否自动播放
				poster: 'material/poster.jpg', //封面图片
				preview: { //预览图片
					file: ['material/mydream_en1800_1010_01.png', 'material/mydream_en1800_1010_02.png'],
					scale: 2
				},
				//flashplayer:true,
				//live:true,
				//debug:true,
				video:[
			        ['http://img.ksbbs.com/asset/Mon_1703/05cacb4e02f9d9e.mp4', 'video/mp4', '中文标清', 0],
			        ['http://img.ksbbs.com/asset/Mon_1703/d0897b4e9ddd9a5.mp4', 'video/mp4', '中文高清', 0],
			        ['http://img.ksbbs.com/asset/Mon_1703/eb048d7839442d0.mp4', 'video/mp4', '英文高清', 0],
			        ['http://img.ksbbs.com/asset/Mon_1703/d30e02a5626c066.mp4', 'video/mp4', '英文超清', 0]
			    ]
			};
			var player = new ckplayer(videoObject);
		</script>
		<p>欢迎使用ckplayer，当前版本：X1，<a href="http://www.ckplayer.com/" target="_blank">官网</a>，<a href="http://www.ckplayer.com/manualX/" target="_blank">帮助手册</a></p>
		<p><a href="sample/index.html" target="_blank">全功能演示</a></p>
		<p><a href="sample/iframe.html" target="_blank">使用框架播放演示</p>
		<p><a href="sample/flashplayer.html" target="_blank">仅flashplayer播放演示</p>
		<p><a href="sample/h5.html" target="_blank">仅H5播放演示</p>
		<p><a href="sample/rtmp.html" target="_blank">RTMP播放示例</p>
		<p><a href="sample/ios.html" target="_blank">在各平台统一播放器风格</p>
		<p><a href="sample/dm.html" target="_blank">弹幕演示</p>
		<p><a href="sample/adother.html" target="_blank">单独配置广告，支持角标广告和片头贴片，中插，结束广告，暂停广告</p>	
		<p><a href="http://www.ckplayer.com/" target="_blank">更多演示</p>
	</body>

</html>