<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<title>ckplayer</title>
		
		<style type="text/css">body{margin:0;padding:0px;font-family:"Microsoft YaHei",<PERSON><PERSON><PERSON>,"微软雅黑",<PERSON><PERSON><PERSON><PERSON>,"黑体";font-size:14px}</style>

	</head>

	<body>
		<div id="video" style="width: 600px; height: 400px;"></div>
		<script type="text/javascript" src="../ckplayer/ckplayer.js"></script>
		<script type="text/javascript">
			var videoObject = {
				container: '#video', //容器的ID或className
				variable: 'player',//播放函数名称
				autoplay:false,
				live:true,
				video: 'rtmp://live.hkstv.hk.lxdns.com/live/hks'
			};
			var player = new ckplayer(videoObject);
		</script>
	</body>

</html>