<template>
  <div>
    <!--菜单部分-->
    <com-nav ref="comnav"></com-nav>
    <!--路由页面切换跳转部分-->
    <keep-alive>
      <!-- 子路由页面 -->
      <div class="router_content">
        <router-view />
      </div>
    </keep-alive>
  </div>
</template>

<script>
import ComNav from "./components/ComNav1.vue";

export default {
  name: "Layout",
  components: {
    ComNav,
  },
  methods: {},
};
</script>
<style>
.router_content{
  margin: 60px 0 0 0;
  background: #fdfdfd;
}
/*css主要部分的样式*/ 
/*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 6px; /*对垂直流动条有效*/
}
</style>
