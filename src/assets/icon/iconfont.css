@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?ldvugz');
  src:  url('fonts/icomoon.eot?ldvugz#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?ldvugz') format('truetype'),
    url('fonts/icomoon.woff?ldvugz') format('woff'),
    url('fonts/icomoon.svg?ldvugz#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-shoucang1:before {
  content: "\e642";
}
.icon-sel-collect:before {
  content: "\e602";
}
.icon-zhiding:before {
  content: "\e646";
}
.icon-zan:before {
  content: "\e645";
}
.icon-huifu1:before {
  content: "\e60e";
}
.icon-weixiao:before {
  content: "\e613";
}
.icon-fengefu:before {
  content: "\e6bd";
}
.icon-xiegang:before {
  content: "\e677";
}
.icon-icon-check:before {
  content: "\e6ba";
}
.icon-caogao:before {
  content: "\e68b";
}
.icon-kejian:before {
  content: "\e68f";
}
.icon-bukejian:before {
  content: "\e7cd";
}
.icon-lianjie:before {
  content: "\e61e";
}
.icon-jinghao:before {
  content: "\e600";
}
.icon-duihua:before {
  content: "\e640";
}
.icon-wenzhang2:before {
  content: "\e681";
}
.icon-tesezhuanti:before {
  content: "\e617";
}
.icon-aite:before {
  content: "\e601";
}
.icon-liebiao:before {
  content: "\e604";
}
.icon-peixun:before {
  content: "\e603";
}
