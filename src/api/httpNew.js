import axios from 'axios';

class httpNew {
  getUserFollower() {
    return axios.get("/lezhi-article-service/v1/SteUserWsg/getUserFollower");
  }
  getTopicInfo(id){
    return axios.get("/lezhi-article-service/myTopic/selectTopic/" + id);
  }
  deleteTopic(id){
    return axios.get("/lezhi-article-service/myTopic/removeTopic/" + id);
  }
  updateTopic(data){
    return axios.post("/lezhi-article-service/myTopic/updateTopic",data);
  }
  getTopicList(){
    return axios.get("/lezhi-article-service/myTopic/list");
  }
  checkTopicDelete(id){
    return axios.get("/lezhi-article-service/myTopic/checkTopicDelete/"+id);
  }
  saveSentiveWord(wordlist){
    return axios.post("/lezhi-article-service/mySentiveWord/saveSentiveWord",wordlist);
  }
  checkSentiveWord(word){
    return axios.get("/lezhi-article-service/mySentiveWord/checkSentiveWord/"+word);
  }
  getSentiveWordDetail(id){
    return axios.get("/lezhi-article-service/mySentiveWord/getSentiveWordDetail/" + id);
  }
  getSentiveWordByUserId(userId){
    return axios.get("/lezhi-article-service/mySentiveWord/getSentiveWordByUserId/"+userId);
  }
  deleteSentiveWord(id){
    return axios.get("/lezhi-article-service/mySentiveWord/deleteSentiveWord/"+id);
  }
  updateSentiveWord(data){
    return axios.post("/lezhi-article-service/mySentiveWord/updateSentiveWord",data);
  }
  getArticleAttach(articleId){
    return axios.get("/lezhi-article-service/articleAttachment/getArticleAttach/"+articleId);
  }
  deleteArticleAttach(articleId, attachId){
    return axios.get("/lezhi-article-service/articleAttachment/deleteArticleAttach/"+articleId+"/"+attachId);
  }
  getTrainApplyList(){
    return axios.get("/lezhi-article-service/myTrain/trainApplyList");
  }
  getTrainApplyUserList(){
    return axios.get("/lezhi-article-service/myTrain/getTrainApplyUserList");
  }

  addTrainApplyNotice(data){
    return axios.post("/lezhi-article-service/myTrain/trainApplyNotice",data);
  }

  getSaveResult(data){
    return axios.post("/lezhi-article-service/v1/personalReq/getSaveResult/",data);
  }

  getSaveTopic(data){
    return axios.post("/lezhi-article-service/v1/personalReq/getSaveTopic/",data);
  }

  personInfo(data){
    return axios.post("/lezhi-article-service/v1/personalReq/personInfo/",data);
  }

  showPersonInfo(data){
    return axios.post("/lezhi-article-service/v1/personalReq/showPersonInfo/",data);
  }

  showkeyWord(data){
    return axios.post("/lezhi-article-service/v1/personalReq/showkeyWord/",data);
  }

  saveTalkLog(data){
    return axios.post("/lezhi-article-service/v1/personalReq/saveTalkLog/",data);
  }

  fixtopicInfo(data){
    return axios.post("/lezhi-article-service/v1/personalReq/fixtopicInfo/",data);
  }

  updateTopicInfo(data){
    return axios.post("/lezhi-article-service/v1/personalReq/updateTopicInfo/",data);
  }

  showTopicUpdateInfo(data){
    return axios.post("/lezhi-article-service/v1/personalReq/showTopicUpdateInfo/",data);
  }

  saveLiTips(data){
    return axios.post("/lezhi-article-service/v1/personalReq/saveLiTips/",data);
  }

  getInterTips(data){
    return axios.post("/lezhi-article-service/v1/personalReq/getInterTips/",data);
  }

  showInterTips(data){
    return axios.post("/lezhi-article-service/v1/personalReq/showInterTips/",data);
  }

  saveInterAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/saveInterAdvice/",data);
  }

  showInterAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/showInterAdvice/",data);
  }

  deleteInterAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/deleteInterAdvice/",data);
  }

  judgeInterAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/judgeInterAdvice/",data);
  }

  saveDeleteAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/saveDeleteAdvice/",data);
  }

  serachUpdateAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/serachUpdateAdvice/",data);
  }

  judgeUpdateAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/judgeUpdateAdvice/",data);
  }

  updateAdviceInfo(data){
    return axios.post("/lezhi-article-service/v1/personalReq/updateAdviceInfo/",data);
  }

  saveRecordType(data){
    return axios.post("/lezhi-article-service/v1/personalReq/saveRecordType/",data);
  }

  findRecordInfo(data){
    return axios.post("/lezhi-article-service/v1/personalReq/findRecordInfo/",data);
  }

  showRecordInfo(data){
    return axios.post("/lezhi-article-service/v1/personalReq/showRecordInfo/",data);
  }

  saveTipRecordAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/saveTipRecordAdvice/",data);
  }

  updateTipRecordAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/updateTipRecordAdvice/",data);
  }

  adultRecord(data){
    return axios.post("/lezhi-article-service/v1/personalReq/adultRecord/",data);
  }

  showRecordAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/showRecordAdvice/",data);
  }

  topInfo(data){
    return axios.post("/lezhi-article-service/v1/personalReq/topInfo/",data);
  }

  cancelTop(data){
    return axios.post("/lezhi-article-service/v1/personalReq/cancelTop/",data);
  }

  saveWaitCommandTopic(data){
    return axios.post("/lezhi-article-service/v1/recommand/saveWaitCommandTopic/",data);
  }

  findCommandTopic(data){
    return axios.post("/lezhi-article-service/v1/recommand/findCommandTopic/",data);
  }

  saveTopicList(data){
    return axios.post("/lezhi-article-service/v1/recommand/saveTopicList/",data);
  }

  showCommandList(data){
    return axios.post("/lezhi-article-service/v1/recommand/showCommandList/",data);
  }


  deleteCommandTopic(data){
    return axios.post("/lezhi-article-service/v1/recommand/deleteCommandTopic/",data);
  }

  savePersonKnow(data){
    return axios.post("/lezhi-article-service/v1/personalTips/savePersonKnow/",data);
  }

  getPersonKnowResult(data){
    return axios.post("/lezhi-article-service/v1/personalTips/getPersonKnowResult/",data);
  }

  knowJudge(data){
    return axios.post("/lezhi-article-service/v1/personalTips/knowJudge/",data);
  }

  knowDelete(data){
    return axios.post("/lezhi-article-service/v1/personalTips/knowDelete/",data);
  }

  knowUpdate(data){
    return axios.post("/lezhi-article-service/v1/personalTips/knowUpdate/",data);
  }

  knowSaveDelete(data){
    return axios.post("/lezhi-article-service/v1/personalTips/knowSaveDelete/",data);
  }

  wordRules(data){
    return axios.post("/lezhi-article-service/v1/personalTips/wordRules/",data);
  }

  updateWord(data){
    return axios.post("/lezhi-article-service/v1/personalTips/updateWord/",data);
  }

  showWord(data){
    return axios.post("/lezhi-article-service/v1/personalTips/showWord/",data);
  }

  getReadNoticeConfig(){
    return axios.get("/lezhi-article-service/myManager/getReadNoticeConfig");
  }
  getTrainReadDetail(id){
    return axios.get("/lezhi-article-service/myManager/getTrainReadDetail?" + id);
  }
  saveTrainNotice(data){
    return axios.post("/lezhi-article-service/myManager/saveTrainNotice",data);
  }
  getTrainNoticeDetail(id){
    return axios.get("/lezhi-article-service/myManager/getTrainNoticeDetail/"+id);
  }
  setArticleNoticeRead(ids){
    return axios.post("/lezhi-article-service/myManager/setArticleNoticeRead",ids);
  }
  getArticleNoticeDetail(id){
    return axios.get("/lezhi-article-service/myManager/getArticleNoticeDetail/"+id);
  }
  checkArticleNoticePublish(id){
    return axios.post("/lezhi-article-service/myManager/checkArticleNoticePublish",id);
  }
  getArticleNoticeConfig(){
    return axios.get("/lezhi-article-service/myManager/getArticleNoticeConfig");
  }
  listSearchType(){
    return axios.get("/lezhi-article-service/myManager/listSearchType");
  }
  getDraftBySubject(subjectId){
    return axios.get("/lezhi-article-service/myArticle/getArticleDraftBySubject");
  }
  getTopicRecommend(queryParam){
    return axios.get('/lezhi-article-service/myTopic/getTopicRecommend', {params:queryParam});
  }
  checkTopicRecommendModify(){
    return axios.get("/lezhi-article-service/myTopic/checkTopicRecommendModify");
  }
  modifyTopicRecommend(data){
    return axios.post("/lezhi-article-service/myTopic/modifyTopicRecommend", data);
  }
  getTopicRecommSuggDetail(id){
    return axios.get('/lezhi-article-service/myTopic/getTopicRecommendDetail', {params:id});
  }
  removeTopicRecommend(params){
    return axios.post("/lezhi-article-service/myTopic/removeTopicRecommend", params);
  }
  updateTopicRecommend(data){
    return axios.post("/lezhi-article-service/myTopic/updateTopicRecommend", data);
  }
  checkTopicRecommendDelete(){
    return axios.get("/lezhi-article-service/myTopic/checkTopicRecommendDelete");
  }

  tableRecordInfo(data){
    return axios.post("/lezhi-article-service/v1/personalReq/tableRecordInfo/",data);
  }

  checkInterAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/checkInterAdvice/",data);
  }

  findAllUpdateAdvice(data){
    return axios.post("/lezhi-article-service/v1/personalReq/findAllUpdateAdvice/",data);
  }
  checkArticleEditSave(data){
    return axios.post("/lezhi-article-service/v1/personalReq/checkArticleEditSave/",data);
  }
  checkArticleMigrate(data){
    return axios.post("/lezhi-article-service/v1/personalReq/checkArticleMigrate/",data);
  }
  checkArtTipsUpdateSuccess(data){
    return axios.post("/lezhi-article-service/v1/myArticle/checkArtTipsUpdateSuccess/",data);
  }
  checkArtTipsDeleteSuccess(data){
    return axios.post("/lezhi-article-service/v1/myArticle/checkArtTipsDeleteSuccess/",data);
  }
  checkSubjectIsMigrateSuccess(data){
    return axios.get("/lezhi-article-service/v1/myArticle/checkSubjectIsMigrateSuccess/",{params:data});
  }
  deleteArticleInfo(data){
    return axios.post("/lezhi-article-service/v1/myArticle/deleteArticleInfo/",data);
  }
  updateArticleInfo(data){
    return axios.post("/lezhi-article-service/v1/myArticle/updateArticleInfo/",data);
  }

  getMyFollowInfo(id){
    return axios.get('/lezhi-article-service/myFollow/getMyFollowInfo', {params: {user:id}});
  }

  getMyFollowList(id){
    return axios.get('/lezhi-article-service/myFollow/getMyFollowList', {params: {user:id}});
  }

  getMyFollowOperationRecord(data){
    return axios.get('/lezhi-article-service/myFollow/getMyFollowOperationRecord',
      {params: {userId:data.id,pageSize:data.pageSize,pageNum:data.pageNum}});
  }

  getMyFollowStatusDetail(id){
    return axios.get('/lezhi-article-service/myFollow/getMyFollowStatusDetail', {params: {user:id}});
  }

  getMyCollectdetail(){
    return axios.get('/lezhi-article-service/myCollect/detail');
  }

  queryBaseInfo(id){
    return axios.get('/lezhi-article-service/myArtTips/queryBaseInfo', {params: {id:id}});
  }

  getRecommendInfo(data){
    return axios.get('/lezhi-article-service/myRecommend/getRecommendInfo',
      {params: {recommendType:data.recommendType,pageSize:data.pageSize,pageNum:data.pageNum}});
  }

  getRecommendTopicDetail(data){
    return axios.get('/lezhi-article-service/myRecommend/getRecommendTopicDetail',
      {params: {topicId:data.topicId}});
  }

  getRecommendArtTipsList(data){
    return axios.get('/lezhi-article-service/myRecommend/getRecommendArtTipsList',
      {params: {pageSize:data.pageSize,pageNum:data.pageNum}});
  }

  getMySubjectList(data){
    return axios.get('/lezhi-article-service/mySubject/getMySubjectList',
      {params: {pageSize:data.pageSize,pageNum:data.pageNum}});
  }

  getMySubjectManageList(data){
    return axios.get('/lezhi-article-service/mySubject/getMySubjectManageList',
      {params: {pageSize:data.pageSize,pageNum:data.pageNum}});
  }

  getMySubjectVersionDetail(data){
    return axios.get('/lezhi-article-service/mySubject/getMySubjectVersionDetail',
      {params: {id:data.id}});
  }

  removeMySubject(data){
    return axios.get('/lezhi-article-service/mySubject/removeMySubject',
      {params: {id:data.id}});
  }
  addRole(data){
    return axios.post('/lezhi-article-service/myManager/addRole', data)
  }

  editRole(data){
    return axios.post('/lezhi-article-service/myManager/editRole', data)
  }

  removeRole(id){
    return axios.post('/lezhi-article-service/myManager/removeRole?id=' + id)
  }

  getArticleList(){
    return axios.get('/lezhi-article-service/myArticle/getLatestArticle',)
  }

  cancelRecommend(id){
    return axios.get("/lezhi-article-service/myArticle/cancelRecommend/" + id,)
  }

  cancelFollow(id){
    return axios.get("/lezhi-article-service/myArticle/cancelFollow/" + id,)
  }

  setArticleTop(id){
    return axios.get("/lezhi-article-service/myArticle/setArticleTop/" + id,)
  }

  cancelCollect(id){
    return axios.get("/lezhi-article-service/myArticle/cancelCollect/" + id)
  }

  noticeRead(id){
    return axios.get("/lezhi-article-service/myTrain/noticeRead?id=" + id)
  }

  getArtTipCollectList(){
    return axios.get('/lezhi-article-service/myArtTips/getArtTipCollect', {params: {}})
  }

  getArtTipRecommendList(tipId){
    return axios.get('/lezhi-article-service/myArtTips/getArtTipRecommend', {params: {tipId:tipId}})
  }

  getArtTipsDetail(id){
    return axios.get('/lezhi-article-service/myArtTips/queryBaseInfo', {params: {id:id}})
  }
  addArtTipRecommend(data){
    return axios.post('/lezhi-article-service/myArtTips/addArtTipRecommend', data)
  }
  removeArtTipRecommend(data){
    return axios.post('/lezhi-article-service/myArtTips/removeArtTipRecommend', data)
  }
  updateArtTipRecommend(data){
    return axios.post('/lezhi-article-service/myArtTips/updateArtTipRecommend', data)
  }
  saveArtTipCollectType(data){
    return axios.post('/lezhi-article-service/myArtTips/saveArtTipCollectType', data)
  }
  addArtTipCollect(data){
    return axios.post('/lezhi-article-service/myArtTips/addArtTipCollect', data)
  }
  getTopicSuggestionList(topicId){
    return axios.get('/lezhi-article-service/myTopic/getTopicRecommend?topicId=' + topicId,)
  }
  getAnnouncementList(){
    return axios.get('/lezhi-article-service/myManager/getAnnouncementList')
  }
  getAnnouncementDetail(id){
    return axios.get('/lezhi-article-service/myManager/getAnnouncementDetail', {params: {id:id}})
  }
  saveAnnouncement(data){
    return axios.post('/lezhi-article-service/myManager/saveAnnouncement', data)
  }
  updateAnnouncement(data){
    return axios.post('/lezhi-article-service/myManager/updateAnnouncement', data)
  }
  deleteAnnouncement(id){
    return axios.post('/lezhi-article-service/myManager/deleteAnnouncement?id=' + id)
  }
  getArtTipList(data){
    return axios.get('/lezhi-article-service/myArtTips/queryArtTips', {params: {pageSize:data.pageSize,pageNum:data.pageNum,topicId:data.topicId}})
  }
  updateArtTip(data){
    return axios.post('/lezhi-article-service/myArtTips/updateArtTips', data)
  }
  deleteArtTip(id){
    return axios.post('/lezhi-article-service/myArtTips/deleteArtTips?id=' + id)
  }
  addArtTip(data){
    return axios.post('/lezhi-article-service/myArtTips/addArtTips', data)
  }
  getCommentList(queryParam){
    return axios.get("/lezhi-article-service/myComment/list",{params:queryParam});
  }
  addComment(data){
    return axios.post("/lezhi-article-service/myComment/add",data);
  }
  /*thumbComment(data){
    return axios.post("/lezhi-article-service/myComment/userThumb",data);
  }*/
  deleteComment(id){
    return axios.post("/lezhi-article-service/myComment/delete/"+id);
  }
  updateComment(data){
    return axios.put("/lezhi-article-service/myComment/update",data);
  }
  searchUser(name){
    return axios.get("/lezhi-article-service/myManager/searchUser?name="+name);
  }
  getHighManageMenu(){
    return axios.get('/lezhi-article-service/myManager/getHighManageMenu')
  }
  getHighManagePower(){
    return axios.get('/lezhi-article-service/myManager/getHighManagePower')
  }
}

export default httpNew;
