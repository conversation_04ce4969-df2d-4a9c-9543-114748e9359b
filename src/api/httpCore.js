import axios from 'axios'
import Es6Promise from 'es6-promise'
import Cookies from 'js-cookie'
import {EncryptData,DecryptData} from "@/utils/aesUtils";
Es6Promise.polyfill()
class httpCore {
	getLogin(){
		// return axios.get("/portal-service/system/v1/SysUserWsg/getLogin");
		return axios.get("/portal-service/system/v1/SteUserWsg/getLogin");
	}
	//专题类接口
	saveTheme(id,data){
		return axios.post("/lezhi-article-service/v1/artSubject/"+id,data);
	}
	queryTheme(queryParam){
		return axios.get("/lezhi-article-service/v1/artSubject",{params:queryParam});
	}
	queryThemeInfo(id){
		return axios.get("/lezhi-article-service/v1/artSubject/" + id);
	}
	deleteTheme(id){
		return axios.post("/lezhi-article-service/v1/artSubject/delete/"+id);
	}
	queryThemeTree(){
		return axios.post("/lezhi-article-service/v1/artSubject/tree");
	}
	transferArticle(desThemeId,data){
		return axios.post("/lezhi-article-service/v1/artArticle/transfer/"+desThemeId,data);
	}
	//文章相关接口
	getArticleId(){
		return axios.get("/lezhi-article-service/v1/artArticle/articleId");
	}
	getArticleContent(id){
		return axios.get("/lezhi-article-service/v1/artArticle/" + id);
	}
	publishArticle(id,data){
		return axios.post("/lezhi-article-service/v1/artArticle/publish/"+id,data);
	}
	saveArticle(id,data){
		return axios.post("/lezhi-article-service/v1/artArticle/"+id,data);
	}
	getNewCollectArticlesOfTheme(queryParam){
		return axios.get("/lezhi-article-service/v1/artArticle",{params:queryParam});
	}
	getArticleDraftList(queryParam){
		return axios.get("/lezhi-article-service/v1/artArticle/draft",{params:queryParam});
	}
	deleteDraftArticles(ids){
		return axios.post("/lezhi-article-service/v1/artArticle/delete/" + ids);
	}
	deleteArticles(ids){
		return axios.post("/lezhi-article-service/v1/artArticle/delete/" + ids);
	}
	addArticleReadRecord(data){
		return axios.post("/lezhi-article-service/v1/userReading",data);
	}

	//个人信息相关接口
	getCurrentUser(){
		return axios.get("/lezhi-article-service/v1/userCenter/currentUser");
	}
	updateUserInfo(id,data){
    let aesId = EncryptData(id);
		return axios.post("/lezhi-article-service/v1/userCenter/"+aesId.aeskey,data);
	}
	//查询人员信息
	getUserInfo(id){
		return axios.get("/lezhi-article-service/v1/userCenter/personalCenter/"+id);
	}
  //被迫加密查询人员信息
  getUserInfoAES(id){
    let aesId = EncryptData(id);
  	return axios.post("/lezhi-article-service/v1/userCenter/personalCenter",{'id':aesId.aeskey});
  }
  //获取专题文章发布权限
  getSysUser(themeId){
  	return axios.get("/lezhi-article-service/v1/artSubject/getAllUserAndPublishConfig/"+themeId);
  }
  //专题文章发布权限配置
  publishConfig(data){
  	return axios.post("/lezhi-article-service/v1/artSubject/publishConfig",data);
  }
  publishCheck(themeId){
  	return axios.get("/lezhi-article-service/v1/artSubject/publishCheck/"+themeId);
  }

	//查询我关注的专题列表
	getMyFollowThemeList(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myFollowSubject",{params:queryParam});
	}

	//附件上传
	upload(formData,config){
    return axios.post("/file-upload-service/v1/paas_attachments_tool/upload/lezhi-service",formData,config);
  }

	//关注
	follow(data){
		return axios.post("/lezhi-article-service/v1/userFollow",data);
  }
	//推荐
	recommend(data){
		return axios.post("/lezhi-article-service/v1/userRecommend",data);
  }

	//收藏
	collect(data){
		return axios.post("/lezhi-article-service/v1/userCollection",data);
  }

	//评论列表查询
	getCommentList(queryParam){
		return axios.get("/lezhi-article-service/v1/userComment",{params:queryParam});
	}
	addComment(data){
		return axios.post("/lezhi-article-service/v1/userComment",data);
  }
	thumbComment(data){
		return axios.post("/lezhi-article-service/v1/userThumb",data);
  }
	deleteComment(id){
		return axios.post("/lezhi-article-service/v1/userComment/delete/"+id);
  }
  updateComment(data){
    return axios.put("/lezhi-article-service/v1/userComment",data);
  }

	//推荐阅读
	relatedArticles(articleId){
		return axios.get("/lezhi-article-service/v1/artArticle/recommend",{params:{id:articleId}});
	}

	//获取知识id
	getKnowledgeId(){
		return axios.get("/lezhi-article-service/v1/artTips/tipsId");
	}
	//新增附件
	addAttach(data){
		return axios.post("/lezhi-article-service/v1/lzFile",data);
  }
	//删除附件
	delAttach(knowledgeId,param){
		return axios.get("/lezhi-article-service/v1/lzFile/delete/"+knowledgeId,{params:param});
  }

	//新增话题
	addTopic(data){
		return axios.post("/lezhi-article-service/v1/artTopic",data);
  }
	//热门话题
	getHotTopic(queryParam){
		return axios.get("/lezhi-article-service/v1/artTopic/hotTopic",{params:queryParam});
	}
	getTopicInfo(id){
		return axios.get("/lezhi-article-service/v1/artTopic/"+id);
	}
	getNewCollectKnowledgesOfTopic(queryParam){
		return axios.get("/lezhi-article-service/v1/artTips",{params:queryParam});
	}

	//知识新增
	addKnowledge(data){
		return axios.post("/lezhi-article-service/v1/artTips",data);
  }
	getKnowledgeContent(id){
		return axios.get("/lezhi-article-service/v1/artTips/" + id);
	}
	deleteKnowledges(ids){
		return axios.post("/lezhi-article-service/v1/artTips/delete/" + ids);
	}

	//个人中心
	getMyFollowAuthor(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myFollowAuthor",{params:queryParam});
	}
	getMyFollowTheme(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myFollowSubject",{params:queryParam});
	}
	getMyFans(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myFans",{params:queryParam});
	}
	getMyKnowledge(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myTips",{params:queryParam});
	}
	getMySubject(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/mySubject",{params:queryParam});
	}
	getMyTopic(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myTopic",{params:queryParam});
	}
	getMyArticle(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myArticle",{params:queryParam});
	}
	getMyCollectArticle(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myCollectionArticle",{params:queryParam});
	}
	getMyRecommendArticle(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myRecommendArticle",{params:queryParam});
	}
	getMyCollectKnowledge(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myCollectionTips",{params:queryParam});
	}
	getMyRecommendKnowledge(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myRecommendTips",{params:queryParam});
	}
	getMyRecommendTopic(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myRecommendTopic",{params:queryParam});
	}
	getMyModeratorSubject(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/myModeratorSubject",{params:queryParam});
	}

	//首页热门
	getHotContent(queryParam){
		return axios.get("/lezhi-article-service/v1/userCenter/hotList",{params:queryParam});
	}
	//首页置顶
	getTopContent(){
		return axios.get("/lezhi-article-service/v1/userCenter/topList");
	}
	//置顶
	setTop(data){
		return axios.post("/lezhi-article-service/v1/userTop",data);
  }
  //取消置顶
  cancelTop(data){
    return axios.post("/lezhi-article-service/v1/cancelTop",data);
  }
	//历史搜索 热门搜索列表
	getSearchList(){
		return axios.get("/lezhi-article-service/v1/userSearch/searchList");
	}
	saveSearchWord(data){
		return axios.post("/lezhi-article-service/v1/userSearch",data);
  }
	getSearchResult(queryParam){
		return axios.get("/lezhi-article-service/v1/userSearch/searchResult",{params:queryParam});
  }
	getRoleList(){
		// return axios.get("/portal-service/system/v1/SysRoleWsg/queryList");
		return axios.get("/lezhi-article-service/v1/artSubject/roleQueryList");
	}
	//专题创建权限配置
	setThemeCreatePower(ids){
		return axios.post("/lezhi-article-service/v1/artSubject/createConfig",{roleIds:ids});
	}
	getThemeCreatePower(){
		return axios.get("/lezhi-article-service/v1/artSubject/createConfig");
	}
	checkThemeCreatePower(){
		return axios.get("/lezhi-article-service/v1/artSubject/createCheck");
	}
	getKeyWords(queryParam){
		return axios.get("/lezhi-article-service/v1/artKeyword",{params:queryParam});
	}
	saveArticleKeyWord(data){
		return axios.post("/lezhi-article-service/v1/artKeyword",data);
	}
	deleteArticleKeyWord(queryParam){
		return axios.post("/lezhi-article-service/v1/artKeyword/delete?articleId="+queryParam.articleId+"&keywordId="+queryParam.keywordId);
	}
	getSearchKeywordResult(queryParam){
		return axios.get("/lezhi-article-service/v1/userSearch/searchKeywordResult",{params:queryParam});
  }
//	getDictionary(queryParam){
//		return axios.get("/lezhi-article-service/v1/...",{params:queryParam});
//}
//	setDictionary(data){
//		return axios.get("/lezhi-article-service/v1/...",data);
//}
  // portal接口,不用了，用本地的
  queryOrgList(queryParam){
		// return axios.get("/portal-service/system/v1/SysOrganizationWsg/queryOrgList",{params:queryParam});
		return axios.get("/lezhi-article-service/v1/artSubject/queryOrgList",{params:queryParam});
  }
  queryUserByOrgId(id){
		// return axios.get("/portal-service/system/v1/SysUserWsg/queryUserByOrgId?orgId="+id);
		return axios.get("/lezhi-article-service/v1/artSubject/queryUserByOrgId?orgId="+id);
  }
  queryOrgListByKeyWord(queryParam){
		// return axios.get("/portal-service/system/v1/SysOrganizationWsg/queryOrgListByKeyWord",{params:queryParam});
		return axios.get("/lezhi-article-service/v1/artSubject/queryOrgListByKeyWord",{params:queryParam});
  }
  selectById(queryParam){
		return axios.get("/portal-service/system/v1/SysOrganizationWsg/selectById",{params:queryParam});
  }
  queryOrgListById(queryParam){
		// return axios.get("/portal-service/system/v1/SysOrganizationWsg/queryOrgListById",{params:queryParam});
		return axios.get("/lezhi-article-service/v1/artSubject/queryOrgListById",{params:queryParam});
  }
  //字典查询
  queryDict(name){
  	return axios.get("/lezhi-article-service/v1/sysDict/name/"+name);
  }
  //专题迁移接口
	transferTheme(desThemeId,data){
		return axios.post("/lezhi-article-service/v1/artSubject/transfer/"+desThemeId,data);
	}
	//子专题顺序刷新
	flushSubThemeSequence(themeId){
		return axios.post("/lezhi-article-service/v1/artSubject/sequence/flush/"+themeId);
	}

	//通知类接口
	queryNotice(queryParam){
		return axios.get("/lezhi-article-service/v1/userNotice",{params:queryParam});
  }
	queryNoticeCount(){
		return axios.get("/lezhi-article-service/v1/userNotice/count");
  }
	noticeMarkRead(id){
		return axios.post("/lezhi-article-service/v1/userNotice/"+id);
	}
	addNotice(data){
		return axios.post("/lezhi-article-service/v1/userNotice",data);
	}
	//模糊查询用户列表
	queryUser(queryParam){
		return axios.get("/lezhi-article-service/v1/userNotice/user",{params:queryParam});
	}
  // =================================
  //效验专题名称是否重复
  checkSubjectRepeat(queryParam){
    return axios.get("/lezhi-article-service/mySubject/checkSubject",{params:queryParam});
  }
  //保存外链分享信息
  saveOuterShareInfo(data){
    return axios.post("/lezhi-article-service/myArticle/saveOuterShareInfo",data);
  }
  //查询专题角色
  getSubjectRole(){
    return axios.get("/lezhi-article-service/mySubject/getSubjectRole");
  }
  //角色名是否重复
  checkRoleRepeat(queryParam){
    return axios.get("/lezhi-article-service/myManager/checkRoleRepeat",queryParam);
  }
  //查询用户信息
  getUserInfoById(id){
    return axios.get("/lezhi-article-service/myself/info",{params:{id:id}});
  }
  //我关注的作者列表
  getMyFollowAuthorList(queryParam){
    return axios.get("/lezhi-article-service/myFollow/getMyFollowAuthorList",{params:queryParam});
  }
  //我的粉丝列表
  getMyFanList(queryParam){
    return axios.get("/lezhi-article-service/myFollow/getMyFanList",{params:queryParam});
  }
  //保存文章及专题关联关系
  saveArticleSubject(data){
    return axios.post("/lezhi-article-service/myArticle/saveArticleSubject",data);
  }
  //文章导航
  getArticleNav(id){
    return axios.get("/lezhi-article-service/myArticle/getArticleNav",{params:id});
  }
  //设置文章已读
  setArticleRead(id){
    return axios.post("/lezhi-article-service/myArticle/setArticleRead",{id:id});
  }
  //热门文章列表
  getHotArticleList(queryParam){
    return axios.get("/lezhi-article-service/v1/artArticle/hot",{params:queryParam});
  }
  //子专题新增
  addSubTheme(data){
    return axios.post("/lezhi-article-service/v1/artSubject/insertClassify",data);
  }
  //子专题删除
  deleteSubTheme(id){
    return axios.post("/lezhi-article-service/v1/artSubject/deleteClassify/"+id);
  }
}
axios.interceptors.request.use(
	function (config) {
    // 时间戳
		if (config.method === 'get') {
	    config.params = {
	      t: Date.parse(new Date()) / 1000,
	      ...config.params
	    }
	  }
    if(Cookies.get('token')==null){
      config.headers = {
        'X-Auth-Token': Cookies.get('acctoken')
        // 'X-Auth-Token': localStore.cookiesGet('token')
      }
    }else{
      config.headers = {
        'X-Auth-Token': Cookies.get('token')
        // 'X-Auth-Token': localStore.cookiesGet('token')
      }
    }
    return config
	}
)
export default httpCore
