@mixin wh($w, $h) {
    width: $w + px;
    height: $h + px;
}

@mixin dib_wh($w, $h) {
    display: inline-block;
    width: $w + px;
    height: $h + px;
    vertical-align: middle;
}

// @mixin pre_wh($w, $h) {
//     width: $w + '%';
//     height: $h + '%';
// }

@mixin flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}

@mixin flex-1 {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}

@mixin flex-2 {
    -webkit-box-flex: 2;
    -webkit-flex: 2;
    flex: 2;
}

@mixin flex-3 {
    -webkit-box-flex: 3;
    -webkit-flex: 3;
    flex: 3;
}

@mixin flex-vertical-center {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
}

@mixin flex-align-center {
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

@mixin flex-space-between {
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}

@mixin flex-space-around {
    -webkit-box-pack: justify;
    -webkit-justify-content: space-around;
    justify-content: space-around;
}

@mixin text-overflow {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

@mixin text-overflow-two {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}