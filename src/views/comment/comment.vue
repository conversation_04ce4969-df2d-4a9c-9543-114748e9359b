<template>
	<div class="comment_content">
	  <div class="comment">
	    <img
	    	v-if="userForm.headImg"
	      :src="userForm.headImg"
	      alt=""
	      :onerror="errorImg"
	    />
	    <div :id="'top_editer_'+id" spellcheck="false" class="edit-area" contenteditable="true" @paste="textInit"></div>
	    <div class="emoji-box" @click="editAreaId='top_editer_'+id"><emoji-icon class="vm pointer" :id="'emojiIcon1_'+id" style="margin-bottom:-2px;" @select="selectIcon" :iconConfig="iconConfig"></emoji-icon></div>
	    <el-button type="primary" @click="addComment('top_editer_'+id,null,'')">发布</el-button>
	  </div>
	  <div class="comment_all" v-show="commentList.length>0">
	    <i class="iconfont icon-fengefu"></i>全部评论<span class="mgl-5"
	      >{{commentNum}}</span
	    >
	  </div>
	  <div class="comment_box">
	    <div v-for="(item, index) in commentList" :key="index">
	      <div class="comment_parent">
	        <img
	        	v-if="item.headImg"
	          :src="item.headImg"
	          alt=""
	          :onerror="errorImg"
	        />
	        <div class="right_comment">
	          <div class="name">
	            <span class="user">{{item.nickName}}<span class="role" v-if="item.accountId==author">作者</span></span>
	            <span>{{item.commentTime}}</span>
	          </div>
	          <!-- <div class="comment_container" v-html="item.commentContent"></div> -->
	          <div class="comment_container" v-dompurify-html="item.commentContent"></div>
	          <div id="comment_num">
              <span class="mgr-15 pointer" @click="thumbComment(item.id,index,'')"><i class="iconfont icon-zan" :style="item.thumbFlag?'color:#3E8CFF':''"></i><span class="fs-12 mgl-2">{{item.likeCount}}</span></span>
              <span class="mgr-15 pointer" @click="replyIndex=index;"><i style="vertical-align:-10%;" class="iconfont icon-huifu1"></i><span class="fs-12 mgl-2">回复</span></span>
              <span class="pointer hide_area" v-if="currentUserId==item.accountId" @click="deleteComment(item.id,index,'');"><i class="fs-14 el-icon-delete-solid"></i><span class="fs-12 mgl-2">删除</span></span>
            </div>
            <div class="sub-comment" v-if="replyIndex==index">
					    <span class="reply-obj"><span class="mgr-2">回复</span>{{item.name}}：</span><div :id="'editer_'+id+'_'+index" spellcheck="false" class="edit-area" contenteditable="true" @paste="textInit"></div>
					    <div class="emoji-box" @click="editAreaId='editer_'+id+'_'+index"><emoji-icon class="vm pointer" :id="'emojiIcon2_'+id" style="margin-bottom:-2px;" @select="selectIcon" :iconConfig="iconConfig"></emoji-icon></div>
					    <el-button type="primary" @click="addComment('editer_'+id+'_'+index,item.id,item.accountId,index)">发布</el-button>
            </div>
	        </div>
	      </div>
	      <div class="comment_children">
	        <div v-for="(item1, index1) in item.children" :key="index1">
	          <img
	          	v-if="item1.headImg"
	            :src="item1.headImg"
	            alt=""
	            :onerror="errorImg"
	          />
	          <div class="right_comment">
	            <div class="name">
	              <span class="user"
	                >{{item1.nickName}}<span class="role" v-if="item1.accountId==author">作者</span
	                ><span class="fs-12 pdl-5 pdr-5" style="color: #3e8cff;"
	                  >回复</span
	                >{{item1.replyNickName}}<span class="role" v-if="item1.replyAccountId==author">作者</span
	                ></span
	              ><span>{{item1.commentTime}}</span>
	            </div>
	            <!-- <div class="comment_container" v-html="item1.commentContent"> -->
	            <div class="comment_container" v-dompurify-html="item1.commentContent">
	            </div>
	            <div id="comment_num">
	              <span class="mgr-15 pointer" @click="thumbComment(item1.id,index,index1+'')"><i class="iconfont icon-zan" :style="item1.thumbFlag?'color:#3E8CFF':''"></i><span class="fs-13 mgl-2">{{item1.likeCount}}</span></span>
	              <span class="mgr-15 pointer" @click="replyIndex=index+'_'+index1;"><i style="vertical-align:-10%;" class="iconfont icon-huifu1"></i><span class="fs-13 mgl-2">回复</span></span>
	              <span class="pointer hide_area" v-if="currentUserId==item1.accountId" @click="deleteComment(item1.id,index,index1+'');"><i class="fs-14 el-icon-delete-solid"></i><span class="fs-13 mgl-2">删除</span></span>
	              <!--<span class="pointer hide_area" v-if="currentUserId==item1.accountId" @click="updateComment(item1.id);"><i class="fs-14 el-icon-delete-solid"></i><span class="fs-13 mgl-2">修改</span></span>-->
	            </div>
	            <div class="sub-comment" v-if="replyIndex==index+'_'+index1">
						    <span class="reply-obj"><span class="mgr-2">回复</span>{{item1.nameA}}：</span><div :id="'editer_'+id+'_'+index+'_'+index1" spellcheck="false" class="edit-area" contenteditable="true" @paste="textInit"></div>
						    <div class="emoji-box" @click="editAreaId='editer_'+id+'_'+index+'_'+index1"><emoji-icon class="vm pointer" :id="'emojiIcon2_'+id" style="margin-bottom:-2px;" @select="selectIcon" :iconConfig="iconConfig"></emoji-icon></div>
						    <el-button type="primary" @click="addComment('editer_'+id+'_'+index+'_'+index1,item.id,item1.accountId,index)">发布</el-button>
	            </div>
	          </div>
	        </div>
	      </div>
	    </div>
	  	<div v-show="totalPage>1&&queryParam.pageNum!=totalPage&&!loading" class="mgt-10 fs-13" style="opacity: 0.5;">
      	<span class="pointer" @click="loadMore">
		      <span>查看更多</span>
	      </span>
	    </div>
	    <div v-show="loading" class="mg-15" style="height:80px" v-loading="loading" element-loading-text="数据正在加载中"></div>
	  </div>
	</div>
</template>

<script>
import httpCore from "@/api/httpCore";
const api = new httpCore();
import httpNew from "@/api/httpNew";
const apiNew = new httpNew();
import errorPic from "@/assets/image/errorPic.png";
export default {
  name: "comment",
  props:['type','id','author'],
  data() {
    return{
    	userForm:{},
    	commentContent:'',
    	commentNum:0,
    	commentList:[],
    	iconConfig: {
    		placement: 'bottom',
		    size: '18px',
		    name: 'chart1-icon',
		    color: '#606166'
		  },
		  replyIndex:null,
		  editAreaId:'top_editer_'+this.id,
		  queryParam:{
      	pageNum:1,
    		pageSize:5,
     },
     totalPage:0,
     loading:true,
     errorImg: 'this.src="' + errorPic + '"',
     currentUserId:this.$store.state.loginInfo.userId,
    };
  },
  mounted(){
  	this.getMyInfo();
  	this.getCommentInfo();
  },

  methods: {
  	getMyInfo:function(){
  		if(this.currentUserId){
  			api.getCurrentUser().then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.userForm = res.data;
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
  		}
  	},
  	getCommentInfo:function(){
  		this.loading=true;
  		var data = this.queryParam;
  		data.commentId=this.id;
      // apiNew.getCommentList(data).then(response => {
  		api.getCommentList(data).then(response => {
    		var res = response.data;
    		this.loading=false;
				if(res.status == "0"){
					this.commentList = this.commentList.concat(res.data.list);
					this.totalPage = res.data.pages;
					this.commentNum = res.data.total;
					this.$emit('updateCommentCount',this.commentNum);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	_insertHtml:function(str){
  		let id = this.editAreaId;
			let acitveId = document.activeElement.id;
			var selection= window.getSelection ? window.getSelection() : document.selection;
			if(id != acitveId){
				var range = document.createRange();
				range.selectNodeContents(document.getElementById(this.editAreaId));
				range.collapse(false);
				selection.removeAllRanges();
				selection.addRange(range);
			}
  		document.getElementById(this.editAreaId).focus();
			var range= selection.createRange ? selection.createRange() : selection.getRangeAt(0);
			if (!window.getSelection){
				range.pasteHTML(str);
				range.collapse(false);
				range.select();
			}else{
				range.collapse(false);
				var hasR = range.createContextualFragment(str);
				var hasR_lastChild = hasR.lastChild;
				while (hasR_lastChild && hasR_lastChild.nodeName.toLowerCase() == "br" && hasR_lastChild.previousSibling && hasR_lastChild.previousSibling.nodeName.toLowerCase() == "br") {
					var e = hasR_lastChild;
					hasR_lastChild = hasR_lastChild.previousSibling;
					hasR.removeChild(e);
				}
				range.insertNode(hasR);
				if (hasR_lastChild) {
					range.setEndAfter(hasR_lastChild);
					range.setStartAfter(hasR_lastChild);
				}
				selection.removeAllRanges();
				selection.addRange(range);
			}
		},
		/*文本复制格式化*/
    textInit:function(e) {
      e.preventDefault();
      var text;
      var clp = (e.originalEvent || e).clipboardData;
      if (clp === undefined || clp === null) {
        text = window.clipboardData.getData("text") || "";
        if (text !== "") {
          if (window.getSelection) {
            var newNode = document.createElement("span");
            newNode.innerHTML = text;
            window.getSelection().getRangeAt(0).insertNode(newNode);
          } else {
            document.selection.createRange().pasteHTML(text);
          }
        }
      } else {
        text = clp.getData('text/plain') || "";
        if (text !== "") {
          document.execCommand('insertText', false, text);
        }
      }
    },
  	selectIcon(img){
  		img = img.replace(">"," style='vertical-align:text-top;'>")
      this._insertHtml("<span>"+img+"</span>");
  	},
  	toFocus(){
  		document.getElementById("top_editer_"+this.id).focus();
  	},
  	addComment(textId,parentId,replyAccountId,index){
  		var data={
  			replyAccountId:replyAccountId,
  			parentId:parentId,
  			commentContent:document.getElementById(textId).innerHTML,
  			commentType:'',
  			commentId:this.id
  		};
  		// apiNew.addComment(data).then(response => {
  		api.addComment(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					if(parentId){
						this.commentList[index].children.unshift(res.data);
						this.replyIndex = null;
					}else{
						this.queryParam.pageNum=1;
						this.commentList=[];
						this.getCommentInfo();
					}
					document.getElementById(textId).innerHTML="";
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	loadMore:function(){
  		if(this.queryParam.pageNum<this.totalPage){
    		this.queryParam.pageNum++;
    		this.getCommentInfo();
    	}
	  },
	  thumbComment:function(id,index,index1){
	  	var data={
	  		thumbId:id,
	  		thumbType:""
	  	};
	  	api.thumbComment(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					var obj;
 					if(index1){
						obj = this.commentList[index].children[index1];
					}else{
						obj = this.commentList[index];
					}
					obj.thumbFlag = !obj.thumbFlag;
					obj.thumbFlag?obj.likeCount++:obj.likeCount--;
					this.$message({message: res.msg ,type: 'success'});
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
	  },
	  deleteComment(id,index,index1){
	  	this.$confirm('确认删除评论?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // apiNew.deleteComment(id).then(response => {
        api.deleteComment(id).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$message({message: '评论删除成功' ,type: 'success'});
						if(index1){
							this.commentList[index].children.splice(index1,1);
						}else{
							this.queryParam.pageNum=1;
							this.commentList=[];
							this.getCommentInfo();
						}
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
      });
	  },
    updateComment(id, textId){
      this.$confirm('确认修改评论?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var data={
          commentContent:document.getElementById(textId).innerHTML,
          commentType:'',
          commentId:this.id
        };
        api.updateComment(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$message({message: '评论修改成功' ,type: 'success'});
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
      });
    }
  }
}
</script>

<style>
	.emoji-box{
		height: 34px;
    line-height: 34px;
    width: 40px;
    background: #F3F3F3;
    border: 1px solid #c0c4cc;
    border-left: none;
    text-align: center;
	}
	.chart1-icon{
	  width:14px;
	  height:14px;
	  background: url(../../assets/image/chart1.png) no-repeat;
	  background-size: 100% 100%;
	}
</style>
