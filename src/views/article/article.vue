<template>
	<div v-if="content">
		<!-- <div v-html="content" v-clampy="4"></div> -->
		<div v-dompurify-html="content" v-clampy="4"></div>
		<div class="mgt-15 fs-12" style="cursor: text;" v-if="contentObj.articleAttach.length>0" @click.stop="">
			<div class="color-9"><i class="mgr-5 el-icon-paperclip"></i>附件信息：</div>
  		<div v-for="(att,index) in contentObj.articleAttach" class="att-block">
        <a :href="att.path">{{att.name}}</a>
      </div>
    </div>
	</div>
</template>

<script>
	export default{
		props:['contentObj'],
		data(){
      return{
      	content:''
      }
    },
		mounted(){
			var exp = /<img src=\"[^\"]*\"/g;
			var arr = this.contentObj.content.match(exp);
			var contentTemp = this.contentObj.content;
			if(arr && arr.length>0){
				arr.forEach(function(item){
					contentTemp = contentTemp.replace(item.substr(0,item.length-1),item.substr(0,item.length-1)+"&t="+new Date().getTime())
				})
			}
			this.content=contentTemp.substring(0,10000);
		}
	}
</script>

<style>
</style>