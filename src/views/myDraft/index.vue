<template>
	<div class="home_content article-contain">
		<div class="article_infor pdl-20">
			<div class="tl mgt-40 mgb-30">
				<span class="fs-18 fwb">我的草稿</span>
				<span v-if="themeId" class="color-9">（专题名称：{{themeName}}）</span>
				<el-button class="fr fs-12" type="text" :disabled="!isChecked" @click="deleteDraft">删除所选草稿</el-button>
			</div>
			<div class="article pdt-10 pdb-10" @click="toEditArticle(contentObj.id)" v-for="(contentObj, index) in myDraftArticles" :key="index">
	      <div class="title">
	      	<el-checkbox @click.stop.native="" class="mgr-5" v-model="contentObj.isChecked"></el-checkbox>
	      	<div style="cursor:text" @click.stop="" v-if="contentObj.subjectSequence" class="theme-classify mgr-10" 
  					:class="'theme-classify-'+contentObj.subjectSequence%5" >{{ contentObj.subjectName }}</div>
	      	{{ contentObj.title }}
	      	<span class="color-9 fs-12 fwn fr">提交时间：{{contentObj.cteTime}}</span>
	      </div>
	      <div class="content">
	      	<articleComponent :contentObj="contentObj"></articleComponent>
	      </div>
	    </div>
	    <div v-show="totalPage>1&&queryParam.pageNum!=totalPage" class="mgt-10 fs-13" style="opacity: 0.5;">
      	<span class="pointer" @click="loadMore">
		      <span>查看更多</span>
	      </span>
	    </div>
		  <div style="margin-top:150px" v-if="!loading&&myDraftArticles.length==0">
      	<img src="../../assets/image/empty.png" alt="" />
      	<div>暂无足迹~</div>
      </div>
	    <div class="mgt-50" v-loading="loading" element-loading-text="数据正在加载中"></div>
	  </div>
  </div>
</template>

<script>
	import articleComponent from '../article/article';
	import httpCore from "@/api/httpCore";
	const api = new httpCore();
	export default {
    data(){
      return{
      	themeId:this.$route.query.themeId,
      	themeName:this.$route.query.themeName,
      	loading:true,
	      myDraftArticles:[],
	      checked:false,
	      queryParam:{
	      	pageNum:1,
      		pageSize:8,
	      },
	      totalPage:0,
      }
    },
    components:{articleComponent},
    mounted(){
	    this.getMyDraftArticles();
	  },
	  computed:{
		  isChecked: function () {
	      return this.myDraftArticles.some(function(val){
	 				return val.isChecked;
	 			});
	    }
	  },
    methods:{
    	getMyDraftArticles(){
	    	this.loading=true;
	    	var data = this.queryParam;
	    	data.subjectId = this.themeId;
	  		api.getArticleDraftList(data).then(response => {
	    		var res = response.data;
	    		this.loading=false;
					if(res.status == "0"){
						var myDraftArticles = res.data.list;
						myDraftArticles.forEach(function(item){
			  			item.content = item.content.replace(/style=\"[^\"]*\"/g, "");
							item.isChecked=false;
			  		});
			  		this.myDraftArticles = this.myDraftArticles.concat(myDraftArticles);
						this.totalPage = res.data.pages;
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	deleteDraft:function(){
	 			this.$confirm('此操作将永久删除草稿, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
        	var ids = "";
		  		this.myDraftArticles.forEach(function(val){
		 				if(val.isChecked){
		 					ids+=(ids==""?val.id:","+val.id);
		 				}
		 			});
          api.deleteDraftArticles(ids).then(response => {
		    		var res = response.data;
						if(res.status == "0"){
							this.myDraftArticles = [];
							this.getMyDraftArticles();
							this.$message({type: 'success',message: '删除成功'});
						}else {
							this.$message({message: res.msg ,type: 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
        });
	  	},
	  	loadMore:function(){
	  		if(this.queryParam.pageNum<this.totalPage){
	    		this.queryParam.pageNum++;
	    		this.getMyDraftArticles();
	    	}
		  },
		  toEditArticle:function(articleId){
		  	this.$router.push({ path: "/writeArticle" , query: { articleId: articleId }});
		  }
    }
  }
</script>

<style>
</style>