<template>
  <div class="topic_content article-contain">
    <div class="nav">
      <div class="left_nav">
        <div>话题</div>
        <div class="title">#{{topicInfo.topic}}#</div>
      </div>
    </div>
    <div class="left_topic">
      <div class="topic_title">
        <div class="user_infor">
          <div class="image" style="border:none;">
            <div class="content">#</div>
            <img src="../../assets/image/talk.png" alt="" />
          </div>
          <div class="user">
            <div class="name" :title="topicInfo.topic">#{{topicInfo.topic}}#</div>
            <div class="sign"><span class="mgr-5">阅读{{topicInfo.readingCount}}</span>·<span class="mgl-5">讨论{{topicInfo.discussCount}}</span></div>
          </div>
        </div>
        <div class="follow pointer" :class="topicInfo.recommendFlag?'already_follow':''" @click="recommend">{{topicInfo.recommendFlag?'已推荐':'推荐'}}</div>
      </div>
      <div class="publish_topic">
      	<img v-if="myInfo.headImg" :src="myInfo.headImg" alt="" :onerror="errorImg"/>
        <knowledgePublish :inTopic="inTopic" :topicInfo="topicInfo" @refereshPage="refereshPage"></knowledgePublish>
      </div>
      <div class="articel_title">
	    	<el-tabs v-model="activeName" @tab-click="handleClick">
	        <el-tab-pane label="最新收录" name="newest" >
	        </el-tab-pane>
	        <el-tab-pane label="热门" name="hot">
	        </el-tab-pane>
	      </el-tabs>
	      <newCollectKnowledges ref="newCollectKnowledges" v-show="activeName=='newest'" :topicId="topicId"></newCollectKnowledges>
      	<hotKnowledges ref="hotKnowledges" v-if="hotLoadFlag" v-show="activeName=='hot'" :topicId="topicId"></hotKnowledges>
      </div>
    </div>
    <div class="right_topic">
      <div class="creator">
        <div class="title">创建者</div>
        <div class="image">
          <img
          	v-if="topicInfo.authorHeadImg"
            :src="topicInfo.authorHeadImg"
            @click="toPerCenter(topicInfo.author)"
            alt=""
            :onerror="errorImg"
          /><span>{{topicInfo.authorName}}</span>
        </div>
      </div>
      <div class="attend_person">
        <div class="title">话题贡献者排行</div>
        <div class="image">
          <img
          	v-if="item.headImg"
	          :src="item.headImg"
	          :title="item.nickName"
	          @click="toPerCenter(item.accountId)"
	          v-for="(item, index) in topicInfo.accountList"
	          :key="index"
	          :onerror="errorImg"
	        />
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import knowledgePublish from '../home/<USER>/knowledgePublish';
import newCollectKnowledges  from './component/newCollectKnowledges';
import hotKnowledges  from './component/hotKnowledges';
import httpCore from "@/api/httpCore";
const api = new httpCore();
import errorPic from "@/assets/image/errorPic.png";
import {EncryptData,DecryptData} from "@/utils/aesUtils";
export default {
  name: "Topic",
  components:{knowledgePublish,newCollectKnowledges,hotKnowledges},
  data() {
    return {
    	topicId:this.$route.query.id,
    	inTopic:true,
    	activeName: "newest",
    	topicInfo:{},
    	hotLoadFlag:false,
    	contributorList:[],
    	myInfo:{},
    	errorImg: 'this.src="' + errorPic + '"',
    };
  },
  mounted(){
  	this.getTopicInfo();
  	this.getMyInfo();
  },
  methods: {
  	getMyInfo:function(){
  		api.getCurrentUser().then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.myInfo = res.data;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
    getTopicInfo:function(){
    	api.getTopicInfo(this.topicId).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.topicInfo = res.data;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    handleClick:function(tab){
			if(tab.name=='hot'){
				this.hotLoadFlag=true;
			}
  	},
    recommend(){
  		var data={
  			recommendId:this.topicId,
  			recommendType:'3'
  		};
  		api.recommend(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.topicInfo.recommendFlag=!this.topicInfo.recommendFlag;
					this.topicInfo.recommendFlag?this.topicInfo.recommendCount++:this.topicInfo.recommendCount--;
					this.$message({message: res.msg ,type: 'success'});
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	refereshPage(){
			this.activeName = 'newest';
			this.$refs.newCollectKnowledges.referesh();
			this.hotLoadFlag=false;
  	},
  	toPerCenter(id) {
      // var routeUrl=this.$router.resolve({name:'perCenter',query: { id: id }});
      localStorage.setItem("forwordUser", id);
      var routeUrl=this.$router.resolve({name:'perCenter'});
      window.open(routeUrl.href, '_blank');
    }
  },
};
</script>
<style>
</style>
