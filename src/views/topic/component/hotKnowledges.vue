<template>
	<div class="article_infor">
		<div v-for="(item, index) in hotKnowledges" :key="index">
      <knowledgeComponent :contentObj="item" :sort="'_hot_'+index"></knowledgeComponent>
    </div>
    <div v-show="totalPage>1&&queryParam.pageNum!=totalPage&&!loading" class="mgt-10 fs-13" style="opacity: 0.5;">
    	<span class="pointer" @click="loadMore">
	      <span>查看更多</span>
      </span>
    </div>
	  <div style="margin-top:100px" v-if="!loading&&hotKnowledges.length==0">
    	<img src="../../../assets/image/empty.png" alt="" />
    	<div>暂无足迹~</div>
    </div>
    <div class="mgt-30" v-loading ="loading" element-loading-text = "数据正在加载中"></div>
  </div>
</template>

<script>
	import httpCore from "@/api/httpCore";
	const api = new httpCore();
	import knowledgeComponent from './knowledgeShow';
	export default {
    data(){
      return{
      	loading:true,
	      hotKnowledges:[],
	      queryParam:{
	      	pageNum:1,
      		pageSize:5,
	      },
	      totalPage:0,
      }
    },
    props:['topicId'],
    components:{knowledgeComponent},
    mounted(){
	  	this.getHotKnowledges();
	  },
    methods:{
    	getHotKnowledges(){
    		this.loading=true;
	    	var param = this.queryParam;
	    	param.topicId = this.topicId;
	  		api.getNewCollectKnowledgesOfTopic(param).then(response => {
	    		var res = response.data;
	    		this.loading=false;
					if(res.status == "0"){
						var hotKnowledges = res.data.list;
						hotKnowledges.forEach(function(item){
			  			var exp = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
		    			item.content =  item.content.replace(exp,"<a style='color:#3E8CFF;' target='_blank' href='$1'><i class='el-icon-link fs-14 mgr-2'></i>网页链接</a>"); 
			  		});
			  		this.hotKnowledges = this.hotKnowledges.concat(hotKnowledges);
						this.totalPage = res.data.pages;
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	loadMore:function(){
	  		if(this.queryParam.pageNum<this.totalPage){
	    		this.queryParam.pageNum++;
	    		this.getHotKnowledges();
	    	}
		  },
		  referesh(){
		  	this.queryParam.pageNum=1;
		  	this.hotKnowledges=[];
		  	this.getHotKnowledges();
		  }
    }
  }
</script>

<style>
</style>