<template>
  <div class="articleDetail_content">
    <div class="detail_left">
      <div class="article_container">
        <div class="article_title">{{articleInfo.title}}</div>
        <div class="article_author">
          <div class="author_infor">
            <div class="image">
              <img
              	v-if="articleInfo.authorHeadImg"
                :src="articleInfo.authorHeadImg"
                :onerror="errorImg"
              />
            </div>
            <div class="infor">
              <div class="name">{{articleInfo.authorName}}</div>
              <div class="time">
                <span>{{authorInfo.deptName}}</span>
                <span>{{articleInfo.cteTime}}</span>
              </div>
            </div>
          </div>
          <div v-if="currentUserId!=articleInfo.author" :class="articleInfo.authorFollowFlag?'already-follow':'follow'" >
            <el-button @click="follow">{{articleInfo.authorFollowFlag?'已关注':'关注'}}</el-button>
          </div>
        </div>
        <!-- <div class="article" v-html="articleInfo.content"> -->
        <div class="article" v-dompurify-html="articleInfo.content">
        </div>
        <div class="mgt-10" v-if="articleInfo.articleAttach&&articleInfo.articleAttach.length>0">
					<div class="color-9"><i class="mgr-5 el-icon-paperclip"></i>附件信息：</div>
		  		<div v-for="(att,index) in articleInfo.articleAttach" class="att-block">
		        <a :href="att.path">{{att.name}}</a>
		      </div>
		    </div>
		    <div class="article-keywords mgt-10" v-if="keyWords.length>0">
		  		<div class="color-9"><i class="mgr-5 el-icon-key"></i>关键字：</div>
		  		<div v-for="(keyWord,index) in keyWords" class="keyword">
		        {{keyWord.keyword}}
		      </div>
		    </div>
		    <div class="color-9 mgt-20" v-if="articleInfo.articleType=='2'">转自：{{articleInfo.reprintSource}}</div>
        <div class="infor" id="commentArea">
          <span class="pointer" @click="comment()"><img src="../../assets/image/inner2.png" alt="" />{{articleInfo.commentCount}}评论</span>
          <span class="pointer" @click="recommend()"><img :src="require(`../../assets/image/recommend_${articleInfo.recommendFlag}.png`)" alt="" />{{articleInfo.recommendCount}}推荐</span>
          <span class="pointer" @click="collect()"><img :src="require(`../../assets/image/collect_${articleInfo.collectionFlag}.png`)" alt="" />{{articleInfo.collectionCount}}收藏</span>
          <span class="pointer" v-if='highManagePower' @click="top()"><i class="iconfont icon-zhiding"></i>{{articleInfo.topCount}}置顶</span>
          <span class="pointer" @click="noticeArea.visible=!noticeArea.visible;"><img :src="require(`../../assets/image/notice3.png`)" alt="" />通知</span>
        	<div @click="requestTrain" class="train-request pointer"><img src="../../assets/image/train-request.png" style="width:20px"/>培训申请</div>
        </div>
        <div class="notice-area" v-show="noticeArea.visible">
	      	<div class="title"><i class="iconfont icon-fengefu"></i><span>文章通知</span></div>
	      	<el-form ref="form" :model="noticeArea.data" label-width="90px">
					  <el-form-item label="发送方式：">
					    @提醒
					  </el-form-item>
					  <el-form-item label="发送给TA：">
					    <el-select
						    v-model="noticeArea.data.noticeIds"
						    multiple
						    filterable
    						remote
    						popper-class="noticeUserPopper"
						    placeholder="请选择通知人"
						    :remote-method="queryUser"
    						:loading="noticeArea.loading">
						    <el-option
						      v-for="item in noticeArea.userList"
						      :key="item.id"
						      :label="item.nickName"
						      :value="item.id">
						      <div>
						      	<img class="avatar" v-if="item.headImg" :src="item.headImg" alt="" :onerror="errorImg"/>
						      	<span>{{item.nickName}}</span>
						      	<span class="fr">{{item.account}}</span>
						      </div>
						    </el-option>
						  </el-select>
					  </el-form-item>
					  <el-form-item label="发送内容：">
					    <el-input type="textarea" v-model="noticeArea.data.noticeContent"></el-input>
					  </el-form-item>
					</el-form>
					<div class="tc"><el-button size="mini" type="primary" @click="sendAiteInfo">发送</el-button></div>
	      </div>
      </div>
      <div class="author_detail">
        <div class="author_avatar">
          <img
          	v-if="articleInfo.authorHeadImg"
            :src="articleInfo.authorHeadImg"
            alt=""
            :onerror="errorImg"
          />
        </div>
        <div class="author_right">
          <div class="infor_top">
            <div class="top_left">
              <div class="name">{{articleInfo.authorName}}</div>
              <div class="company">{{authorInfo.deptName}}</div>
            </div>
            <div v-if="currentUserId!=articleInfo.author" :class="articleInfo.authorFollowFlag?'top_right1':'top_right'">
              <el-button @click="follow">{{articleInfo.authorFollowFlag?'已关注':'关注'}}</el-button>
            </div>
          </div>
          <div class="infor_bottom">
            <span class="mgr-5">发布了{{authorInfo.articleCount}}篇长文章</span
            >·<span class="mgl-5 mgr-5">{{authorInfo.subjectCount}}个专题</span>·<span class="mgl-5 mgr-5">{{authorInfo.topicCount}}条话题</span>·<span class="mgl-5">{{authorInfo.fansCount}}人关注</span>
          </div>
        </div>
      </div>
      <commentComponent ref="comment" :type="'article'" :id="id" :author="articleInfo.author" @updateCommentCount="updateCommentCount"></commentComponent>
    </div>
    <div class="detail_right" id="fixed" :style="rightBarFixed?'position:sticky;right:0;top:' + offsetTop + 'px':''">
      <div class="image"><img src="../../assets/image/10.png" alt="" /></div>
      <div class="recommend_read">
        <div class="title"><i class="iconfont icon-fengefu"></i>推荐阅读</div>
        <div class="title_list" v-for="(item, index) in relatedArticles" :key="index">
          <div class="title_detail pointer" @click="toArticleDetail(item.id)">{{ item.title }}</div>
          <span class="num"
            >阅读<span>{{ item.readCount }}</span></span
          >
        </div>
      </div>
      <div class="picture">
        <img src="../../assets/image/11.png" alt="" />
      </div>
      <div class="nav">
        <div class="title"><i class="iconfont icon-fengefu"></i>文章导航</div>
        <div class="title_nav">
          <!-- <div v-html="articleNav" @click="markCheckedNav"> -->
          <div v-dompurify-html="articleNav" @click="markCheckedNav">
          </div>
        </div>
      </div>
    </div>
    <div class="fixed_content">
      <div class="pointer" @click="recommend()">
        <div><i class="iconfont icon-zan" :style="articleInfo.recommendFlag?'color:#3E8CFF':''"></i></div>
        <span><span>{{articleInfo.recommendCount}}</span>推荐</span>
      </div>
      <div class="pointer" @click="collect()">
        <div><i class="iconfont icon-shoucang1" :style="articleInfo.collectionFlag?'color:#3E8CFF':''"></i></div>
        <span><span>{{articleInfo.collectionCount}}</span>收藏</span>
      </div>
      <div class="pointer" @click="toTop">
        <div><i class="iconfont icon-zhiding"></i></div>
        <span>置顶</span>
      </div>
      <template v-if='highManagePower'>
        <el-popover
          placement="left"
          width="220"
          :append-to-body="false"
          trigger="hover">
          <div>
            <div class="inline-block vm"><span class="fs-12">快来扫一扫吧~</span></div>
            <div class="inline-block vm mgl-5" id="qrcode" ref="qrcode"></div>
          </div>
          <div class="pointer mgt-10" slot="reference">
            <div><img style="width:20px;padding:10px 1px 0 0" src="../../assets/image/qrcode.png" alt="" /></div>
            <span>分享</span>
          </div>
        </el-popover>
      </template>
    </div>
    <template v-if='highManagePower'>
      <div class="qrcode">
        <div id="qrcode" ref="qrcode"></div>
      </div>
      <el-dialog
        width="190px"
        title="扫一扫"
        @close="closeQrcode"
        class="common-dialog"
        :visible.sync="qrcodeVisible"
        append-to-body>
        <div class="">
          <div id="qrcode" ref="qrcode"></div>
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script>
import commentComponent from '../comment/comment';
import httpCore from "@/api/httpCore";
const api = new httpCore();
import httpNew from "@/api/httpNew";
const apiNew = new httpNew();
import errorPic from "@/assets/image/errorPic.png";
import QRCode from 'qrcodejs2'
export default {
  name: "articleDetail",
  components:{commentComponent},
  data() {
    return {
    	id:this.$route.query.id,
    	goComment:this.$route.query.goComment,
    	articleInfo:{
    		recommendFlag:false,
    		collectionFlag:false,
        topCount: 0
    	},
    	authorInfo:{},
      relatedArticles: [],
      commentList: [],
      currentPage:1,
			pageLimit:10,
			articleNav:[],
			index:1,
			rightBarFixed:false,
			currentUserId:this.$store.state.loginInfo.userId,
			keyWords:[],
			errorImg: 'this.src="' + errorPic + '"',
			qrcodeVisible:false,
			qrcode: window.location.href,
			noticeArea:{
				visible:false,
				data:{
					noticeType:'aite',
					noticeIds:'',
					noticeContent:''
				},
				userList:[],
				loading:false
			},
      highManagePower: false
    };
  },
  watch: {
		'$route' (to, from) { //监听路由是否变化
			if(to.query.id != from.query.id){
			  this.id = to.query.id;
				this.getArticleDetailInfo();
  			this.getRelatedArticles();
			}
		}
	},
  mounted(){
  	this.getArticleDetailInfo();
  	this.getArticleKeywords();
  	this.addArticleReadRecord();
  	this.getRelatedArticles();
    // this.handleGetArticleNav()
    this.handleGetHighManagePower()
  },
  methods: {
  	getArticleDetailInfo:function(){
  		api.getArticleContent(this.id).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.articleInfo = res.data;
					this.$nextTick(()=>{
						this.createMenu();
						window.addEventListener('scroll',this.handleScroll);
					});
					this.getAuthorInfo(this.articleInfo.author);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getAuthorInfo:function(authorId){
  		api.getUserInfoAES(authorId).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.authorInfo = res.data;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getRelatedArticles:function(){
  		api.relatedArticles(this.id).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.relatedArticles = res.data;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
    handleSizeChange(val) {
      this.pageLimit=val;
    },
    handleCurrentChange(val) {
      this.currentPage=val;
    },
    createMenu:function() {
    	// 首先获取所有H标签，若页面中有h4，h5，h6则可以添加到参数中
      var headList = [...document.querySelectorAll('h1,h2,h3')]
      if(headList.length>0){
      	// 将H标签构造成一棵树，就可以明确H标签之间的层级
	      var root = {
	        children: []
	      }
	      var h = {
	        node: headList[0],
	        children: [],
	        parent: root
	      }
	      root.children.push(h)
	      headList.reduce(function (pre, cur) {
	        var n = {
	          node: cur,
	          children: []
	        }
	        while (h.node.localName[1] - n.node.localName[1] !== -1) {
	          h = h.parent
	          if (h === root) {
	            break
	          }
	        }
	        n.parent = h
	        h.children.push(n)
	        h = n
	        return h
	      })
	      this.articleNav = this.createList(root.children,0);
      }else{
      	this.articleNav = "";
      }
    },
    createList:function(list,label) {
    	var that = this;
      var text = list.reduce(function (pre, cur) {
        var childText = '';　　　　　　// 判断该H标签有没有子层H标签
        if (cur.children.length>0) {
          childText = that.createList(cur.children,label+1)
        }
        cur.node.id = 'header' + that.index++;
        pre += `<li style="margin-left:${label*20}px">
          <a href="#${cur.node.id}" style="position:relative;" class="pdl-20">
            <i id="${cur.node.id}_anchor" class="iconfont icon-xiegang nav_i" style="color: #3e8cff;display:none;"></i>
            ${cur.node.innerHTML}
          </a>
          ${childText}
          </li>`;
        return pre;
      },'');
      var text = `<ul> ${text} </ul>`
      return text;
    },
    markCheckedNav:function(event){
		  if (event.target.nodeName == 'A') {
		  	Array.from(document.getElementsByClassName('nav_i')).forEach(function(item){
		  		item.style.display='none';
		  	});
		    event.target.firstElementChild.style.display='inline';
		  } else if(event.target.parentNode.nodeName == 'A' && event.target.nodeName!="I"){
		  	Array.from(document.getElementsByClassName('nav_i')).forEach(function(item){
		  		item.style.display='none';
		  	});
		  	event.target.previousElementSibling.style.display='inline';
		  }
	  },
	  handleScroll(){
	  	var headList = [...document.querySelectorAll('h1,h2,h3')];
	  	if(headList.length>0){
	  		headList.forEach(function(item,index){
		  		var anchorOffset = item.offsetTop - 80;
		  		var scrollTop = document.documentElement.scrollTop;
		  		if(scrollTop >= anchorOffset){
		  			Array.from(document.getElementsByClassName('nav_i')).forEach(function(item1){
				  		item1.style.display='none';
				  	});
				  	document.getElementById(item.id+"_anchor").style.display='inline';
		  		}
		  	});
	  	}
	  	var offsetTop = document.querySelector('#fixed').offsetHeight+60-document.documentElement.clientHeight;
	  	this.offsetTop = offsetTop<0?60:60-offsetTop;
      var scrollTop = document.documentElement.scrollTop;
      scrollTop >= offsetTop ? this.rightBarFixed = true : this.rightBarFixed = false;
	  },
	  toTop:function(){
	  	window.scrollTo({
		    left: 0,
		    top: 0,
		    behavior: 'smooth'
			})
	  },
	  recommend(){
  		var data={
  			recommendId:this.id,
  			recommendType:'2'
  		};
  		api.recommend(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.articleInfo.recommendFlag=!this.articleInfo.recommendFlag;
					this.articleInfo.recommendFlag?this.articleInfo.recommendCount++:this.articleInfo.recommendCount--;
					this.$message({message: res.msg ,type: 'success'});
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	collect(){
  		var data={
  			collectionId:this.id,
  			collectionType:'2'
  		};
  		api.collect(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.articleInfo.collectionFlag=!this.articleInfo.collectionFlag;
					this.articleInfo.collectionFlag?this.articleInfo.collectionCount++:this.articleInfo.collectionCount--;
					this.$message({message: res.msg ,type: 'success'});
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	follow:function(){
  		var data={
				followId:this.articleInfo.author,
				followType:'1'
			};
    	api.follow(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.articleInfo.authorFollowFlag=!this.articleInfo.authorFollowFlag;
      		this.$message.success(res.msg);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	comment(){
  		this.$refs.comment.toFocus();
  	},
    top(){

    },
  	toArticleDetail(id){
  		var routeUrl=this.$router.resolve({name:'articleDetail',query: { id: id}});
  		window.open(routeUrl.href, '_blank');
  	},
  	updateCommentCount(num){
  		this.articleInfo.commentCount=num;
  	},
  	addArticleReadRecord(){
  		var data={
  			readingId:this.id,
  			readingType:'2'
  		};
  		api.addArticleReadRecord(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					//无处理
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getArticleKeywords(){
  		api.getKeyWords({articleId:this.id}).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.keyWords=res.data.list;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
		// 展示二维码
	  showQrcode () {
	    this.qrcodeVisible = true;
	    // 使用$nextTick确保数据渲染
	    this.$nextTick(() => {
	      this.createQrcode();
	    })
	  },
	  // 生成二维码
	  createQrcode () {
	    this.qr = new QRCode('qrcode', {
	      width: 100,
	      height: 100, // 高度
	      text: this.qrcode // 二维码内容
	    })
      // this.saveOuterShareInfo();
	  },
	  requestTrain(){
  		var data={trainingId:this.id,title:this.articleInfo.title,type:"2"};
	  	api.addNotice({noticeType:'2',noticeContent: JSON.stringify(data)}).then(response => {
	  		var res = response.data;
				if(res.status == "0"){
					if(res.data=='1'){
						this.$message({'message': '您已申请过，无法重复申请' ,'type': 'warning'});
					}else{
						this.$message({'message': '申请成功' ,'type': 'success'});
					}
				}else {
					this.$message({'message': res.msg ,'type': 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	queryUser:function(query){
  		if (query !== '') {
        this.noticeArea.loading = true;
        api.queryUser({searchWord:query}).then(response => {
		  		var res = response.data;
					if(res.status == "0"){
						this.noticeArea.loading = false;
	          this.noticeArea.userList = res.data;
					}else {
						this.$message({'message': res.msg ,'type': 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
      } else {
        this.noticeArea.userList = [];
      }
  	},
  	sendAiteInfo:function(){
  		if(this.noticeArea.data.noticeIds.length<=0){
  			this.$message({'message': '请选择通知人' ,'type': 'warning'});
  			return;
  		}else if(!this.noticeArea.data.noticeContent.trim()){
  			this.$message({'message': '通知内容不能为空' ,'type': 'warning'});
  			return;
  		}
  		var ids = "";
  		this.noticeArea.data.noticeIds.forEach(function(item){
  			ids+=(ids==""?item:","+item);
  		});
    	var data={
    		articleId:this.id,
    		articleTitle:this.articleInfo.title,
    		noticeIds:ids,
    		content:this.noticeArea.data.noticeContent
    	};
    	api.addNotice({noticeType:'1',noticeContent: JSON.stringify(data)}).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.$message({'message': '发送成功' ,'type': 'success'});
					this.noticeArea.data={};
				}else {
					this.$message({'message': res.msg ,'type': 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
	  // 关闭弹框,清除已经生成的二维码
	  closeQrcode () {
	    this.$refs.qrcode.innerHTML = '';
	  },
    // 保存外链分享信息
    saveOuterShareInfo(){
      api.saveOuterShareInfo({
        userId: this.$store.state.loginInfo.userId,
        outerShareUrl: this.articleInfo.outerLink
      }).then(response => {
        var res = response.data;
        if (res.status == "0") {
          this.$message.success(res.msg);
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    handleGetArticleNav(){
      api.getArticleNav(this.id).then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.articleNav = res.data;
        }
      })
    },
    handleGetHighManagePower(){
      apiNew.getHighManagePower().then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.highManagePower = res.data;
          if (this.highManagePower){
            this.$nextTick(()=>{
              this.createQrcode();
              if(this.goComment){
                document.getElementById("commentArea").scrollIntoView();
                this.$refs.comment.toFocus();
              }
            });
          }
        }else {
          this.$message({message: res.msg ,type: 'error'});
        }
      }).catch(err => {
        console.log(err);
      });
    },
  },
};
</script>
<style>
.nav_i{
	position:absolute;
	width:20px;
	left:0px;
}
.qrcode{
	position:fixed;
	right:10px;
	bottom:10px;
}
</style>
