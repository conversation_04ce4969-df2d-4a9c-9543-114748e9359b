import httpCore from "@/api/httpCore";
const api = new httpCore();

/* 富文本编辑图片上传配置 */
const uploadConfig = {
  name: 'img', // 必填参数 文件的参数名
  accept: 'image/png, image/gif, image/jpeg, image/bmp, image/x-icon' // 可选 可上传的图片格式
}

// toolbar工具栏的工具选项（默认展示全部）
const toolOptions = [
    ['bold', 'italic', 'underline'],  // toggled buttons
    ['blockquote', 'code-block'],
    [{'header': 1}, {'header': 2}],   // custom button values
    [{'list': 'ordered'}, {'list': 'bullet'}],
    //[{'script': 'sub'}, {'script': 'super'}],  // superscript/subscript
    [{'indent': '-1'}, {'indent': '+1'}],   // outdent/indent
    //[{'direction': 'rtl'}],      // text direction
    [{'size': ['small', false, 'large', 'huge']}], // custom dropdown
    [{'header': [1, 2, 3, 4, 5, 6, false]}],
  
    [{'color': []}, {'background': []}],  // dropdown with defaults from theme
    //[{'font': []}],
    [{'align': []}],
    ['link', 'image', 'video'],
    ['clean'],    // remove formatting button
];
const handlers = {
  image: function image () {
    var self = this
    var fileInput = this.container.querySelector('input.ql-image[type=file]')
    if (fileInput === null) {
      fileInput = document.createElement('input')
      fileInput.setAttribute('type', 'file')
      // 设置图片参数名
      if (uploadConfig.name) {
        fileInput.setAttribute('name', uploadConfig.name)
      }
      // 可设置上传图片的格式
      fileInput.setAttribute('accept', uploadConfig.accept)
      fileInput.classList.add('ql-image')
      // 监听选择文件
      fileInput.addEventListener('change', async () => {
        const formData = new FormData()
        let config = {headers:{"Content-Type":"multipart/form-data"}};
        formData.append('file', fileInput.files[0])
        let res = await api.upload(formData,config)
        let length = 0
        length = self.quill.getSelection(true).index
        self.quill.insertEmbed(length, 'image', "/file-upload-service/v1/paas_attachments_tool/download?id="+res.data.data)
        self.quill.setSelection(length + 1)
        fileInput.value = ''
      })
      this.container.appendChild(fileInput)
    }
    fileInput.click()
  }
}

export default {
  placeholder: '',
  theme: 'snow', // 主题
  modules: {
    toolbar: {
      container: toolOptions, // 工具栏选项
      handlers: handlers // 事件重写
    }
  }
}

