<template>
  <div class="setUser editor">
  	<div class="pdt-20 tl" v-if="themeId">
  		<div class="theme-classify"
  			:class="'theme-classify-'+index%5"
  			@click="chooseClassify=item;chooseClassify.index=index%5;"
  			v-for="(item,index) in themeClassify">{{item.name}}
  		</div>
  	</div>
  	<div class="tl pdt-20">
  		<label class="color-9">文章类型：</label>
  		<el-select size="small" v-model="articleType" placeholder="请选择" @change="reprintSource=''">
		    <el-option label="原创" value="1"></el-option>
		    <el-option label="转载" value="2"></el-option>
		  </el-select>
  	</div>
  	<div class="tl mgt-20" v-if="articleType=='2'">
  		<label class="color-9"><font color="red">*&nbsp;</font>转载出处：</label>
  		<el-input class="w-400" size='small' v-model="reprintSource" placeholder="请输入内容"></el-input>
  	</div>
    <div class="editor_title">
    	<div v-if="chooseClassify.id" class="theme-classify tl" :class="'theme-classify-'+chooseClassify.index">{{chooseClassify.name}}</div>
      <label class="color-9" style="margin-right: 8.5px;"><span style="color: red;">*</span> 标 题 ： </label>
      <!-- <el-input spellcheck="false" placeholder="添加标题（50字以内）" v-model="title" maxlength="50" > -->
      <el-input spellcheck="false" placeholder="（50字以内）" v-model="title" maxlength="50" >
      </el-input>
      <span style="margin-top: 29px;float:right">
      	<span class="pointer color-9 fs-13" @click="toMyDraft" title="打开草稿列表"><i class="iconfont icon-caogao mgr-5"></i><span>草稿</span></span>
			</span>
    </div>
    <div id="editor_container" spellcheck="false" class="container">
    	<!--<VueUeditorWrap
      	v-model="content"
	      :config="editorConfig"
	    />-->
      <!--<quill-editor
      	spellcheck="false"
        ref="myTextEditor"
        v-model="content"
        :options="editorOption"
        @change="onEditorChange($event)"
      ></quill-editor>-->
    </div>
    <div class="nav">写文章</div>
    <div class="footer mgb-20">
      <div class="left_footer">
        <span>当前字数</span><span>{{ wordNum }}</span>
      </div>
      <div class="right_footer">
        <div class="left">
        	<uploaderChunk
			    	:type="'2'"
			    	:tipsId="articleId"
			    	@handleAttachSuccess="handleAttachSuccess"
			    	@changeAttachProgress="changeAttachProgress"
			    ></uploaderChunk>
		    	<span>|</span>
		    	<span class="pointer" @click="submit('save')">保存</span>
      		<span class="mgl-10"><button class="submit-button" @click="submit('publish')">发布</button></span>
        </div>
      </div>
    </div>
    <div class="tl" :class="attaches.length>0?'mgb-20':''">
    	<template v-if="attaches.length>0">
    		<span class="color-9">已上传附件：</span>
	  		<div v-for="(att,index) in attaches" class="att-block">
	        <a :href="att.path">{{att.name}}</a><i class="el-icon-circle-close mgl-5" @click.stop="removeAttach(att,index)"></i>
	      </div>
	      <div v-for="(att,index) in uploadingAttach" class="att-block" v-if="!att.completed">
	      	<span><i class="el-icon-loading mgr-5"></i><span class="color-9 fs-10">上传中&nbsp;{{att.uploadProgress}}</span></span>
	      </div>
    	</template>
    </div>
    <div class="article-keywords">
  		<span class="color-9">关键字：</span>
  		<div v-for="(keyWord,index) in keyWords" class="keyword">
        {{keyWord.keyword}}<i class="mgl-10 color-9 el-icon-circle-close" @click="deleteArticleKeyWord(keyWord.id,index)"></i>
      </div>
      <div class="new-keyword" @click="openNewKeywordDialog">新增关键字</div>
    </div>
    <el-dialog class="common-dialog" title="新增关键字" :visible.sync="keywordAddDialog.visible" width="500px">
    	<div style="height:100px;" class="pdt-20">
    		<span class="color-9 fs-13">关键字：</span>
    		<el-autocomplete
	    		style="width:250px;"
	    		v-model="keywordAddDialog.keyword"
		      :fetch-suggestions="fetchSuggestions"
		      placeholder="请输入关键字"
		    >
		    	<template slot-scope="{ item }">
				    <span>{{ item.keyword }}</span>
				  </template>
				</el-autocomplete>
				<button class="mgl-20 submit-button" @click="saveKeyword">添加</button>
		  </div>
    </el-dialog>
    <el-upload
    	style="display:none;"
    	accept=".mpg,.m4v,.mp4,.flv,.3gp,.mov,.avi,.rmvb,.mkv,.wmv"
    	:show-file-list="false"
     	:action="uploadPath"
     	:on-success="handleVideoSuccess">
     	<el-button size="mini" id="audioUploadButton" type="primary"></el-button>
  	</el-upload>
  	<el-dialog :visible.sync="dialogVisible" width="700px">
    	<div style="width:100%;height:400px;" class="tc">
        <videoComponent style="width:100%;height:100%;" :id="'video'" :url="dialogUrl"></videoComponent>
    	</div>
    </el-dialog>
  </div>
</template>

<script>
import E from 'wangeditor'
import httpCore from "@/api/httpCore";
const api = new httpCore();
import uploaderChunk from '../uploader/uploader-chunk';
export default {
  name: "editor",
  data: function () {
    return {
    	articleId:"",
    	editor:null,
      title: "",
      content: "",
      wordNum: 0,
      themeId:this.$route.query.themeId,
      attaches:[],
      uploadPath:'/file-upload-service/v1/paas_attachments_tool/upload/lezhi-service',
		  downloadPath:'/file-upload-service/v1/paas_attachments_tool/download?id=',
		  themeClassify:[],
		  chooseClassify:{
		  	index:null
		  },
		  dialogUrl: "",
      dialogVisible: false,
      keyWords:[],
      keywordAddDialog:{
      	visible:false,
      	keyword:'',
      	allKeyword:[]
      },
      tipPromise:Promise.resolve(),
      uploadingAttach:[],
      articleType:"1",
      reprintSource:''
    };
  },
  components: {
//  VueUeditorWrap,
		uploaderChunk
  },
  mounted(){
  	this.ininEditor();
  	if(this.themeId){
  		this.getThemeClassify();
  	}
  	if(this.$route.query.articleId){
  		this.articleId = this.$route.query.articleId;
  		this.getArticleContent();
  	}else{
  		this.getArticleId();
  	}
  },
	beforeDestroy() {
	    this.editor.destroy();
	    this.editor = null ;
	},
  methods: {
  	ininEditor(){
  		this.editor = new E('#editor_container');
      this.editor.config.placeholder = '';
      this.editor.config.zIndex = 500;
      this.editor.config.height = 380;
      this.editor.config.showLinkImg = false;
      this.editor.config.pasteFilterStyle = true;
  		this.editor.config.excludeMenus = [
	      'emoticon',
	      'todo',
	      'video'
    	];
    	this.editor.config.pasteIgnoreImg = true;
      //配置 自定义处理粘贴的文本内容
      this.editor.config.pasteTextHandle = function (pasteStr) {
      	var newStr = pasteStr.replace(/@font-face{[^>]*div.Section0{page:Section0;}/g, "");
      	return newStr
      };
    	var that = this;
    	this.editor.config.onchange = function (newHtml) {
    		that.content = newHtml;
//      获取纯文字 在获取img 相加就可以了
        var temp = that.content.replace(/<\/?.+?>/g, "");
        var result = temp.replace(/ /g, ""); //result为获取冲文字得到后的内容
//         获取img标签
        var re = /<img[^>]+>/g;
        var ImgA = that.content.match(re);
        let imgLen;
        if(ImgA){
          imgLen = ImgA.length
        }
        if(!ImgA){
          imgLen = 0
        }
        that.wordNum = imgLen + result.length;
    	};
	    this.editor.config.customUploadImg = function (resultFiles, insertImgFn) {
			  // 上传图片，返回结果，将图片插入到编辑器中
			  const formData = new FormData()
        let config = {headers:{"Content-Type":"multipart/form-data"}};
        formData.append('file', resultFiles[0])
			  api.upload(formData,config).then(response => {
	    		var res = response.data;
					if(res.code == "SUCCESS"){
						insertImgFn('/file-upload-service/v1/paas_attachments_tool/download?id=' + res.data);
					}else {
						that.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
			};

    	this.editor.create();
//  	this.editor.$toolbarElem.elems[0].childNodes[16].onclick=()=>{ //childNodes 跟随着菜单栏变化
//   		var e = document.createEvent("MouseEvents");
//	    	e.initEvent("click", true, true);
//   		document.getElementById("audioUploadButton").dispatchEvent(e);
//		}
  	},
  	getArticleId:function(){
  		api.getArticleId().then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.articleId = res.data;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getArticleContent:function(){
  		api.getArticleContent(this.articleId).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.title = res.data.title;
					this.content = res.data.content;
					this.editor.txt.html(this.content);
					this.attaches = res.data.articleAttach;
					this.getArticleKeyWords();
					this.articleType = res.data.articleType;
					this.reprintSource = res.data.reprintSource;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
    onEditorChange(event) {
      this.wordNum = event.quill.getLength() - 1;
    },
    validate:function(){
    	var result = false;
    	if(this.title.trim()==''){
      	this.$message({message:"文章标题不能为空",type:'warning'});
      }else if(this.wordNum==0){
      	this.$message({message:"文章内容不能为空",type:'warning'});
      }else if(this.articleType=='2'&&!this.reprintSource){
      	this.$message({message:"转载来源不能为空",type:'warning'});
      }else{
      	result = true;
      }
      return result;
    },
    submit(type) {
    	if(this.validate()){
    		var content = this.editor.txt.html().replace(/<o:p>/ig,"");
    		content = content.replace(/<\/o:p>/ig,"");
    		content = content.replace(/<br\s*\/?>\s*<\/p\s*>/ig,"</p>");
    		content = content.replace(/<\s*img/ig,"<img");
				var reg = /<([a-z]+?)(?:\s+?[^>]*?)?>\s*?<\/\1>/ig;
				while (reg.test(content)) {
				  content = content.replace(reg,"");
				}
				var reg1 = /<table.*?>[\s\S]*?<\/table>/ig;
				if (reg1.test(content)) {
					content = content.replace(reg1, function(val, replacement) {
						var res = val.replace(/<p>/ig,"");
						return res = res.replace(/<\/p>/ig,"");
					});
				}
    		var data={
      		title:this.title,
      		content:content,
      		subjectIds:this.chooseClassify.id?this.chooseClassify.id:this.themeId,
      		articleType:this.articleType,
      		reprintSource:this.reprintSource
      	};
    		if(type=='publish'){
    			api.publishArticle(this.articleId,data).then(response => {
		    		var res = response.data;
						if(res.status == "0"){
							this.$message({message: '发布成功' ,type: 'success'});
							if(this.themeId){
								this.addNotice();
							}
							this.$router.go(-1);
						}else {
							this.$message({'message': res.msg ,'type': 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
    		}else if(type=='save'){
          // this.handleSaveArticleSubject()
    			api.saveArticle(this.articleId,data).then(response => {
		    		var res = response.data;
						if(res.status == "0"){
							this.$message({message: '保存成功' ,type: 'success'});
						}else {
							this.$message({'message': res.msg ,'type': 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
    		}
    	}
    },
    addNotice(){
    	var data={
    		articleId:this.articleId,
    		subjectId:this.chooseClassify.id?this.chooseClassify.id:'',
    		articleTitle:this.title,
    		parentSubjectId:this.themeId,
    		subjectName:this.chooseClassify.name?this.chooseClassify.name:''
    	};
    	api.addNotice({noticeType:'3',noticeContent: JSON.stringify(data)}).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					//无处理
				}else {
					this.$message({'message': res.msg ,'type': 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    toMyDraft(){
			var routeUrl=this.$router.resolve({name:'myDraft'});
      window.open(routeUrl.href, '_blank');
		},
  	removeAttach(file,index){
			api.delAttach(file.tipsId,{id:file.id,type:'5'}).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.attaches.splice(index,1);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
		},
		getThemeClassify:function(){
			api.queryThemeInfo(this.themeId).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.themeClassify=res.data.children;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
		},
		handleVideoSuccess(res, file){
			var path=this.downloadPath+res.data;
			this.editor.cmd.do('insertHTML', `<div contenteditable="false" style="position:relative;display:inline-block;"><video src="${path}"></video><i class="article-video-play" onclick="alert(11);playAudio(${path})"></i></div>`)
		},
		playAudio(url){
  		this.dialogUrl=url;
  		this.dialogVisible = true;
  		this.dialogType="audio";
  	},
  	openNewKeywordDialog(){
  		this.keywordAddDialog.visible=true;
      this.keywordAddDialog.keyword='';
      api.getKeyWords().then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.keywordAddDialog.allKeyword = res.data.list.map(function(item){
						item.value = item.keyword;
						return item;
					})
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	fetchSuggestions(queryString, cb) {
      var allKeyword = this.keywordAddDialog.allKeyword;
      var results = queryString ? allKeyword.filter((item) => {
          return (item.keyword.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
        }) : allKeyword;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    saveKeyword(){
    	if(this.keywordAddDialog.keyword.trim()){
    		var data={
	    		articleId:this.articleId,
	    		keyword:this.keywordAddDialog.keyword
	    	};
	    	api.saveArticleKeyWord(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.getArticleKeyWords();
						this.$message({message: '关键字添加成功' ,type: 'success'});
						this.keywordAddDialog.visible=false;
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	}else{
    		this.$message({message: '关键字不能为空' ,type: 'warning'});
    	}
    },
    getArticleKeyWords(){
    	api.getKeyWords({articleId:this.articleId}).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.keyWords=res.data.list;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    deleteArticleKeyWord(keywordId,index){
    	var param={
    		articleId:this.articleId,
	    	keywordId:keywordId
    	}
    	api.deleteArticleKeyWord(param).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.keyWords.splice(index,1);
					this.$message({message: '关键字删除成功' ,type: 'success'});
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    changeAttachProgress(obj){
			var num = this.uploadingAttach.filter(item => item.uniqueIdentifier==obj.uniqueIdentifier).length;
			if(num==0){
				this.uploadingAttach.push(obj);
			}else{
				this.uploadingAttach.forEach(function(item){
					if(item.uniqueIdentifier==obj.uniqueIdentifier){
						item.uploadProgress = obj.uploadProgress;
						item.completed = obj.completed;
					}
				});
			}
		},
		handleAttachSuccess(att){
			this.attaches.push(att);
  	},
    // 文章关联主题
    handleSaveArticleSubject(){
      var data={
        id: this.articleId,
        subjectIds: this.chooseClassify.id?this.chooseClassify.id:''
      };
      api.saveArticleSubject(data).then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.$message({message: '文章关联专题成功' ,type: 'success'});
        }else {
          this.$message({message: res.msg ,type: 'error'});
        }
      })
     .catch(err => {
        console.log(err);
      });
    }
  },
};
</script>
<style scoped lang="scss">
	.editor-btn {
	  margin-top: 20px;
	}
	.attch-area{
		height: 40px;
    border: 1px solid #c9d8db;
    border-top: none;
    border-bottom: none;
    padding:8px 15px;
    .left{
    	display: inline-block;
    	float: left;
    	color:#606166;
    	font-size: 13px;
    }
	}
	.cancel-button{
		width: 64px;
		height: 32px;
		background: transparent;
		border-radius: 2px;
		color: #606166;
		font-size:12px;
		border: 1px solid #c0c4cc;
		outline: none;
	}
  ::v-deep.el-input{
    height:40px ;
  }
  >>> .el-input .el-input__inner{
    height:40px !important;
    margin-top: 15px;
    border: 1px solid #DCDFE6 !important;
  }
</style>
