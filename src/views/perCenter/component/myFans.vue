<template>
	<div class="top_nav no_nav">
	  <div class="list_content">
	    <div
	      class="infor_content"
	      v-for="(item, index) in fansList"
	      :key="index"
	    >
	      <div class="left_content pointer" @click="toPerCenter(item.id)">
	        <div class="image">
	          <img v-if="item.headImg" :src="item.headImg" alt="" :onerror="errorImg"/>
	        </div>
	        <div class="follow_infor">
	          <div>{{item.nickName}}</div>
	          <div>
	            <span>{{item.followCount}}人关注</span>|<span>{{item.articleCount}}篇文章</span>|<span
	              >{{item.subjectCount}}个专题</span
	            >
	          </div>
	        </div>
	      </div>
	      <div class="right_content">
	        <el-button v-show="item.fansIsFollowed" plain class="follow_button"  @click="follow(item,index)"
	          ><i class="iconfont icon-icon-check"></i>已关注</el-button
	        >
	        <el-button v-show="!item.fansIsFollowed" plain @click="follow(item,index)"
	          ><i class="el-icon-plus"></i>关注</el-button
	        >
	      </div>
	    </div>
	    <div v-show="totalPage>1&&queryParam.pageNum!=totalPage&&!loading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
	    	<span class="pointer" @click="loadMore">
		      <span>查看更多</span>
	      </span>
	    </div>
		  <div v-if="!loading&&fansList.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
	    	<div>未查询到数据</div>
	    </div>
	    <div class="mgt-10" style="height:80px" v-loading ="loading" element-loading-text = "数据正在加载中"></div>
	  </div>
  </div>
</template>

<script>
import {Message,MessageBox} from 'element-ui';
import httpCore from "@/api/httpCore";
const api = new httpCore();
import errorPic from "@/assets/image/errorPic.png";
import {EncryptData,DecryptData} from "@/utils/aesUtils";
export default {
  props:['id'],
  data() {
    return {
      fansList: [],
      queryParam:{
      	pageNum:1,
    		pageSize:5,
    		accountId:this.id
      },
      totalPage:0,
      loading:true,
      errorImg: 'this.src="' + errorPic + '"',
    };
  },
  mounted() {
  	this.getFansList();
    // this.getMyFanList();
  },
  methods: {
  	getFansList:function(){
  		this.loading = true;
  		api.getMyFans(this.queryParam).then(response => {
    		var res = response.data;
    		this.loading=false;
				if(res.status == "0"){
		  		this.fansList = this.fansList.concat(res.data.list);
					this.totalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	follow:function(item,index){
  		var data={
				followId:item.id,
				followType:'1'
			};
    	api.follow(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.fansList[index].fansIsFollowed=!this.fansList[index].fansIsFollowed;
      		this.$message.success(res.msg);
      		this.$emit('getUserInfo');
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	loadMore(){
    	if(this.queryParam.pageNum<this.totalPage){
    		this.queryParam.pageNum++;
    		this.getFansList();
    	}
    },
    toPerCenter(id) {
    	if(id!=this.id){
    		// var routeUrl=this.$router.resolve({name:'perCenter',query: { id: id }});
        localStorage.setItem("forwordUser", id);
        var routeUrl=this.$router.resolve({name:'perCenter'});
      	window.open(routeUrl.href, '_blank');
    	}
    },
    getMyFanList(){
      api.getMyFanList(this.queryParam).then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.fansList = res.data.list;
          this.totalPage = res.data.pages;
        }else {
          this.$message({message: res.msg ,type: 'error'});
        }
      })
    }
  }
}
</script>

<style>

</style>
