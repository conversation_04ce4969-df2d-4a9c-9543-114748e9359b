<template>
	<div>
		<div v-if="contentObj.status=='1'">
			<div @click="toArticleDetail(contentObj.id)">
				<div class="title">{{ contentObj.title }}</div>
		    <div class="content">
		    	<articleComponent :contentObj="contentObj"></articleComponent>
		    </div>
	    </div>
    </div>
    <div class="tc pd-20 mgb-10 color-9" style="background: #f6f6f6;" v-else>
    	文章已删除
    </div>
    <div class="footer" v-if="type!='myArticle'||currentUserId!=forwordUser">
      <div class="user">
        <img @click="toPerCenter(contentObj.author)" v-if="contentObj.authorHeadImg" :src="contentObj.authorHeadImg" alt="" :onerror="errorImg"/><span>{{contentObj.authorName}}</span>
      </div>
      <div class="infor">
        <span v-if="contentObj.status=='1'" class="pointer" @click="goComment(contentObj.id)"><img src="../../../assets/image/inner2.png" alt="" />{{contentObj.commentCount}}评论</span>
        <span v-if="contentObj.status=='1'||type=='recommend'||type=='recommendKnowledge'" class="pointer" @click="recommend(contentObj.id)"><img :src="require(`../../../assets/image/recommend_${contentObj.recommendFlag}.png`)" alt="" />{{contentObj.recommendCount}}推荐</span>
        <span v-if="contentObj.status=='1'||type=='collect'||type=='collectKnowledge'" class="pointer" @click="collect(contentObj.id)"><img :src="require(`../../../assets/image/collect_${contentObj.collectionFlag}.png`)" alt="" />{{contentObj.collectionCount}}收藏</span>
      	<div v-if="contentObj.status=='1'" @click="requestTrain" class="train-request pointer"><img src="../../../assets/image/train-request.png" style="width:20px"/>培训申请</div>
      </div>
    </div>
    <div class="footer mgt-10 color-9" v-else>
    	<div class="infor">
	    	<span class="pointer" @click="toEditArticle(contentObj.id)"><i class="el-icon-edit mgr-5"></i>编辑</span>
	      <span class="pointer" @click="deleteArticle(contentObj.id)"><i class="el-icon-delete mgr-5"></i>删除</span>
    	</div>
    </div>
  </div>
</template>

<script>
	import httpCore from "@/api/httpCore";
	const api = new httpCore();
	import articleComponent from '../../article/article';
	import errorPic from "@/assets/image/errorPic.png";
  import {EncryptData,DecryptData} from "@/utils/aesUtils";
	export default {
    props:['contentObj','type'],
    components:{articleComponent},
    data() {
	    return {
	      currentUserId:this.$store.state.loginInfo.userId,
	      id:this.$route.query.id,
        forwordUser:localStorage.getItem('forwordUser'),
	      errorImg: 'this.src="' + errorPic + '"',
	    };
	  },
    methods:{
    	goComment(id){
  			var routeUrl=this.$router.resolve({name:'articleDetail',query: { id: id,goComment: true }});
    		window.open(routeUrl.href, '_blank');
    	},
    	toArticleDetail(id){
	  		var routeUrl=this.$router.resolve({name:'articleDetail',query: { id: id}});
	  		window.open(routeUrl.href, '_blank');
	  	},
	  	recommend(id){
    		var data={
    			recommendId:id,
    			recommendType:'2'
    		};
    		api.recommend(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$message({message: res.msg ,type: 'success'});
						this.$emit('getUserInfo');
						if(this.type=='recommend'){
							this.$emit('referesh');
						}else{
							this.contentObj.recommendFlag=!this.contentObj.recommendFlag;
							this.contentObj.recommendFlag?this.contentObj.recommendCount++:this.contentObj.recommendCount--;
						}
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	},
    	collect(id){
    		var data={
    			collectionId:id,
    			collectionType:'2'
    		};
    		api.collect(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$message({message: res.msg ,type: 'success'});
						this.$emit('getUserInfo');
						if(this.type=='collect'){
							this.$emit('referesh');
						}else{
							this.contentObj.collectionFlag=!this.contentObj.collectionFlag;
							this.contentObj.collectionFlag?this.contentObj.collectionCount++:this.contentObj.collectionCount--;
						}
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	},
    	toPerCenter(id) {
    		if(id!=this.id){
    			// var routeUrl=this.$router.resolve({name:'perCenter',query: { id: id }});
          localStorage.setItem("forwordUser", id);
          var routeUrl=this.$router.resolve({name:'perCenter'});
	      	window.open(routeUrl.href, '_blank');
    		}
	    },
	    toEditArticle:function(articleId){
		  	this.$router.push({ path: "/writeArticle" , query: { articleId: articleId }});
		  },
	    deleteArticle(id){
	    	api.deleteArticles(id).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$emit('referesh');
						this.$message({message: "文章删除成功" ,type: 'success'});
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	    },
	    requestTrain(){
	  		var data={trainingId:this.contentObj.id,title:this.contentObj.title,type:"2"};
		  	api.addNotice({noticeType:'2',noticeContent: JSON.stringify(data)}).then(response => {
		  		var res = response.data;
					if(res.status == "0"){
						if(res.data=='1'){
							this.$message({'message': '您已申请过，无法重复申请' ,'type': 'warning'});
						}else{
							this.$message({'message': '申请成功' ,'type': 'success'});
						}
					}else {
						this.$message({'message': res.msg ,'type': 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
    }
  }
</script>

<style>
</style>
