<template>
	<el-tabs v-model="activeName" class="top_nav" @tab-click="handleClick">
    <el-tab-pane label="文章" name="article">
    	<div class="article_infor pdl-20">
				<div class="article pdt-10 pdb-10" v-for="(contentObj, index) in recommendArticleList" :key="index"  v-show="activeName=='article'">
		      <articleComponent :contentObj="contentObj" :type="'recommend'" @getUserInfo="getUserInfo" @referesh="refereshArticle"></articleComponent>
		    </div>
		    <div v-show="articleTotalPage>1&&queryArticleParam.pageNum!=articleTotalPage&&!articleLoading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
		    	<span class="pointer" @click="loadMoreArticle">
			      <span>查看更多</span>
		      </span>
		    </div>
			  <div v-if="!articleLoading&&recommendArticleList.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
		    	<div>未查询到数据</div>
		    </div>
		    <div class="mgt-10" style="height:80px" v-loading ="articleLoading" element-loading-text = "数据正在加载中"></div>
		  </div>
    </el-tab-pane>
    <el-tab-pane label="话题" name="topic">
    	<div class="list_content">
        <div
          class="infor_content"
          v-for="(item, index) in recommendTopicList"
          :key="index"
        >
          <div class="left_content pointer" @click="toTopic(item.id)">
            <div class="follow_infor">
              <div>{{item.topic}}</div>
              <div>
                <span>{{item.readingCount}}次阅读</span>|<span>{{item.discussCount}}次讨论</span>
              </div>
            </div>
          </div>
          <div class="right_content" @click="cancelTopicRecommend(item)">
            <el-button plain class="recommend_button"
              ><i class="iconfont icon-icon-check"></i>已推荐</el-button
            >
          </div>
        </div>
      </div>
      <div v-show="topicTotalPage>1&&queryTopicParam.pageNum!=topicTotalPage&&!loading2" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
	    	<span class="pointer" @click="loadMoreTopic">
		      <span>查看更多</span>
	      </span>
	    </div>
		  <div v-if="!topicLoading&&recommendTopicList.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
	    	<div>未查询到数据</div>
	    </div>
	    <div class="mgt-10" style="height:80px" v-loading ="topicLoading" element-loading-text = "数据正在加载中"></div>
    </el-tab-pane>
    <el-tab-pane label="小知识" name="knowledge">
    	<div class="article_infor">
				<div v-for="(item, index) in recommendKnowledgeList" :key="index" v-if="knowledgeLoadFlag">
		      <knowledgeComponent :contentObj="item" :sort="'_recommendKnowledge_'+index" :type="'recommendKnowledge'"  @getUserInfo="getUserInfo" @referesh="refereshKnowledge"></knowledgeComponent>
		    </div>
		    <div v-show="knowledgeTotalPage>1&&queryKnowledgeParam.pageNum!=knowledgeTotalPage&&!knowledgeLoading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
		    	<span class="pointer" @click="loadMoreKnowledge">
			      <span>查看更多</span>
		      </span>
		    </div>
			  <div v-if="!knowledgeLoading&&recommendKnowledgeList.length==0" class="mg-30 fs-14 tc" style="opacity: 0.2;">
		    	<div>未查询到数据</div>
		    </div>
		    <div class="mgt-10" style="height:80px" v-loading ="knowledgeLoading" element-loading-text = "数据正在加载中"></div>
		  </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import {Message,MessageBox} from 'element-ui';
import knowledgeComponent from './knowledgeShow';
import articleComponent from './articleShow';
import httpCore from "@/api/httpCore";
const api = new httpCore();
export default {
  props:['id'],
  components:{knowledgeComponent,articleComponent},
  data() {
    return {
      activeName: "article",
      recommendArticleList: [],
      recommendTopicList: [],
      recommendKnowledgeList: [],
      articleLoading:true,
      knowledgeLoading:true,
      topicLoading:true,
      knowledgeLoadFlag:false,
      queryArticleParam:{
      	pageNum:1,
    		pageSize:5,
    		accountId:this.id
      },
      articleTotalPage:0,
      queryTopicParam:{
      	pageNum:1,
    		pageSize:5,
    		accountId:this.id
      },
      topicTotalPage:0,
      queryKnowledgeParam:{
      	pageNum:1,
    		pageSize:5,
    		accountId:this.id
      },
      knowledgeTotalPage:0,
    };
  },
  mounted() {
  	this.getRecommendArticleList();
  	this.getRecommendTopicList();
  },
  methods: {
  	getRecommendArticleList:function(){
  		this.articleLoading = true;
  		api.getMyRecommendArticle(this.queryArticleParam).then(response => {
    		var res = response.data;
    		this.articleLoading=false;
				if(res.status == "0"){
					var recommendArticleList= res.data.list;
					recommendArticleList.forEach(function(item){
		  			item.content = item.content.replace(/style=\"[^\"]*\"/g, "");
		  		});
		  		this.recommendArticleList = this.recommendArticleList.concat(recommendArticleList);
					this.articleTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getRecommendKnowledgeList:function(){
  		this.knowledgeLoading = true;
  		api.getMyRecommendKnowledge(this.queryKnowledgeParam).then(response => {
    		var res = response.data;
    		this.knowledgeLoading=false;
				if(res.status == "0"){
					var recommendKnowledgeList= res.data.list;
					recommendKnowledgeList.forEach(function(item){
		  			var exp = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
		    		item.content =  item.content.replace(exp,"<a style='color:#3E8CFF;' target='_blank' href='$1'><i class='el-icon-link fs-14 mgr-2'></i>网页链接</a>"); 
		  		});
		  		this.recommendKnowledgeList = this.recommendKnowledgeList.concat(recommendKnowledgeList);
					this.knowledgeTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getRecommendTopicList:function(){
  		this.topicLoading = true;
  		api.getMyRecommendTopic(this.queryTopicParam).then(response => {
    		var res = response.data;
    		this.topicLoading=false;
				if(res.status == "0"){
					if(res.data.list){
						this.recommendTopicList = this.recommendTopicList.concat(res.data.list);
					}
					this.topicTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	handleClick:function(tab){
			if(tab.name=='knowledge'){
				this.queryKnowledgeParam.pageNum=1;
  			this.recommendKnowledgeList=[];
				this.getRecommendKnowledgeList();
				this.knowledgeLoadFlag=true;
			}
  	},
  	loadMoreArticle(){
  		if(this.queryArticleParam.pageNum<this.articleTotalPage){
    		this.queryArticleParam.pageNum++;
    		this.getRecommendArticleList();
    	}
  	},
  	loadMoreTopic(){
  		if(this.queryTopicParam.pageNum<this.topicTotalPage){
    		this.queryTopicParam.pageNum++;
    		this.getRecommendTopicList();
    	}
  	},
  	loadMoreKnowledge(){
  		if(this.queryKnowledgeParam.pageNum<this.knowledgeTotalPage){
    		this.queryKnowledgeParam.pageNum++;
    		this.getRecommendKnowledgeList();
    	}
  	},
  	refereshArticle(){
  		this.queryArticleParam.pageNum=1;
  		this.recommendArticleList=[];
  		this.getRecommendArticleList();
  	},
  	refereshKnowledge(){
  		this.queryKnowledgeParam.pageNum=1;
  		this.recommendKnowledgeList=[];
  		this.getRecommendKnowledgeList();
  	},
  	refereshTopic(){
  		this.queryTopicParam.pageNum=1;
  		this.recommendTopicList=[];
  		this.getRecommendTopicList();
  	},
  	getUserInfo(){
  		this.$emit('getUserInfo');
  	},
  	toTopic(id) {
      var routeUrl=this.$router.resolve({name:'topic',query: { id: id }});
      window.open(routeUrl.href, '_blank');
    },
  	cancelTopicRecommend(item){
  		var data={
  			recommendId:item.id,
  			recommendType:'3'
  		};
  		api.recommend(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.refereshTopic();
					this.$message.success("已取消推荐话题"+item.topic);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	}
  }
}
</script>

<style>
	.emoji-wrap{
		left:-150px !important;
	}
</style>