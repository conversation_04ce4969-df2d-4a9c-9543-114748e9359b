<template>
	<el-tabs v-model="activeName" class="top_nav">
    <el-tab-pane label="作者" name="author">
      <div class="list_content">
        <div
          class="infor_content"
          v-for="(item, index) in followAuthorList"
          :key="index"
        >
          <div class="left_content pointer" @click="toPerCenter(item.id)">
            <div class="image">
              <img v-if="item.headImg" :src="item.headImg" alt="" :onerror="errorImg"/>
            </div>
            <div class="follow_infor">
              <div>{{item.nickName}}</div>
              <div>
                <span>{{item.followCount}}人关注</span>|<span>{{item.articleCount}}篇文章</span>|<span
                  >{{item.subjectCount}}个专题</span
                >
              </div>
            </div>
          </div>
          <div class="right_content">
            <el-button v-show="item.fansIsFollowed" plain class="follow_button"  @click="followPerson(item,index)"
		          ><i class="iconfont icon-icon-check"></i>已关注</el-button
		        >
		        <el-button v-show="!item.fansIsFollowed" plain @click="followPerson(item,index)"
		          ><i class="el-icon-plus"></i>关注</el-button
		        >
          </div>
        </div>
        <div v-show="authorTotalPage>1&&queryAuthorParam.pageNum!=authorTotalPage&&!loading1" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
		    	<span class="pointer" @click="loadMoreAuthor">
			      <span>查看更多</span>
		      </span>
		    </div>
			  <div v-if="!loading1&&followAuthorList.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
		    	<div>未查询到数据</div>
		    </div>
		    <div class="mgt-10" style="height:80px" v-loading ="loading1" element-loading-text = "数据正在加载中"></div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="专题" name="theme">
    	<div class="list_content">
        <div
          class="infor_content"
          v-for="(item, index) in followThemeList"
          :key="index"
        >
          <div class="left_content pointer" @click="toTheme(item.id)">
            <div class="image">
              <img v-if="item.cover" :src="item.cover" alt="" :onerror="errorImg"/>
            </div>
            <div class="follow_infor">
              <div>{{item.name}}</div>
              <div>
                <span>{{item.followCount}}人关注</span>|<span>{{item.articleCount}}篇文章</span>|<span
                  >{{item.articleAuthorCount}}人投稿</span
                >
              </div>
            </div>
          </div>
          <div class="right_content">
            <el-button v-show="item.followFlag" plain class="follow_button"  @click="followTheme(item,index)"
		          ><i class="iconfont icon-icon-check"></i>已关注</el-button
		        >
		        <el-button v-show="!item.followFlag" plain @click="followTheme(item,index)"
		          ><i class="el-icon-plus"></i>关注</el-button
		        >
          </div>
        </div>
      </div>
      <div v-show="themeTotalPage>1&&queryThemeParam.pageNum!=themeTotalPage&&!loading2" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
	    	<span class="pointer" @click="loadMoreTheme">
		      <span>查看更多</span>
	      </span>
	    </div>
		  <div v-if="!loading2&&followThemeList.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
	    	<div>未查询到数据</div>
	    </div>
	    <div class="mgt-10" style="height:80px" v-loading ="loading2" element-loading-text = "数据正在加载中"></div>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import httpCore from "@/api/httpCore";
const api = new httpCore();
import {Message,MessageBox} from 'element-ui';
import errorPic from "@/assets/image/errorPic.png";
import {EncryptData,DecryptData} from "@/utils/aesUtils";
export default {
  props:['id'],
  data() {
    return {
      activeName: "author",
      followAuthorList: [],
      followThemeList: [],
      queryAuthorParam:{
      	pageNum:1,
    		pageSize:5,
    		accountId:this.id
      },
      authorTotalPage:0,
      queryThemeParam:{
      	pageNum:1,
    		pageSize:5,
    		accountId:this.id
      },
      themeTotalPage:0,
      loading1:true,
      loading2:true,
      currentUserId:this.$store.state.loginInfo.userId,
      errorImg: 'this.src="' + errorPic + '"',
    };
  },
  mounted() {
  	this.getFollowAuthorList();
  	this.getFollowThemeList();
    // this.getFollowList();
  },
  methods: {
  	getFollowAuthorList:function(){
  		this.loading1 = true;
  		api.getMyFollowAuthor(this.queryAuthorParam).then(response => {
    		var res = response.data;
    		this.loading1=false;
				if(res.status == "0"){
		  		this.followAuthorList = this.followAuthorList.concat(res.data.list);
					this.authorTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getFollowThemeList:function(){
  		this.loading2 = true;
  		api.getMyFollowTheme(this.queryThemeParam).then(response => {
    		var res = response.data;
    		this.loading2=false;
				if(res.status == "0"){
		  		this.followThemeList = this.followThemeList.concat(res.data.list);
					this.themeTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	followPerson(item,index) {
      var data={
				followId:item.id,
				followType:'1'
			};
			api.follow(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					if(this.id==this.currentUserId){
						this.queryAuthorParam.pageNum=1;
						this.followAuthorList=[];
						this.getFollowAuthorList();
						this.$message.success("已取消关注"+item.nickName);
						this.$emit('getUserInfo');
					}else{
						this.followAuthorList[index].fansIsFollowed=!this.followAuthorList[index].fansIsFollowed;
      			this.$message.success(res.msg);
					}
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    followTheme(item,index) {
    	var data={
				followId:item.id,
				followType:'2'
			};
    	api.follow(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					if(this.id==this.currentUserId){
						this.queryThemeParam.pageNum=1;
						this.followThemeList=[];
						this.getFollowThemeList();
	      		this.$message.success(res.msg);
	      		this.$emit('getUserInfo');
					}else{
						this.followThemeList[index].followFlag=!this.followThemeList[index].followFlag;
      			this.$message.success(res.msg);
					}
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    loadMoreAuthor(){
    	if(this.queryAuthorParam.pageNum<this.authorTotalPage){
    		this.queryAuthorParam.pageNum++;
    		this.getFollowAuthorList();
    	}
    },
    loadMoreTheme(){
    	if(this.queryThemeParam.pageNum<this.themeTotalPage){
    		this.queryThemeParam.pageNum++;
    		this.getFollowThemeList();
    	}
    },
    toPerCenter(id) {
    	if(id!=this.id){
    		// var routeUrl=this.$router.resolve({name:'perCenter',query: { id: id }});
        localStorage.setItem("forwordUser", id);
        var routeUrl=this.$router.resolve({name:'perCenter'});
      	window.open(routeUrl.href, '_blank');
    	}
    },
    toTheme(id) {
      var routeUrl=this.$router.resolve({name:'themeInner',query: { themeId: id }});
      window.open(routeUrl.href, '_blank');
    },
    getFollowList(){
      api.getMyFollowAuthorList({accountId:this.id}).then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.followAuthorList = res.data.list;
        }else {
          this.$message({message: res.msg ,type: 'error'});
        }
      })
    }
  }
}
</script>

<style>
</style>
