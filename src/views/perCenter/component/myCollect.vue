<template>
	<el-tabs v-model="activeName" class="top_nav" @tab-click="handleClick">
    <el-tab-pane label="文章" name="article">
    	<div class="article_infor pdl-20">
				<div class="article pdt-10 pdb-10" v-for="(contentObj, index) in collectArticleList" :key="index"  v-show="activeName=='article'">
		      <articleComponent :contentObj="contentObj" :type="'collect'" @getUserInfo="getUserInfo" @referesh="refereshArticle"></articleComponent>
		    </div>
		    <div v-show="articleTotalPage>1&&queryArticleParam.pageNum!=articleTotalPage&&!articleLoading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
		    	<span class="pointer" @click="loadMoreArticle">
			      <span>查看更多</span>
		      </span>
		    </div>
			  <div v-if="!articleLoading&&collectArticleList.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
		    	<div>未查询到数据</div>
		    </div>
		    <div class="mgt-10" style="height:80px" v-loading ="articleLoading" element-loading-text = "数据正在加载中"></div>
		  </div>
    </el-tab-pane>
    <el-tab-pane label="小知识" name="knowledge">
    	<div class="article_infor">
				<div v-for="(item, index) in collectKnowledgeList" :key="index" v-if="knowledgeLoadFlag">
		      <knowledgeComponent :contentObj="item" :sort="'_collectKnowledge_'+index" :type="'collectKnowledge'"  @getUserInfo="getUserInfo" @referesh="refereshKnowledge"></knowledgeComponent>
		    </div>
		    <div v-show="knowledgeTotalPage>1&&queryKnowledgeParam.pageNum!=knowledgeTotalPage&&!knowledgeLoading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
		    	<span class="pointer" @click="loadMoreKnowledge">
			      <span>查看更多</span>
		      </span>
		    </div>
			  <div v-if="!knowledgeLoading&&collectKnowledgeList.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
		    	<div>未查询到数据</div>
		    </div>
		    <div class="mgt-10" style="height:80px" v-loading ="knowledgeLoading" element-loading-text = "数据正在加载中"></div>
		  </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import {Message,MessageBox} from 'element-ui';
import knowledgeComponent from './knowledgeShow';
import articleComponent from './articleShow';
import httpCore from "@/api/httpCore";
const api = new httpCore();
export default {
  props:['id'],
  components:{knowledgeComponent,articleComponent},
  data() {
    return {
      activeName: "article",
      collectArticleList: [],
      collectKnowledgeList: [],
      articleLoading:true,
      knowledgeLoading:true,
      knowledgeLoadFlag:false,
      queryArticleParam:{
      	pageNum:1,
    		pageSize:5,
    		accountId:this.id
      },
      articleTotalPage:0,
      queryKnowledgeParam:{
      	pageNum:1,
    		pageSize:5,
    		accountId:this.id
      },
      knowledgeTotalPage:0,
    };
  },
  mounted() {
  	this.getCollectArticleList();
  },
  methods: {
  	getCollectArticleList:function(){
  		this.articleLoading = true;
  		api.getMyCollectArticle(this.queryArticleParam).then(response => {
    		var res = response.data;
    		this.articleLoading=false;
				if(res.status == "0"){
					var collectArticleList= res.data.list;
					collectArticleList.forEach(function(item){
		  			item.content = item.content.replace(/style=\"[^\"]*\"/g, "");
		  		});
		  		this.collectArticleList = this.collectArticleList.concat(collectArticleList);
					this.articleTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getCollectKnowledgeList:function(){
  		this.knowledgeLoading = true;
  		api.getMyCollectKnowledge(this.queryKnowledgeParam).then(response => {
    		var res = response.data;
    		this.knowledgeLoading=false;
				if(res.status == "0"){
					var collectKnowledgeList= res.data.list;
					collectKnowledgeList.forEach(function(item){
		  			var exp = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
		    		item.content =  item.content.replace(exp,"<a style='color:#3E8CFF;' target='_blank' href='$1'><i class='el-icon-link fs-14 mgr-2'></i>网页链接</a>"); 
		  		});
		  		this.collectKnowledgeList = this.collectKnowledgeList.concat(collectKnowledgeList);
					this.knowledgeTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	handleClick:function(tab){
			if(tab.name=='knowledge'){
				this.queryArticleParam.pageNum=1;
				this.collectKnowledgeList=[];
				this.getCollectKnowledgeList();
				this.knowledgeLoadFlag=true;
			}
  	},
  	loadMoreArticle(){
  		if(this.queryArticleParam.pageNum<this.articleTotalPage){
    		this.queryArticleParam.pageNum++;
    		this.getCollectArticleList();
    	}
  	},
  	loadMoreKnowledge(){
  		if(this.queryKnowledgeParam.pageNum<this.knowledgeTotalPage){
    		this.queryKnowledgeParam.pageNum++;
    		this.getCollectKnowledgeList();
    	}
  	},
  	refereshArticle(){
  		this.queryArticleParam.pageNum=1;
  		this.collectArticleList=[];
  		this.getCollectArticleList();
  	},
  	refereshKnowledge(){
  		this.queryKnowledgeParam.pageNum=1;
  		this.collectKnowledgeList=[];
  		this.getCollectKnowledgeList();
  	},
  	getUserInfo(){
  		this.$emit('getUserInfo');
  	}
  }
}
</script>

<style>
	.emoji-wrap{
		left:-150px !important;
	}
</style>