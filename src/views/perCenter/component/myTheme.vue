<template>
	<div class="top_nav no_nav">
	  <div class="list_content">
	    <div
	      class="infor_content"
	      v-for="(item, index) in themeList"
	      :key="index"
	    >
	      <div class="left_content pointer" @click="toTheme(item.id)">
	        <div class="image">
	          <img v-if="item.cover" :src="item.cover" alt="" :onerror="errorImg"/>
	        </div>
	        <div class="follow_infor">
	          <div>{{item.name}}</div>
	          <div>
	            <span>{{item.followCount}}人关注</span>|<span>{{item.articleCount}}篇文章</span>|<span
	              >{{item.articleAuthorCount}}人投稿</span
	            >
	          </div>
	        </div>
	      </div>
	    </div>
	    <div v-show="totalPage>1&&queryParam.pageNum!=totalPage&&!loading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
	    	<span class="pointer" @click="loadMore">
		      <span>查看更多</span>
	      </span>
	    </div>
		  <div v-if="!loading&&themeList.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
	    	<div>未查询到数据</div>
	    </div>
	    <div class="mgt-10" style="height:80px" v-loading ="loading" element-loading-text = "数据正在加载中"></div>
	  </div>
  </div>
</template>

<script>
import {Message,MessageBox} from 'element-ui';
import httpCore from "@/api/httpCore";
const api = new httpCore();
import errorPic from "@/assets/image/errorPic.png";
export default {
  props:['id'],
  data() {
    return {
      themeList: [],
      queryParam:{
      	pageNum:1,
    		pageSize:5,
    		accountId:this.id
      },
      totalPage:0,
      loading:true,
      errorImg: 'this.src="' + errorPic + '"',
    };
  },
  mounted() {
  	this.getThemeList();
  },
  methods: {
  	getThemeList:function(){
  		this.loading = true;
  		api.getMySubject(this.queryParam).then(response => {
    		var res = response.data;
    		this.loading=false;
				if(res.status == "0"){
		  		this.themeList = this.themeList.concat(res.data.list);
					this.totalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	loadMore(){
    	if(this.queryParam.pageNum<this.totalPage){
    		this.queryParam.pageNum++;
    		this.getThemeList();
    	}
    },
    toTheme(id) {
      var routeUrl=this.$router.resolve({name:'themeInner',query: { themeId: id }});
      window.open(routeUrl.href, '_blank');
    },
  }
}
</script>

<style>

</style>