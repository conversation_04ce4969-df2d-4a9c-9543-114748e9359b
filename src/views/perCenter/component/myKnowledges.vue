<template>
	<div class="top_nav no_nav">
		<div class="article_infor">
			<div v-for="(item, index) in myKnowledges" :key="index">
	      <knowledgeComponent :contentObj="item" :sort="'_myKnowledge_'+index" :type="'myKnowledge'" :index="index" @refereshUpdateContent="refereshUpdateContent" @referesh="referesh"></knowledgeComponent>
	    </div>
	    <div v-show="totalPage>1&&queryParam.pageNum!=totalPage&&!loading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
	    	<span class="pointer" @click="loadMore">
		      <span>查看更多</span>
	      </span>
	    </div>
		  <div v-if="!loading&&myKnowledges.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
	    	<div>未查询到数据</div>
	    </div>
	    <div class="mgt-10" style="height:80px" v-loading ="loading" element-loading-text = "数据正在加载中"></div>
	  </div>
	</div>
</template>

<script>
	import knowledgeComponent from './knowledgeShow';
	import httpCore from "@/api/httpCore";
	const api = new httpCore();
	export default {
    data(){
      return{
      	loading:true,
	      myKnowledges:[],
	      queryParam:{
	      	pageNum:1,
	    		pageSize:5,
	    		accountId:this.id
	      },
	      totalPage:0,
      }
    },
    props:['id'],
    components:{knowledgeComponent},
    mounted(){
	    this.getMyKnowledges();
	  },
    methods:{
    	getMyKnowledges(){
    		this.loading = true;
	  		api.getMyKnowledge(this.queryParam).then(response => {
	    		var res = response.data;
	    		this.loading=false;
					if(res.status == "0"){
						var myKnowledges= res.data.list;
						myKnowledges.forEach(function(item){
			  			var exp = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
				    	item.content =  item.content.replace(exp,"<a style='color:#3E8CFF;' target='_blank' href='$1'><i class='el-icon-link fs-14 mgr-2'></i>网页链接</a>"); 
						});
			  		this.myKnowledges = this.myKnowledges.concat(myKnowledges);
						this.totalPage = res.data.pages;
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	loadMore(){
	    	if(this.queryParam.pageNum<this.totalPage){
	    		this.queryParam.pageNum++;
	    		this.getMyKnowledges();
	    	}
	    },
	    refereshUpdateContent(data,index){
	    	this.myKnowledges.splice(index,1,data);
	    	this.$set(this.myKnowledges[index], 'updateFlag', true);
	    },
	    referesh(){
	    	this.queryParam.pageNum=1;
	  		this.myKnowledges=[];
	  		this.getMyKnowledges();
	  		this.$emit('getUserInfo');
	    }
    }
  }
</script>

<style>
</style>