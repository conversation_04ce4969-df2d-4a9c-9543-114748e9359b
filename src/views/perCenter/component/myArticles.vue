<template>
	<div class="top_nav no_nav">
		<div class="list_content" style="position: relative;">
			<div class="nav-content" v-if="currentUserId==id">
			  <!--文章删除，迁移-->
			  <span v-if="checkedLength>0">共选{{checkedLength}}项，<span @click="deleteArticles()" class="pointer" style="color:#FF6262">删除选择项</span>&nbsp;/&nbsp;移动到
			  	<el-cascader
			  		style="width:100px"
				    v-model="desTheme"
				    :options="themeList"
				    :props="optionProps"
				    emitPath="false"
				    @change="chooseDesTheme"></el-cascader>
			  </span>
	  	</div>
			<div class="article_infor pdl-20">
				<div v-if="myArticles.length>0">
				<div class="article pdt-10 pdb-10" :class="currentUserId==id?'special-title':''" v-for="(contentObj, index) in myArticles">
		      <el-checkbox v-if="currentUserId==id" class="special-checkbox" v-model="contentObj.isChecked"></el-checkbox>
		      <articleComponent :contentObj="contentObj" :type="'myArticle'" :index="index" @referesh="referesh"></articleComponent>
		    </div>
		    <div v-show="totalPage>1&&queryParam.pageNum!=totalPage&&!loading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
		    	<span class="pointer" @click="loadMore">
			      <span>查看更多</span>
		      </span>
		    </div>
		    </div>
			  <div v-if="!loading&&myArticles.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
		    	<div>未查询到数据</div>
		    </div>
		    <div class="mgt-10" style="height:80px" v-loading ="loading" element-loading-text = "数据正在加载中"></div>
		  </div>
	  </div>
  </div>
</template>

<script>
	import articleComponent from './articleShow';
	import httpCore from "@/api/httpCore";
	const api = new httpCore();
	export default {
    data(){
      return{
      	loading:true,
	      myArticles:[],
	      queryParam:{
	      	pageNum:1,
	    		pageSize:5,
	    		accountId:this.id
	      },
	      totalPage:0,
	      desTheme:[],  //文章迁移目的专题
	      //父子专题层级关系列表
      	themeList:[],
      	optionProps:{
	      	value:'id',
	      	label:'name'
	      },
	      currentUserId:this.$store.state.loginInfo.userId,
      }
    },
    props:['id'],
    mounted(){
	    this.getMyArticles();
	    this.getThemeTree();
	  },
	  components:{articleComponent},
	  computed:{
	  	//文章check数量
		  checkedLength: function() {
	      var list = this.myArticles.filter(function(val){
	 				return val.isChecked;
	 			});
	 			return list.length;
	    },
	  },
    methods:{
    	getMyArticles(){
    		this.loading = true;
	  		api.getMyArticle(this.queryParam).then(response => {
	    		var res = response.data;
	    		this.loading=false;
					if(res.status == "0"){
						var myArticles= res.data.list;
						myArticles.forEach(function(item){
							item.isChecked=false;
			  			item.content = item.content.replace(/style=\"[^\"]*\"/g, "");
			  		});
			  		this.myArticles = this.myArticles.concat(myArticles);
						this.totalPage = res.data.pages;
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	loadMore(){
	    	if(this.queryParam.pageNum<this.totalPage){
	    		this.queryParam.pageNum++;
	    		this.getMyArticles();
	    	}
	    },
	    referesh(){
	    	this.queryParam.pageNum=1;
	  		this.myArticles=[];
	  		this.getMyArticles();
	  		this.$emit('getUserInfo');
	    },
	    //获取专题层级关系树
	  	getThemeTree(){
	  		api.queryThemeTree().then(response => {
	    		var res = response.data;
					if(res.status == "0"){
			  		this.themeList = this.clearEmptyChildren(res.data);
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	clearEmptyChildren(data) {
	      // 循环遍历json数据
	      for (var i = 0; i < data.length; i++) {
	        if (data[i].children.length < 1) {
	          // children若为空数组，则将children设为undefined
	          data[i].children = undefined;
	        } else {
	          // children若不为空数组，则继续 递归调用 本方法
	          this.clearEmptyChildren(data[i].children);
	        }
	      }
	      return data;
	    },
	    //文章迁移目的专题选择
	  	chooseDesTheme(){
	  		var ids = "";
	  		this.myArticles.forEach(function(val){
	 				if(val.isChecked){
	 					ids+=(ids==""?val.id:","+val.id);
	 				}
	 			});
	  		api.transferArticle(this.desTheme[this.desTheme.length-1],{ids:ids}).then(response => {
	    		var res = response.data;
	    		this.desTheme=[];
					if(res.status == "0"){
			  		this.$message({message: res.msg ,type: 'success'});
			  		this.queryParam.pageNum=1;
		    		this.myArticles=[];
		    		this.getMyArticles();
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	//文章删除
	  	deleteArticles(){
	  		var ids = "";
	  		this.myArticles.forEach(function(val){
	 				if(val.isChecked){
	 					ids+=(ids==""?val.id:","+val.id);
	 				}
	 			});
	 			api.deleteArticles(ids).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$message({message: res.msg ,type: 'success'});
			  		this.queryParam.pageNum=1;
		    		this.myArticles=[];
		    		this.getMyArticles();
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
    }
  }
</script>

<style lang="scss">
	.special-title{
		position: relative;
		.title{
			margin-left: 25px;
		}
		
		.special-checkbox{
			position:absolute;
			top:10px;
			left:0px;
		}
	}
</style>