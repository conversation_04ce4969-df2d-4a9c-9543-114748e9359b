<template>
	<div>
		<div class="article_content pdl-20" style="border:none;border-bottom:2px solid #f8f8f8;background:transparent;">
      <div class="top_content" v-if="type!='myKnowledge'||currentUserId!=id">
        <div class="user_infor">
          <div class="image">
            <img @click="toPerCenter(contentObj.author)" v-if="contentObj.authorHeadImg" :src="contentObj.authorHeadImg" alt="" :onerror="errorImg"/>
          </div>
          <div class="user">
	          <div class="name">{{contentObj.authorName}}</div>
	          <div class="sign">
	            <span>{{contentObj.cteTime|fomatTime()}}</span>
	            <span>来自</span>
	            <span>{{contentObj.authorDept}}</span>
	          </div>
	        </div>
        </div>
      </div>
      <div v-show="contentObj.status=='1'">
	      <div v-if="!edit" class="middle_content mgb-0">
	        <div class="knowledge_content" :class="type=='myKnowledge'&&currentUserId==id?'mgb-0':''">
			      <!-- <div :id="'content-know1'+sort" v-show="allShow" class="content" v-html="contentObj.content"></div> -->
			      <div :id="'content-know1'+sort" v-show="allShow" class="content" v-dompurify-html="contentObj.content"></div>
			    	<!-- <div :id="'content-know2'+sort" v-show="!allShow" class="content" v-html="contentObj.content" v-clampy="4"></div> -->
			    	<div :id="'content-know2'+sort" v-show="!allShow" class="content" v-dompurify-html="contentObj.content" v-clampy="4"></div>
			      <div v-show="allButtonShow" style="margin-top: 10px;overflow: hidden;">
			      	<a style="color:#3E8CFF;"  class="fr pointer mgr-10" @click="allShow=true;allButtonShow=false;">展示全文</a>
			      </div>

			      <div v-if="contentObj.tipsImages.length>0" class="picture" :style="contentObj.tipsImages.length==9?'width:80%;':(contentObj.tipsImages.length>=6?'width:70%;':'')">
			      	<div v-for="(pic,index) in contentObj.tipsImages">
			      		<el-image
						      :style="contentObj.tipsImages.length==9?'height:150px;width:150px':(contentObj.tipsImages.length>=6?'height:120px;width:120px':'height:100px;width:100px')"
						      v-if="pic.path"
						      :src="pic.path"
						      fit="cover"
						      :preview-src-list="picList">
						    </el-image>
			      	</div>
			      </div>
			      <div v-if="contentObj.tipsVideo.path">
			      	<div class="video">
			          <!--<video width="100%" height="236" controls v-if="contentObj.tipsVideo.path" :src="contentObj.tipsVideo.path" style="object-fit:fill;">
			            <source src="movie.mp4" type="video/mp4" />
			            <source src="movie.ogg" type="video/ogg" />
			              	您的浏览器不支持 HTML5 video 标签。
			          </video>-->
			          <videoComponent style="height:236px;width:100%" :id="'video_' + sort" :url="contentObj.tipsVideo.path"></videoComponent>
			        </div>
			      </div>
			      <div class="mgt-15" v-if="contentObj.tipsAttach.length>0">
							<div class="color-9"><i class="mgr-5 el-icon-paperclip"></i>附件信息：</div>
			    		<div v-for="(att,index) in contentObj.tipsAttach" class="att-block">
			          <a :href="att.path">{{att.name}}</a>
				      </div>
			      </div>
			    </div>
	      </div>
	      <div class="home_content" style="width:100%" v-else>
		      <div class="left_home_content" style="width:80%">
		      	<knowledgePublish :id="contentObj.id" @refereshPage="refereshPage"></knowledgePublish>
		      </div>
	      </div>
	    </div>
	    <div class="tc pd-20 mgb-10 color-9" style="background: #f6f6f6;" v-show="contentObj.status!='1'">
	    	知识已删除
	    </div>
      <div class="bottom_content" v-if="type!='myKnowledge'||currentUserId!=id">
      	<div class="pointer" @click="commentShow=!commentShow;" v-if="contentObj.status=='1'">
          <img src="../../../assets/image/art1.png" alt="" />{{contentObj.commentCount}}评论
        </div>
        <div v-if="contentObj.status=='1'||type=='recommend'||type=='recommendKnowledge'" class="pointer" @click="recommend(contentObj.id)"><img :src="require(`../../../assets/image/recommend_${contentObj.recommendFlag}.png`)" alt="" />{{contentObj.recommendCount}}推荐</div>
        <div v-if="contentObj.status=='1'||type=='collect'||type=='collectKnowledge'" class="pointer" @click="collect(contentObj.id)"><img :src="require(`../../../assets/image/collect_${contentObj.collectionFlag}.png`)" alt="" />{{contentObj.collectionCount}}收藏</div>
      	<div v-if="contentObj.status=='1'" @click="requestTrain" class="train-request pointer"><img src="../../../assets/image/train-request.png" style="width:20px"/>培训申请</div>
      </div>
      <div class="bottom_content mgt-10 color-9" v-else>
      	<div class="pointer" @click="toEditKnowledge(contentObj.id)"><i class="el-icon-edit mgr-5"></i>编辑</div>
        <div class="pointer" @click="deleteKnowledge(contentObj.id)"><i class="el-icon-delete mgr-5"></i>删除</div>
      </div>
      <commentComponent v-if="commentShow" :type="'knowledge'" :id="contentObj.id" :author="contentObj.author" @updateCommentCount="updateCommentCount"></commentComponent>
    </div>
  </div>
</template>

<script>
	import commentComponent from '../../comment/comment';
	import videoComponent from '../../video/video';
	import httpCore from "@/api/httpCore";
	import knowledgePublish from '../../home/<USER>/knowledgePublish';
	import errorPic from "@/assets/image/errorPic.png";
  import {EncryptData,DecryptData} from "@/utils/aesUtils";
	const api = new httpCore();
	export default {
    data(){
      return{
	      allShow:false,
	      allButtonShow:false,
	      picList:[],
	      commentShow:false,
	      edit:false,
	      currentUserId:this.$store.state.loginInfo.userId,
	      id:this.$route.query.id,
	      errorImg: 'this.src="' + errorPic + '"',
      }
    },
    props:['contentObj','sort','type','index'],
    components:{commentComponent,knowledgePublish,videoComponent},
    mounted(){
	    this.$nextTick(() => {
    		this.checkContent();
    	});
  		this.picList = this.contentObj.tipsImages.map(function(item){
    		return item.path;
    	});
	  },
    methods:{
	  	checkContent(){
        var partHtml=document.getElementById('content-know2'+this.sort).innerHTML;
	  		var allHtml=document.getElementById('content-know1'+this.sort).innerHTML;
	  		if(allHtml == partHtml){
	  			this.allButtonShow = false;
	  		}else{
	  			this.allButtonShow =  true;
	  		}
	  	},
	  	recommend(id){
    		var data={
    			recommendId:id,
    			recommendType:'1'
    		};
    		api.recommend(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$message({message: res.msg ,type: 'success'});
						this.$emit('getUserInfo');
						if(this.type=='recommendKnowledge'){
							this.$emit('referesh');
						}else{
							this.contentObj.recommendFlag=!this.contentObj.recommendFlag;
							this.contentObj.recommendFlag?this.contentObj.recommendCount++:this.contentObj.recommendCount--;
						}
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	},
    	collect(id){
    		var data={
    			collectionId:id,
    			collectionType:'1'
    		};
    		api.collect(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$message({message: res.msg ,type: 'success'});
						this.$emit('getUserInfo');
						if(this.type=='collectKnowledge'){
							this.$emit('referesh');
						}else{
							this.contentObj.collectionFlag=!this.contentObj.collectionFlag;
							this.contentObj.collectionFlag?this.contentObj.collectionCount++:this.contentObj.collectionCount--;
						}
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	},
    	updateCommentCount(num){
    		this.contentObj.commentCount=num;
    	},
    	toPerCenter(id) {
    		if(id!=this.id){
    			// var routeUrl=this.$router.resolve({name:'perCenter',query: { id: id }});
    			localStorage.setItem("forwordUser", id);
    			var routeUrl=this.$router.resolve({name:'perCenter'});
	      	window.open(routeUrl.href, '_blank');
    		}
	    },
	    toEditKnowledge(id){
	    	this.edit=!this.edit;
	    },
	    refereshPage(data){
	    	this.edit=false;
	    	this.$emit('refereshUpdateContent',data,this.index);
	    },
	    deleteKnowledge(id){
	    	api.deleteKnowledges(id).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$emit('referesh');
						this.$message({message: "知识删除成功" ,type: 'success'});
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	    },
	    requestTrain(){
	  		var title = '';
  			let reg=/<\/?.+?\/?>/g;
  			var tmp = this.contentObj.content.replace(reg,'');
  			tmp = tmp.replace(/&nbsp;/g,' ');
  			title = tmp.length>=50?tmp.substr(0,50)+"...":tmp;
	  		var data={trainingId:this.contentObj.id,title:title,type:"1"};
		  	api.addNotice({noticeType:'2',noticeContent: JSON.stringify(data)}).then(response => {
		  		var res = response.data;
					if(res.status == "0"){
						if(res.data=='1'){
							this.$message({'message': '您已申请过，无法重复申请' ,'type': 'warning'});
						}else{
							this.$message({'message': '申请成功' ,'type': 'success'});
						}
					}else {
						this.$message({'message': res.msg ,'type': 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
    }
  }
</script>

<style>
	.home_content .left_home_content .triangle{
		display: none !important;
	}
</style>
