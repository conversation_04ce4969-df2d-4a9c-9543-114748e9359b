<template>
	<div class="top_nav no_nav">
		<!--父专题管理  chooseThemeId：标志所选父专题，未选择时展示父专题列表-->
	  <div class="list_content" v-if="!chooseThemeId" style="position:relative">
	  	<div class="nav-content">
			  <!--父专题迁移-->
			  <span v-if="parentCheckedLength>0">共选{{parentCheckedLength}}项，移动到
			  	<el-cascader
			  		style="width:100px"
				    v-model="parentDesTheme"
				    :options="themeList"
				    :props="optionProps1"
				    emitPath="false"
				    @change="chooseParentDesTheme"></el-cascader>
			  </span>
	  	</div>
	  	<!--父专题列表-->
	    <div
	      class="infor_content"
	      v-for="(item, index) in adminThemeList"
	      :key="index"
	    >
	      <div class="left_content pointer" @click="toTheme(item.id)">
	      	<el-checkbox @click.stop.native="" class="mgr-10" style="line-height: 40px;" v-model="item.isChecked"></el-checkbox>
	        <div class="image">
	          <img v-if="item.cover" :src="item.cover" alt="" :onerror="errorImg"/>
	        </div>
	        <div class="follow_infor">
	          <div>{{item.name}}</div>
	          <div>
	            <span>{{item.followCount}}人关注</span>|<span>{{item.articleCount}}篇文章</span>|<span
	              >{{item.articleAuthorCount}}人投稿</span
	            >
	          </div>
	        </div>
	      </div>
	      <div class="right_content">
          <span class="manage-button" @click="chooseTheme(item.id);getArticleOfTheme();">管理</span>
          <!--<span class="manage-button" @click="removeTheme(item.id);">删除</span>-->
        </div>
	    </div>
	    <div v-show="adminThemeTotalPage>1&&adminThemeQueryParam.pageNum!=adminThemeTotalPage&&!adminThemeLoading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
	    	<span class="pointer" @click="loadMoreTheme">
		      <span>查看更多</span>
	      </span>
	    </div>
		  <div v-if="!adminThemeLoading&&adminThemeList.length==0" class="mg-30 fs-13 tc" style="opacity: 0.4;">
	    	<div>未查询到数据</div>
	    </div>
	    <div class="mgt-10" style="height:80px" v-loading ="adminThemeLoading" element-loading-text = "数据正在加载中"></div>
	  </div>
	  <!--子专题管理-->
	  <div class="list_content" v-else style="position: relative;">
	  	<div class="nav-content">
	  		<!--返回到父专题列表-->
	  		<img @click="back" class="mgr-10 pointer" style="width:14px;margin-bottom:-2px;" src="../../../assets/image/back.png"/>
	  		<!--子专题切换-->
	  		<el-popover
			    placement="bottom"
			    width="200"
			    trigger="manual"
			    v-model="classifySelectShow">
			    <ul class="classify-item">
			    	<li :style="classify.id==chooseThemeId?'color:#3e8cff;font-weight:bold':''" @click="changeClassify({id:chooseThemeId,name:'全部'})">全部</li>
			    	<li :style="classify.id==item.id?'color:#3e8cff;font-weight:bold':''" v-for="(item,index) in themeClassify" @click="changeClassify(item)">{{item.name}}</li>
			    </ul>
			    <span slot="reference" class="classify-select" @click="classifySelectShow = !classifySelectShow">
	        	{{classify.name}}<i class="mgl-2 el-icon-caret-bottom"></i>
					</span>
			  </el-popover>
			  <!--文章删除，迁移-->
			  <span v-if="checkedLength>0">共选{{checkedLength}}项，<span @click="deleteArticles()" class="pointer" style="color:#FF6262">删除选择项</span>&nbsp;/&nbsp;移动到
			  	<el-cascader
			  		style="width:100px"
				    v-model="desTheme"
				    :options="themeList"
				    :props="optionProps"
				    emitPath="false"
				    @change="chooseDesTheme"></el-cascader>
			  </span>
			  <!--子专题迁移-->
			  <span class="fr" v-if="chooseThemeId!=articleQueryParam.subjectId"><span style="color:#3E8CFF;">移动该子专题到</span>
			  	<el-cascader
			  		style="width:100px"
				    v-model="subDesTheme"
				    :options="themeList"
				    :props="optionProps1"
				    emitPath="false"
				    :checkStrictly="true"
				    @change="chooseSubDesTheme"></el-cascader>
			  </span>
	  	</div>
	  	<!--文章列表-->
	    <div
	      class="infor_content"
	      v-for="(item, index) in articleList"
	      :key="index"
	    >
	      <div class="left_content pointer">
	      	<div class="classify">
	      		<el-checkbox class="mgr-10" v-model="item.isChecked"></el-checkbox>
	      		<div style="cursor:text" v-if="item.subjectSequence&&chooseThemeId==articleQueryParam.subjectId" class="theme-classify mgr-10"
		  				:class="'theme-classify-'+item.subjectSequence%5" >{{ item.subjectName }}</div>
	      	</div>
					<div @click="toArticleDetail(item.id)" class="title inline-block">{{ item.title }}</div>
	      </div>
	    </div>
	    <div v-show="articleTotalPage>1&&articleQueryParam.pageNum!=articleTotalPage&&!articleLoading" class="pdt-10 fs-13 tc" style="opacity: 0.5;">
	    	<span class="pointer" @click="loadMoreArticle">
		      <span>查看更多</span>
	      </span>
	    </div>
		  <div v-if="!articleLoading&&articleList.length==0" class="pd-30 fs-13 tc" style="opacity: 0.4;">
	    	<div>未查询到数据</div>
	    </div>
	    <div class="pdt-10" style="height:80px" v-loading ="articleLoading" element-loading-text = "数据正在加载中"></div>
	  </div>
  </div>
</template>

<script>
import {Message,MessageBox} from 'element-ui';
import httpCore from "@/api/httpCore";
const api = new httpCore();
import errorPic from "@/assets/image/errorPic.png";
export default {
  props:['id'],
  data() {
    return {
    	//父专题列表
      adminThemeList: [],
      adminThemeQueryParam:{
      	pageNum:1,
    		pageSize:5,
    		accountId:this.id
      },
      adminThemeTotalPage:0,
      adminThemeLoading:true,

      //所选父专题
      chooseThemeId:'',

      //文章列表
      articleList: [],
      articleQueryParam:{
      	pageNum:1,
    		pageSize:5,
    		subjectId:''
      },
      articleTotalPage:0,
      articleLoading:true,

      //子专题分类
      themeClassify:[],
      //子专题分类弹框展示标志
      classifySelectShow:false,
      //所选分类
      classify:null,
      //父子专题层级关系列表
      themeList:[],
      optionProps:{
      	value:'id',
      	label:'name'
      },
      optionProps1:{ //专题不可迁移到子专题下，利用children设置过滤掉子专题
      	value:'id',
      	label:'name',
      	children:'subChildren'
      },
      parentDesTheme:[], //父专题迁移目的专题
      desTheme:[],  //文章迁移目的专题
      subDesTheme:[],  //子专题迁移目的专题
      errorImg: 'this.src="' + errorPic + '"',
    };
  },
  computed:{
  	//文章check数量
	  checkedLength: function() {
      var list = this.articleList.filter(function(val){
 				return val.isChecked;
 			});
 			return list.length;
    },
    //父专题check数量
    parentCheckedLength: function() {
    	console.log(122)
      var list = this.adminThemeList.filter(function(val){
 				return val.isChecked;
 			});
 			return list.length;
    }
  },
  mounted() {
  	this.articleQueryParam.subjectId='';
  	this.getAdminList();
  	this.getThemeTree();
  },
  methods: {
  	//获取我管理的专题列表
  	getAdminList:function(){
  		this.adminThemeLoading = true;
  		api.getMyModeratorSubject(this.adminThemeQueryParam).then(response => {
    		var res = response.data;
    		this.adminThemeLoading=false;
				if(res.status == "0"){
					var adminThemeList = res.data.list;
					adminThemeList.forEach(function(item){
						item.isChecked=false;
		  		});
		  		this.adminThemeList = this.adminThemeList.concat(adminThemeList);
					this.adminThemeTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	//我管理的专题列表加载更多
  	loadMoreTheme(){
    	if(this.adminThemeQueryParam.pageNum<this.adminThemeTotalPage){
    		this.adminThemeQueryParam.pageNum++;
    		this.getThemeList();
    	}
    },
    //跳转到专题详细页面
    toTheme(id) {
      var routeUrl=this.$router.resolve({name:'themeInner',query: { themeId: id }});
      window.open(routeUrl.href, '_blank');
    },
    //切换子专题
    changeClassify(obj){
    	if(this.classify.id!=obj.id){
    		this.classify=obj;
    		this.articleQueryParam.pageNum=1;
    		this.articleQueryParam.subjectId=obj.id;
    		this.articleList=[];
    		this.getArticleOfTheme();
    	}
    	this.classifySelectShow=false;
    },
    //父专题选择
    chooseTheme(id){
    	this.chooseThemeId=id;
    	this.articleQueryParam.subjectId=id;
    	this.classify={id:id,name:'全部'};
    	this.desTheme=[];
			api.queryThemeInfo(id).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.themeClassify=res.data.children;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    //获取专题下的文章
    getArticleOfTheme(){
    	this.articleLoading = true;
    	api.getNewCollectArticlesOfTheme(this.articleQueryParam).then(response => {
    		var res = response.data;
    		this.articleLoading=false;
				if(res.status == "0"){
					var articles = res.data.list;
					articles.forEach(function(item){
						item.isChecked=false;
		  		});
		  		this.articleList = this.articleList.concat(articles);
					this.articleTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    //文章列表加载更多
  	loadMoreArticle(){
    	if(this.articleQueryParam.pageNum<this.articleTotalPage){
    		this.articleQueryParam.pageNum++;
    		this.getArticleOfTheme();
    	}
    },
    //跳转到文章详情页面
    toArticleDetail(id){
  		var routeUrl=this.$router.resolve({name:'articleDetail',query: { id: id}});
  		window.open(routeUrl.href, '_blank');
  	},
  	//获取专题层级关系树
  	getThemeTree(){
  		api.queryThemeTree().then(response => {
    		var res = response.data;
				if(res.status == "0"){
		  		this.themeList = this.clearEmptyChildren(res.data);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	clearEmptyChildren(data) {
      // 循环遍历json数据
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          // children若为空数组，则将children设为undefined
          data[i].children = undefined;
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          this.clearEmptyChildren(data[i].children);
        }
      }
      return data;
    },
    //文章迁移目的专题选择
  	chooseDesTheme(){
  		var ids = "";
  		this.articleList.forEach(function(val){
 				if(val.isChecked){
 					ids+=(ids==""?val.id:","+val.id);
 				}
 			});
  		api.transferArticle(this.desTheme[this.desTheme.length-1],{ids:ids}).then(response => {
    		var res = response.data;
    		this.desTheme=[];
				if(res.status == "0"){
		  		this.$message({message: res.msg ,type: 'success'});
		  		this.articleQueryParam.pageNum=1;
	    		this.articleList=[];
	    		this.getArticleOfTheme();
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	//子专题迁移目的专题选择
  	chooseSubDesTheme(){
  		var ids = this.articleQueryParam.subjectId;
  		var desThemeId = this.subDesTheme[this.subDesTheme.length-1];
  		api.transferTheme(desThemeId,{ids:ids}).then(response => {
    		var res = response.data;
    		this.subDesTheme=[];
				if(res.status == "0"){
		  		this.$message({message: res.msg ,type: 'success'});
		  		this.flushSubThemeSequence(desThemeId);
		  		this.back();
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	//父专题迁移目的专题选择
  	chooseParentDesTheme(){
  		var ids = "";
  		this.adminThemeList.forEach(function(val){
 				if(val.isChecked){
 					ids+=(ids==""?val.id:","+val.id);
 				}
 			});
 			var desThemeId = this.parentDesTheme[this.parentDesTheme.length-1];
  		api.transferTheme(desThemeId,{ids:ids}).then(response => {
    		var res = response.data;
    		this.parentDesTheme=[];
				if(res.status == "0"){
		  		this.$message({message: res.msg ,type: 'success'});
		  		this.adminThemeQueryParam.pageNum=1;
  				this.adminThemeList=[];
  				this.getAdminList();
  				this.flushSubThemeSequence(desThemeId);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	//文章删除
  	deleteArticles(){
  		var ids = "";
  		this.articleList.forEach(function(val){
 				if(val.isChecked){
 					ids+=(ids==""?val.id:","+val.id);
 				}
 			});
 			api.deleteArticles(ids).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.$message({message: res.msg ,type: 'success'});
		  		this.articleQueryParam.pageNum=1;
	    		this.articleList=[];
	    		this.getArticleOfTheme();
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	//返回到父专题列表
  	back(){
  		//清空子专题数据
  		this.chooseThemeId='';
  		this.articleQueryParam.pageNum=1;
  		this.articleList=[];
  		this.classifySelectShow=false;

  		//清空父专题数据
  		this.adminThemeQueryParam.pageNum=1;
			this.adminThemeList=[];
			this.getAdminList();
  	},
  	flushSubThemeSequence(id){
  		api.flushSubThemeSequence(id).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					//无处理
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
    removeTheme(id){
      api.deleteTheme(id).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.$message({message: "删除成功" ,type: 'success'});
				}else {
					this.$message({message: "删除失败" ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    }
  }
}
</script>

<style>

</style>
