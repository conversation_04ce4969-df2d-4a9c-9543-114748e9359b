<template>
  <div class="center_home article-contain">
    <div class="user_infor">
      <div class="left_infor">
        <img
        	v-if="userForm.headImg"
          :src="userForm.headImg"
          :onerror="errorImg"
        />
      </div>
      <div class="right_infor">
        <div class="name">{{userForm.nickName}}</div>
        <div class="sign">{{userForm.personalSignature}}</div>
      </div>
    </div>
    <div class="user_follow">
      <el-tabs class="tab1" v-model="activeName" tab-position="left">
        <el-tab-pane name="follow">
          <div slot="label" class="tab_name">
            <span>{{desc}}的关注</span><span>{{userForm.followCount}}</span>
          </div>
          <myFollow v-if="activeName=='follow'" :id="id" @getUserInfo="getUserInfo"></myFollow>
        </el-tab-pane>
        <el-tab-pane  name="fans">
          <div slot="label" class="tab_name">
            <span>{{desc}}的粉丝</span><span>{{userForm.fansCount}}</span>
          </div>
          <myFans v-if="activeName=='fans'" :id="id" @getUserInfo="getUserInfo"></myFans>
        </el-tab-pane>
        <el-tab-pane name="collect" v-if="currentUserId==id">
          <div slot="label" class="tab_name">
            <span>{{desc}}的收藏</span><span>{{userForm.collectionCount}}</span>
          </div>
          <myCollect v-if="activeName=='collect'" :id="id" @getUserInfo="getUserInfo"></myCollect>
        </el-tab-pane>
        <el-tab-pane name="recommend" v-if="currentUserId==id">
          <div slot="label" class="tab_name">
            <span>{{desc}}的推荐</span><span>{{userForm.recommendCount}}</span>
          </div>
          <myRecommend v-if="activeName=='recommend'" :id="id" @getUserInfo="getUserInfo"></myRecommend>
        </el-tab-pane>
        <el-tab-pane name="theme">
          <div slot="label" class="tab_name">
            <span>{{desc}}的专题</span><span>{{userForm.subjectCount}}</span>
          </div>
          <myTheme v-if="activeName=='theme'" :id="id"></myTheme>
        </el-tab-pane>
        <el-tab-pane name="admin" v-if="currentUserId==id">
          <div slot="label" class="tab_name">
            <span>版主清单</span><span>{{userForm.moderatorSubjectCount}}</span>
          </div>
          <adminList v-if="activeName=='admin'" :id="id" @getUserInfo="getUserInfo"></adminList>
        </el-tab-pane>
        <!--<el-tab-pane name="topic">
          <div slot="label" class="tab_name">
            <span>我的话题</span><span>{{userForm.topicCount}}</span>
          </div>
          <myTopic v-if="activeName=='topic'" :id="id"></myTopic>
        </el-tab-pane>-->
        <el-tab-pane name="article">
          <div slot="label" class="tab_name">
            <span>{{desc}}的文章</span><span>{{userForm.articleCount}}</span>
          </div>
          <myArticles v-if="activeName=='article'" :id="id" @getUserInfo="getUserInfo"></myArticles>
        </el-tab-pane>
        <el-tab-pane name="knowledge">
          <div slot="label" class="tab_name">
             <!-- 上海的奇葩要求 -把话题叫成小知识-->
            <span>{{desc}}的话题</span><span>{{userForm.tipsCount}}</span>
            <!-- <span>{{desc}}的小知识</span><span>{{userForm.tipsCount}}</span> -->
          </div>
          <myKnowledges v-if="activeName=='knowledge'" :id="id" @getUserInfo="getUserInfo"></myKnowledges>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import myFollow from './component/myFollow';
import myFans from './component/myFans';
import myTheme from './component/myTheme';
import myTopic from './component/myTopic';
import myArticles from './component/myArticles';
import myKnowledges from './component/myKnowledges';
import myRecommend from './component/myRecommend';
import myCollect from './component/myCollect';
import adminList from './component/adminList';
import httpCore from "@/api/httpCore";
const api = new httpCore();
import errorPic from "@/assets/image/errorPic.png";
import {EncryptData,DecryptData} from "@/utils/aesUtils";
export default {
  name: "perCenter",
  components:{myFollow,myFans,myTheme,myTopic,myArticles,myKnowledges,myRecommend,myCollect,adminList},
  data() {
    return {
    	// id:this.$route.query.id,
    	id:localStorage.getItem('forwordUser'),
    	userForm:{},
      activeName: "follow",
      currentUserId:this.$store.state.loginInfo.userId,
      desc:'我',
      errorImg: 'this.src="' + errorPic + '"',
    };
  },
  mounted() {
  	if(this.$route.query.tab){
  		this.activeName=this.$route.query.tab;
  	}
    // if(!(this.$route.query.id)){
    // 	this.id=localStorage.getItem('forwordUser');
    // }
  	this.getUserInfo();
    this.changeStyle();
  },
  methods: {
  	getUserInfo:function(){
  		// api.getUserInfo(this.id).then(response => {
  		api.getUserInfoAES(localStorage.getItem('forwordUser')).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.userForm = res.data;
					if(this.id!=this.currentUserId){
						this.desc=this.userForm.gender=='2'?'她':'他';
					}
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
    cancelFollow(id) {
      console.log(id);
    },
    changeStyle() {
      let button = document.getElementsByClassName("follow_button");
      for (let i = 0; i < button.length; i++) {
        button[i].onmouseenter = () => {
          button[i].innerText = "取消关注";
          button[i].onmouseleave = () => {
            button[i].innerHTML =
              "<i class='iconfont icon-icon-check'></i>已关注";
          };
        };
      }
    },
  },

};
</script>
<style>
</style>
