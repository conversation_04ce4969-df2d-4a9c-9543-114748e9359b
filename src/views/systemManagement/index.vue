<template>
	<div>
		<div class="com-nav">
	    <div class="nav">
	      <div class="nav_list fs-14">
	        <div class="left_nav">
	          <div class="logo">
	            <img src="../../assets/image/logo1.png" />
	          </div><span style="color:#3e8cff;font-weight: bold;width:400px;text-align: left;">系统管理</span>
	        </div>
	      </div>
	    </div>
	  </div>
	  <div class="center_home mgt-50">
	    <div class="system-manage">
	      <el-tabs v-model="activeName" tab-position="left">
	        <el-tab-pane label="专题创建角色配置" name="powerManage">
	        	<powerManage></powerManage>
	        </el-tab-pane>
	        <el-tab-pane label="用户默认信息配置" name="userManage">
	        	<userManage></userManage>
	        </el-tab-pane>
	      </el-tabs>  
	    </div>  
    </div>
	</div>
</template>
<script>
import httpCore from "@/api/httpCore";
const api = new httpCore();
import userManage from "./component/userManage";
import powerManage from "./component/powerManage";
export default {
  data() {
    return {
    	activeName:'powerManage',
    };
  },
  components:{
		userManage,powerManage
  },
  methods: {
  	
  },
  mounted() {
  },
};
</script>
<style lang="scss">
	.system-manage{
    .el-tabs--left .el-tabs__active-bar.is-left {
      left: 5px;
      right: 174px;
      height: 15px !important;
      top: 13px;
    }

    .el-tabs__nav-wrap {
      position: static;
    }

    .el-tabs__nav-wrap::after {
      background-color: #fff;
    }

    .el-tabs--left .el-tabs__item.is-left {
      text-align: left;
      font-size:13px;
    }

    .tab_name {
      width: 143px;
      height: 45px;
      display:flex;
      justify-content: space-between;
    }
    
    .el-tabs__content {
      border-left: 2px solid #f3f3f3;
      min-height:360px;
      margin-left: -10px;
    }
    
    .el-tabs--left .el-tabs__header.is-left {
		  margin-right: 0px;
		}
	}
</style>
