<template>
	<div>
		<div style="display: flex;">
			<div style="flex:1;border:1px solid #E2E2E2;border-left:none">
				<el-input class="priority-tree-filter" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
			  <div style="overflow-y:auto;height:460px;">
			      <el-tree class="priority-tree-def pdt-5"
			      	ref="procTree" :data="roleList"
			      	:props="defaultProps"
			        :filter-node-method="filterNode"
			        show-checkbox
			        @check-change="checkChange"
			        node-key="id"
			        :default-checked-keys="defaultCheckVal">
			      </el-tree>
			  </div>
		  </div>
		  <div style="flex:1;border:1px solid #E2E2E2;border-left:none">
				<div class="tree-title">已选角色<span @click="clearTree()" class="fr color-light-black mgr-10 pointer">清空全部<i class="el-icon-delete pdl-5 fs-14" style="color:#FF6262;font-weight:600;"></i></span></div>
				<ul class="role-list" style="overflow-y:auto;height:460px;">
					<template v-if="submitNodes.length>0">
						<li v-for="(obj,index) in submitNodes">
								<span :title="obj.label">{{ obj.label }}</span>
		            <span class="fr"><i @click="delCheckNodes(obj.id)" class="el-icon-error fs-14 pointer" style="color:#909199;background-color:#F5F6F6;"></i></span>
						</li>
					</template>
					<li class="pd-12 fs-13 tc" style="opacity: 0.5;" v-else>暂无数据</li>
				</ul>
			</div>
		</div>
		<div class="mgt-10 tc">
			<button class="submit-button" @click="submitTree">提交</button>
		</div>
	</div>
</template>

<script>
import httpCore from "@/api/httpCore";
const api = new httpCore();
export default {
  data() {
    return {
    	filterText: '',
    	defaultProps: {
        children: 'children',
        label: 'label',
        id: 'id'
      },
      submitNodes:[],
      defaultCheckVal:[],
      roleList: [],
    };
  },
  mounted() {
		this.setInitCheckNodes();
  },
  watch: {
    filterText(val) {
      this.$refs.procTree.filter(val);
    }
  },
  methods: {
  	filterNode: function(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    getRoleList:function() {
      const data = [];
      api.getRoleList().then(response => {
				var respData = response.data.data.entity;
        if (respData && respData.length > 0) {
          respData.forEach(item => {
            var curNode = {id: item.id,label: item.name,children: []};
            if (item.parentId == undefined || item.parentId == null || item.parentId == '') {
              data.push(curNode);
            } else {
              var pNode = this.getParentNode(data, item.parentId);
              if (pNode != undefined && pNode != null) {
                pNode.children.push(curNode);
              } else {
                data.push(curNode);
              }
            }
          });
        }
        this.roleList=data;
			})
			.catch(err => {
				console.log(err);
			});
    },
  	getParentNode: function(nodeList, pId) {
	    if (nodeList && nodeList.length > 0) {
        nodeList.forEach(item => {
          if (item.id == pId) {
            return item;
          } else {
            if (item.children && item.children.length > 0) {
              var temp = this.getParentNode(item.children, pId);
              if (temp != null) {
                return temp;
              }
            }
          }
        });
	    }
	    return null;
	  },
	  checkChange:function(data,checked,indeterminate){
	  	this.submitNodes=this.$refs.procTree.getCheckedNodes();
		},
		//清空按钮
  	clearTree:function(){
  		this.submitNodes=[];
  		this.$refs.procTree.setCheckedKeys([]);
  	},
 		//删除按钮
  	delCheckNodes:function(id,name){
  		this.submitNodes=this.submitNodes.filter(item => item.id != id);
			this.setUnchecked(id);
  	},
  	//把某个节点设成未选中的状态
  	setUnchecked:function(id){
  		this.$refs.procTree.setChecked(id,false,false);
  	},
		submitTree:function(){
 			var ids="";
 		  this.submitNodes.forEach(item =>{
	  		ids==""?ids+=item.id:ids+=","+item.id;
	  	});
	  	api.setThemeCreatePower(ids).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.$message({message: "配置成功" ,type: 'success'});
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
 		},
 		setInitCheckNodes:function(){
 			api.getThemeCreatePower().then(response => {
    		var res = response.data;
				if(res.status == "0"){
 					this.defaultCheckVal=res.data.map(function(value){
					  return value.id;
					});
					this.getRoleList();
					this.submitNodes=res.data;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
 		},
    getSubjectRoleList(){
      api.getSubjectRole().then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.roleList = res.data.roleList;
        }
      })
    },
    checkRoleRepeat(){
      api.checkRoleRepeat({roleName: ''}).then(response => {
        var res = response.data;
        if (res.status == "0"){
          this.$message({message: res.msg ,type: 'error'});
        }
      })
    }
  }
}
</script>

<style lang="scss">
	.priority-tree-filter input{
    border:none;
    background-color:transparent;
    border-bottom: 1px solid #E2E2E2;
    border-radius: 0;
    font-size:13px;
	}
	.priority-tree-def{
		background: transparent;
    .el-tree-node{
      font-size:13px;
      .el-tree-node__content:hover{
        color:#409EFF;
      }
      .el-tree-node__label{
      	font-size:13px;
      }
    }
    .el-tree__empty-text{
    	font-size:13px;
    }
	}
	.tree-checked-item{
    color:#409EFF;
    font-weight: 600;
	}
	.tree-title{
		height:40px;
		line-height: 40px;
		background: #F5F6F6;
		border-bottom: 1px solid #E9E9E9;
		padding-left: 20px;
	}
	.role-list{
		padding: 10px 20px;
		li{
			height:34px;
			line-height:34px;
		}
	}
</style>
