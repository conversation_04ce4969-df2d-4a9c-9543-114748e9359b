<template>
	<div class="pdt-10 mgl-20">
    <div class="avatar_box">
    	<span class="color-6 mgb-15 fs-13">默认头像配置：</span>
      <div :class="avatar.value?'image':'cover-image'">
      	<img v-if="avatar.value" :src="avatar.value" class="avatar" :onerror="errorImg"/>
        <img v-else src="../../../assets/image/bg4.png" alt="" />
      </div>
      <el-upload
        class="avatar-uploader"
      	accept=".gif,.jpg,.jpeg,.bmp,.png"
       	:show-file-list="false"
       	:action="uploadPath"
       	:on-success="handleSuccess"
	    >
        <div class="change_avatar">点击上传</div>
      </el-upload>
    </div>
    <div v-show="id" class="color-6 mgb-15 fs-13">
    	<div>返回id：<font color="orangered">{{id}}</font></div>
    	<div>请将返回id配置在后端配置文件application.yml中，配置内容参考以下写法：</div>
    	<div style="white-space: pre;background: black;color:white">
file:
  http:
    prefix: /file-upload-service/v1/paas_attachments_tool/download?id=
  default:
    image: xxx(此处替换返回id)
    	</div>
    </div>
  </div>
</template>

<script>
import httpCore from "@/api/httpCore";
const api = new httpCore();
import errorPic from "@/assets/image/errorPic.png";
export default {
  data() {
    return {
    	avatar:{value:''},
    	uploadPath:'/file-upload-service/v1/paas_attachments_tool/upload/lezhi-service',
    	downloadPath:'/file-upload-service/v1/paas_attachments_tool/download?id=',
    	id:'',
			errorImg: 'this.src="' + errorPic + '"',
    };
  },
  mounted() {
  	this.getDefaultAvatar();
  },
  methods: {
  	getDefaultAvatar:function(){
//		var data={key:'defaultAvatar'};
//		api.getDictionary(data).then(response => {
//  		var res = response.data;
//				if(res.status == "0"){
//					this.avatar = res.data;
//				}else {
//					this.$message({message: res.msg ,type: 'error'});
//				}
//			})
//			.catch(err => {
//				console.log(err);
//			});
  	},
  	handleSuccess(res, file) {
  		var data={key:'defaultAvatar',value:this.downloadPath+res.data};
  		this.avatar.value = this.downloadPath+res.data;
  		this.id=res.data;
//    api.setDictionary(data).then(response => {
//  		var res = response.data;
//				if(res.status == "0"){
//					this.avatar.value = this.downloadPath+res.data;
//				}else {
//					this.$message({message: res.msg ,type: 'error'});
//				}
//			})
//			.catch(err => {
//				console.log(err);
//			});
    },
  }
}
</script>

<style lang="scss" scoped>
.avatar_box {
  width: 100%;
  display:flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-bottom: 15px;

  .avatar-uploader {
    width: 52px;
    height: 36px;
  }
  
  .cover-image{
    width: 52px;
    height: 52px;
    line-height: 52px;
    overflow: hidden;
    img{
      width:52px;
      height:52px;
    }
  }

  .image {
    width: 52px;
    height: 52px;
    line-height: 52px;
    border: 1px dotted #d9d9d9;
    border-radius: 50%;
    overflow: hidden;

    img {
      width: 51px; 
      height:51px;
    }
  }

  .change_avatar {
    width: 52px;
    height: 36px;
    line-height: 36px;
    font-weight: 400;
    color: #C0C4CC;

    &:hover {
      cursor: pointer;
    }
  }
}
</style>