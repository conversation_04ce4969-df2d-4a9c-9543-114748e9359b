<template>
  <div>
    <p class="js_tis" style="font-size:24px; text-align:center; margin-top:30px; cursor:pointer">
      404或者系统正在升级。。。</p>
    <!--<p class="js_tis" style="font-size:24px; text-align:center; margin-top:30px; cursor:pointer">
      &lt;!&ndash;tis start&ndash;&gt;
      <div class="tis">
        <p class="tis-colse">X</p>
        <div class="tis-txt">404</div>
      </div>
      &lt;!&ndash;tis end&ndash;&gt;
    </p>-->
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  components: {},
  created() {
  },
  mounted() {
  },
  beforeDestroy() {
  },
  computed: {},
  methods: {}
};
</script>
