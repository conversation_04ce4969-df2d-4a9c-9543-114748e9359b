<template>
	<div class="article_content">
		<div class="top_content">
      <div class="user_infor">
        <div class="image">
          <img v-if="contentObj.authorHeadImg" :src="contentObj.authorHeadImg" alt="" :onerror="errorImg"/>
        </div>
        <div class="user">
          <div class="name">{{contentObj.authorName}}</div>
          <div class="sign">
            <span>{{contentObj.cteTime|fomatTime()}}</span>
            <span>来自</span>
            <span>{{contentObj.authorDept}}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="middle_content">
			<div class="knowledge_content">
	      <!-- <div class="content" v-html="contentObj.content"></div> -->
	      <div class="content" v-dompurify-html="contentObj.content"></div>
	      
	      <div v-if="contentObj.tipsImages.length>0" class="picture" :style="contentObj.tipsImages.length==9?'width:90%;':(contentObj.tipsImages.length>=6?'width:90%;':'')">
	      	<div v-for="(pic,index) in contentObj.tipsImages">
	      		<el-image
				      :style="contentObj.tipsImages.length==9?'height:150px;width:150px':(contentObj.tipsImages.length>=6?'height:120px;width:120px':'height:100px;width:100px')"
				      v-if="pic.path"
				      :src="pic.path"
				      fit="cover"
				      :preview-src-list="picList">
				    </el-image>
	      	</div>
	      </div>
	      <div v-if="contentObj.tipsVideo.path">
	      	<div class="video">
	      		<videoComponent style="height:236px;width:100%" :id="'video_' + sort" :url="contentObj.tipsVideo.path"></videoComponent>
	        </div>
	      </div>
				<div class="mgt-15" v-if="contentObj.tipsAttach.length>0">
					<div class="color-9"><i class="mgr-5 el-icon-paperclip"></i>附件信息：</div>
	    		<div v-for="(att,index) in contentObj.tipsAttach" class="att-block">
	          <a :href="att.path">{{att.name}}</a>
		      </div>
	      </div>
	    </div>
	  </div>
    <div class="bottom_content">
      <div class="pointer" @click="commentShow=!commentShow">
        <img src="../../assets/image/art1.png" alt="" />{{contentObj.commentCount}}评论
      </div>
      <div class="pointer" @click="recommend(contentObj.id)"><img :src="require(`../../assets/image/recommend_${contentObj.recommendFlag}.png`)" alt="" />{{contentObj.recommendCount}}推荐</div>
      <div class="pointer" @click="collect(contentObj.id)"><img :src="require(`../../assets/image/collect_${contentObj.collectionFlag}.png`)" alt="" />{{contentObj.collectionCount}}收藏</div>
    </div>
    <commentComponent v-if="commentShow" :type="'knowledge'" :id="contentObj.id" :author="contentObj.author" @updateCommentCount="updateCommentCount"></commentComponent>
	</div>
</template>

<script>
	import commentComponent from '../comment/comment';
	import videoComponent from '../video/video';
	import httpCore from "@/api/httpCore";
	import errorPic from "@/assets/image/errorPic.png";
	const api = new httpCore();
	export default{
		props:['id'],
		components:{commentComponent,videoComponent},
		data(){
      return{
      	contentObj:{
      		tipsImages:[],tipsVideo:{},tipsAttach:[],
      		recommendFlag:false,
      		collectionFlag:false
      	},
      	commentShow:false,
      	picList:[],
      	errorImg: 'this.src="' + errorPic + '"',
      }
    },
    watch:{
	  	'id':{
	  		immediate: true,
        handler(val, oldVal) {
          this.getKnowledgeContent(val);
        }
      }
	  },
		methods:{
			getKnowledgeContent(id){
	    	api.getKnowledgeContent(id).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.contentObj = res.data;
						this.picList = this.contentObj.tipsImages.map(function(item){
			    		return item.path;
			    	});
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	recommend(id){
    		var data={
    			recommendId:id,
    			recommendType:"1"
    		};
    		api.recommend(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.contentObj.recommendFlag=!this.contentObj.recommendFlag;
						this.contentObj.recommendFlag?this.contentObj.recommendCount++:this.contentObj.recommendCount--;
						this.$message({message: res.msg ,type: 'success'});
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	},
    	collect(id){
    		var data={
    			collectionId:id,
    			collectionType:"1"
    		};
    		api.collect(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.contentObj.collectionFlag=!this.contentObj.collectionFlag;
						this.contentObj.collectionFlag?this.contentObj.collectionCount++:this.contentObj.collectionCount--;
						this.$message({message: res.msg ,type: 'success'});
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	},
    	updateCommentCount(num){
	  		this.contentObj.commentCount=num;
	  	},
		}
	}
</script>

<style>
</style>