<template>
	<div class="pdt-15 article-contain" @click="handleClick">
	  <div class="home_content">
	    <div class="left_home_content">
	      <div class="release_topic">
	        <div class="home_title">
	          <div>
	            <i class="iconfont icon-duihua"></i>
	            <span>话题</span>
	          </div>
	          <div @click="toWriteArticle()">
	            <i class="iconfont icon-wenzhang2" style="margin-top:-2px;"></i>
	            <span>文章</span>
	          </div>
	        </div>
	        <knowledgePublish ref="knowledge" @refereshPage="refereshPage"></knowledgePublish>
	      </div>

	      <div class="type-list">
	      	<ul>
	      		<li :class="queryParam.sortType=='1'?'active':''" @click="changeSortType('1')">全部</li>
	      		<li :class="queryParam.sortType=='2'?'active':''" @click="changeSortType('2')">阅读量</li>
	      		<li :class="queryParam.sortType=='3'?'active':''" @click="changeSortType('3')">热度</li>
	      		<li :class="queryParam.sortType=='4'?'active':''" @click="changeSortType('4')">关注</li>
	      	</ul>
	      </div>
	      <div v-for="(item,index) in topList" v-if="showAllTop||index<2">
	      	<contentComponent
	      		:contentObj="item"
	      		:sort="'top_'+index"
	      		@updateFollowStatus="updateFollowStatus"
	      		@getUserInfo="getUserInfo"
	      		@setTop="setTop"
	      		showSetTop="true"
	      	></contentComponent>
	      </div>
	      <div v-if="topList.length>2" class="mgb-10 tr mgr-2"><span style="color:#3E8CFF;" class="pointer" @click="showAllTop=!showAllTop">{{showAllTop?'隐藏部分置顶':'展示所有置顶'}}</span></div>
	      <div v-for="(item,index) in contentList">
	      	<contentComponent
	      		:contentObj="item"
	      		:sort="index"
	      		@updateFollowStatus="updateFollowStatus"
	      		@getUserInfo="getUserInfo"
	      		@setTop="setTop"
	      		showSetTop="true"
	      	></contentComponent>
	      </div>
	      <div v-show="totalPage>1&&queryParam.pageNum!=totalPage&&!loading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
		    	<span class="pointer" @click="loadMore">
			      <span>查看更多</span>
		      </span>
		    </div>
		    <div class="mgt-10" style="height:80px" v-loading ="loading" element-loading-text = "数据正在加载中"></div>
	      <!-- 首页为空时，展示内容 -->
	      <div class="empty" v-if="!loading&&contentList.length==0&&topList.length==0">
	      	<img src="../../assets/image/empty.png" alt="" />
	      	<div>暂无足迹~</div>
	      </div>
	    </div>
	    <div class="right_home_content">
	      <div class="user_infor">
	        <div class="infor_top">
	          <div class="image" @click="toPerCenterTab('follow')">
	            <img v-if="userInfo.headImg" :src="userInfo.headImg" alt="" :onerror="errorImg"/>
	          </div>
	          <div class="user">
	            <div class="name">{{userInfo.nickName}}</div>
	            <div class="sign" :title="userInfo.personalSignature">{{userInfo.personalSignature}}</div>
	          </div>
	        </div>
	        <div class="infor_bottom">
	          <div class="tip_content" @click="toPerCenterTab('follow')">
	            <div class="num">{{userInfo.followCount}}</div>
	            <div class="tip">
	              <img src="../../assets/image/tip1.png" alt="" />
	              <span>关注</span>
	            </div>
	          </div>
	          <div class="tip_content" @click="toPerCenterTab('fans')">
	            <div class="num">{{userInfo.fansCount}}</div>
	            <div class="tip">
	              <img src="../../assets/image/tip2.png" alt="" />
	              <span>粉丝</span>
	            </div>
	          </div>
	          <div class="tip_content" @click="toPerCenterTab('collect')">
	            <div class="num">{{userInfo.collectionCount}}</div>
	            <div class="tip">
	              <img src="../../assets/image/tip3.png" alt="" />
	              <span>收藏</span>
	            </div>
	          </div>
	          <div class="tip_content" @click="toPerCenterTab('recommend')">
	            <div class="num">{{userInfo.recommendCount}}</div>
	            <div class="tip">
	              <img src="../../assets/image/tip4.png" alt="" />
	              <span>推荐</span>
	            </div>
	          </div>
	          <div class="tip_content" @click="toPerCenterTab('admin')">
	            <div class="num">{{userInfo.moderatorSubjectCount}}</div>
	            <div class="tip">
	              <img style="width:11px;height:11px;" src="../../assets/image/tip5.png" alt="" />
	              <span>版主</span>
	            </div>
	          </div>
	        </div>
	      </div>
	      <div class="create_center">
	        <div class="center_title">
	          <img src="../../assets/image/create1.png" alt="" />
	          <span>创作中心</span>
	        </div>
	        <div class="center_num">
	          <div class="left_num">
	            <p class="mgb-5">总被阅读数</p>
	            <p class="num">{{userInfo.beReadCount}}</p>
	            <p>较前日<span :class="getChangeState(userInfo.beReadChange)">{{userInfo.beReadChange}}</span></p>
	          </div>
	          <div class="right_num">
	            <p class="mgb-5">总被推荐数</p>
	            <p class="num">{{userInfo.beRecommendCount}}</p>
	            <p>较前日<span :class="getChangeState(userInfo.beRecommendChange)">{{userInfo.beRecommendChange}}</span></p>
	          </div>
	        </div>
	        <div class="center_tip">
	          <div class="pointer" @click="toPerCenterTab('article')">
	            <img style="width:28px;" src="../../assets/image/create2.png" alt="" />
	            <div>
	              <p>{{userInfo.articleCount}}</p>
	              <p>文章</p>
	            </div>
	          </div>
	          <div class="pointer" @click="toPerCenterTab('knowledge')">
	            <img src="../../assets/image/knowledge.png" alt="" />
	            <div>
	              <p>{{userInfo.tipsCount}}</p>
	              <p>小知识</p>
	            </div>
	          </div>
	          <div class="pointer" @click="toPerCenterTab('theme')">
	            <img style="width:21px;" src="../../assets/image/create3.png" alt="" />
	            <div>
	              <p>{{userInfo.subjectCount}}</p>
	              <p>专题</p>
	            </div>
	          </div>
	        </div>
	      </div>
	      <div class="hot_topic">
	        <div class="topic_title">
	          <div>热门话题</div>
	          <div class="pointer" @click="changeHotTopicPage">
	            <span>换一批</span>
	            <img src="../../assets/image/referesh.png" alt="" />
	          </div>
	        </div>
	        <div class="content">
	          <ul>
	            <li v-for="(item, index) in hotTopicList" :key="index">
	              <span :title="'#'+item.topic+'#'" class="pointer" @click="toTopicDetail(item.id)">
	                <font class="fwb" style="color:#00c7ff;">#&nbsp;</font><span>{{ item.topic }}</span>
	                <font class="fwb" style="color:#00c7ff;">#</font>
	              </span>
	              <span>{{ item.discussCount }}</span>
	            </li>
	          </ul>
	        </div>
	      </div>
	    </div>
	  </div>
		<el-dialog
			class="readRule-dialog"
		  :visible.sync="showRule"
		  width="60%"
		  :close-on-click-modal="false"
		  :close-on-press-escape="false"
		  top="25vh"
		  :show-close="false">
		  <div class="rule-title">社区公告</div>
		  <div class="rule-block">
		  	<div class="notice"><img src="../../assets/image/notice.png" /></div>
		  	<!-- <div class="read-rule" v-html="rule"></div> -->
		  	<div class="read-rule" v-dompurify-html="rule"></div>
		  	<div class="cancel" @click="showRule=false;"><img src="../../assets/image/cancel.png" /></div>
		  </div>
		</el-dialog>
  </div>
</template>

<script>
import knowledgePublish from "./component/knowledgePublish";
import contentComponent from "./component/content";
import httpCore from "@/api/httpCore";
import errorPic from "@/assets/image/errorPic.png";
import {EncryptData,DecryptData} from "@/utils/aesUtils";
const api = new httpCore();
export default {
  name: "index",
  components:{knowledgePublish,contentComponent},
  data() {
    return {
    	showRule:false,
    	topList:[],
    	contentList:[],
    	userInfo:{},
      hotTopicList: [],
		  hotTopicQueryParam:{
      	pageNum:1,
    		pageSize:10,
      },
      hotTopicTotalPage:0,
      currentUserId:this.$store.state.loginInfo.userId,
      queryParam:{
      	pageNum:1,
    		pageSize:5,
    		sortType:'1'
      },
      totalPage:0,
      loading:true,
      rule:"",
      showTop:true,
      showAllTop:false,
      errorImg: 'this.src="' + errorPic + '"',
    };
  },
  mounted(){
  	var isFirstVisit = sessionStorage.getItem("isFirstVisit");
  	if(isFirstVisit=='1'){
  		this.getReadRule();
  	}
  	this.getTopList();
		this.getContentList();
  	this.getUserInfo();
  	this.getHotTopicList();
  },
  methods: {
  	getUserInfo:function(){
  		api.getUserInfoAES(this.currentUserId).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.userInfo = res.data;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getTopList:function(){
  		var that = this;
  		api.getTopContent().then(response => {
    		var res = response.data;
				if(res.status == "0"){
					var topList= res.data;
					topList.forEach(function(item){
						item.topFlag=true;
		  			if(item.type=='2'){
		  				item.content = item.content.replace(/style=\"[^\"]*\"/g, "");
		  			}else{
		  				item.content = that.replaceURLWithHTMLLinks(item.content);
		  			}
		  		});
		  		this.topList = topList;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getContentList:function(){
  		var that = this;
  		this.loading = true;
  		api.getHotContent(this.queryParam).then(response => {
    		var res = response.data;
    		this.loading=false;
				if(res.status == "0"){
					var contentList= res.data.list;
					contentList.forEach(function(item){
						item.topFlag=false;
		  			if(item.type=='2'){
		  				item.content = item.content.replace(/style=\"[^\"]*\"/g, "");
		  			}else{
		  				item.content = that.replaceURLWithHTMLLinks(item.content);
		  			}
		  		});
		  		this.contentList = this.contentList.concat(contentList);
					this.totalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getHotTopicList:function(){
    	api.getHotTopic(this.hotTopicQueryParam).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.hotTopicList= res.data.list;
					this.hotTopicTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    changeHotTopicPage:function(){
  		if(this.hotTopicQueryParam.pageNum<this.hotTopicTotalPage){
    		this.hotTopicQueryParam.pageNum++;
    	}else{
    		this.hotTopicQueryParam.pageNum=1;
    	}
    	this.getHotTopicList();
    },
  	toWriteArticle() {
      this.$router.push({ path: "/writeArticle"});
    },
  	getChangeState:function(val){
  		if(val){
  			return val.charAt(0)=='+'?'add':(val.charAt(0)=='-'?'reduce':'');
  		}else{
  			return "0";
  		}
  	},
  	replaceURLWithHTMLLinks:function(text) {
		    var exp = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
		    return text.replace(exp,"<a style='color:#3E8CFF;' target='_blank' href='$1'><i class='el-icon-link fs-14 mgr-2'></i>网页链接</a>");
		},
  	handleClick:function(event){
			var sp=document.getElementById("emojiIcon");
			var sp1=document.getElementById("topicIcon");
			var sp2=document.getElementById("refereshHotTopic");
//		if(sp){
//			if(!sp.contains(event.target)){
//				if(document.getElementsByClassName("emoji-wrap").length>0){
//						var e = document.createEvent("MouseEvents");
//	    			e.initEvent("click", true, true);
//		  			document.getElementsByClassName("fa")[0].dispatchEvent(e);
//		  		}
//			}
//		}
			if(!sp1.contains(event.target)&&!sp2.contains(event.target)){
				this.$refs.knowledge.topListShowFlag=false;
			}
	  },
	  toTopicDetail(id){
	  	var routeUrl=this.$router.resolve({name:'topic',query: { id: id }});
      window.open(routeUrl.href, '_blank');
	  },
	  loadMore(){
    	if(this.queryParam.pageNum<this.totalPage){
    		this.queryParam.pageNum++;
    		this.getContentList();
    	}
    },
    updateFollowStatus(id){
    	var that = this;
    	this.contentList.forEach(function(item,index){
    		if(item.author==id){
    			that.$set(that.contentList[index], 'authorFollowFlag', !item.authorFollowFlag);
    		}
  		});
  		this.topList.forEach(function(item,index){
    		if(item.author==id){
    			that.$set(that.topList[index], 'authorFollowFlag', !item.authorFollowFlag);
    		}
  		});
  		this.getUserInfo();
    },
    refereshPage(){
	  	this.queryParam.pageNum=1;
	  	this.contentList=[];
	  	this.getContentList();
	  	this.getUserInfo();
	  },
	  toPerCenterTab(tab){
	  	// var routeUrl=this.$router.resolve({name:'perCenter',query:{id:this.$store.state.loginInfo.userId,tab:tab}});
	  	localStorage.setItem("forwordUser", this.$store.state.loginInfo.userId);
      var routeUrl=this.$router.resolve({name:'perCenter',query:{tab:tab}});
      window.open(routeUrl.href, '_blank');
	  },
	  getReadRule:function(){
  		api.queryDict('reading_rule').then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.rule=res.data.dictValue;
			  	this.showRule=true;
			  	sessionStorage.setItem("isFirstVisit", "2");
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	changeSortType(sortType){
  		this.queryParam.pageNum=1;
  		this.queryParam.sortType=sortType;
  		this.contentList=[];
  		this.getContentList();
  	},
  	setTop(contentObj){
  		this.queryParam.pageNum=1;
  		this.contentList=[];
  		this.getContentList();
  		this.getTopList();
  	}
  }
};
</script>
<style lang="scss">
	.rule-title{
		color: #3e8cff;
    margin-left: -30px;
    margin-bottom: 20px;
    font-size: 16px;
	}
	.rule-block{
		position: relative;
		.read-rule{
			background: #F4F9FF;
			color:#3E8CFF;
			text-align: left;
			font-size:12px;
		}
		.notice{
			position:absolute;
			top:3px;
			left:-30px;
		}
		.cancel{
			position:absolute;
			top:-40px;
			right:-30px;
			cursor: pointer;
		}
	}
	.readRule-dialog{
		.el-dialog__header{
			display:none;
		}
		.el-dialog__body{
			background: #F4F9FF;
			padding: 40px 60px;
		}
	}
	@keyframes fade-in {
    0% {
      color: #909199;
      font-size:13px;
    }

    100% {
      font-size:15px;
      color: #303133;
    }
	}
</style>
