<template>
  <div>
    <div class="frame" :class="getFrameClass()">
      <span class="triangle"></span>
      <!--内容编辑区-->
      <div :id="'editer_'+(id?id:'')" spellcheck="false" class="edit-area" contenteditable="true" @keyup="calcLength()" @paste="textInit"></div>
			<!--图片展示区-->	    
			<div v-show="pics.length>0">
	    	<div class="uoload">
	    		<div v-for="(file,index) in pics" :style="index==0?'':'margin-left:3px;'" class="picture-card">
	            <el-image
	            	v-if="file.path"
					      :src="file.path"
					      fit="fit"
					      :preview-src-list="getImgList()">
					    </el-image>
	            <i class="el-icon-error attach-delete-icon" @click.stop="removePicture(file,index)"></i>
		      </div>
	        <el-upload 
	        	:style="pics.length<9?'display:inline-block':'display:none'"
	        	class="fs-14 vt"
		      	accept=".gif,.jpg,.jpeg,.bmp,.png"
		       	:show-file-list="false"
		       	:file-list="fileList"
		       	:action="uploadPath"
		       	:on-success="handleSuccess"
		       	:limit="9"
						:on-exceed="handleExceed"
						:before-upload="beforeUpload"
		      	multiple
		      >
		      <div slot="default" id="picUploadButton" class="el-upload--picture-card" style="margin-left:3px;">
		      	<i class="el-icon-plus"></i>
		      </div>
	        </el-upload>
	      </div>
	      <div class="limit_num">还可以上传{{9-pics.length}}张（最多上传9张）</div>
      </div>
      <!--视频展示区-->
      <template v-if="videoUploadPercentShowFlag||audio.path">
      	<div class="video_upload">
      		<div v-if="audio.path && !videoUploadPercentShowFlag" style="position:relative;height:100%;">
	          <video
	          	v-if="audio.path"
	            :src="audio.path"
	            style="width:100%;height:100%;object-fit:fill;"
	          >
	          </video>
	          <i class="el-icon-error attach-delete-icon" @click.stop="removeAudio(audio)"></i>
	          <i class="video-play-icon" @click.stop="playAudio(audio.path)"></i>
          </div>
          <el-progress
            :width="50"
            v-if="videoUploadPercentShowFlag == true"
            type="circle"
            :percentage="videoUploadPercent"
            class="vm"
          ></el-progress>
        </div>
      </template>
      <!--附件展示区-->	    
			<div class="mgb-10 inline-block" v-show="attaches.length>0">
    		<div v-for="(att,index) in attaches" class="att-block">
          <a :href="att.path">{{att.name}}</a><i class="el-icon-circle-close mgl-5" @click.stop="removeAttach(att,index)"></i>
	      </div>
	      <div v-for="(att,index) in uploadingAttach" class="att-block" v-if="!att.completed">
	      	<span><i class="el-icon-loading mgr-5"></i><span class="color-9 fs-10">上传中&nbsp;{{att.uploadProgress}}</span></span>
	      </div>
      </div>
      <!--图片放大/视频播放弹框-->
      <el-dialog :visible.sync="dialogVisible" width="700px">
      	<div style="width:100%;height:400px;" class="tc">
	        <videoComponent v-if="dialogType=='audio'" style="width:100%;height:100%;" :id="'video'" :url="dialogUrl"></videoComponent>
      	</div>
      </el-dialog>
      <div class="image" @mousedown="preventBlur($event)">
        <!--表情包-->
        <emoji-icon class="vm pointer mgr-10" id="emojiIcon" style="margin-bottom:-4px;" @select="selectIcon" :iconConfig="iconConfig"></emoji-icon>
        <!--图片上传-->
	    	<img class="vm pointer" src="../../../assets/image/chart2.png" alt="" @click="uploadPicCheck"/>
	    	<!--视频上传-->
	    	<el-upload
        	style="display:none;"
	      	accept=".mpg,.m4v,.mp4,.flv,.3gp,.mov,.avi,.rmvb,.mkv,.wmv"
	      	:show-file-list="false"
	       	:action="uploadPath"
	       	:on-success="handleVideoSuccess"
	        :on-progress="uploadVideoProcess"
          :before-upload="beforeUploadVideo">
	       	<el-button size="mini" id="audioUploadButton" type="primary"></el-button>
	    	</el-upload>
	    	<img class="vm pointer" src="../../../assets/image/chart3.png" alt="" @click="uploadAudioCheck"/>
        <!--话题添加-->
        <img id="topicIcon" v-if="!inTopic" class="vm pointer" @click="topListShowFlag=!topListShowFlag;changeHotTopicPage(true);" src="../../../assets/image/chart4.png" alt="" />
        <!--附件上传-->
		    <uploaderChunk 
		    	:type="'1'" 
		    	:tipsId="knowledgeId"
		    	@handleAttachSuccess="handleAttachSuccess" 
		    	@changeAttachProgress="changeAttachProgress"
		    ></uploaderChunk>
        <el-button class="fr fs-13 mgr-5" type="text" v-show="!editAreaIsEmpty" @click="publishKnowledge">发布</el-button>
        <div id="topicDialog" class="frame_theme" v-show="topListShowFlag" ><div class="theme_list">
          <ul>
            <li v-for="(item, index) in topicList" :key="index" @click="addTopic(item.id,item.topic)">
              <span :title="'#'+item.topic+'#'" class="overflow-deal" style="width:70%">#{{ item.topic }}#</span>
              <span>{{ item.discussCount }}发起</span>
            </li>
          </ul>
          <div id="refereshHotTopic" v-show="topicTotalPage>1" class="pointer tc" @click="changeHotTopicPage(false)">
            <span>换一批</span>
            <img src="../../../assets/image/referesh.png" alt="" />
          </div>
          <p class="tip mgt-10">没有想要的话题？试着<span class="pointer" @click="newTopic()">新建话题</span> 吧…</p>
        </div></div> 
      </div>
    </div>
  </div>
</template>

<script>
import videoComponent from '../../video/video';
import uploaderChunk from '../../uploader/uploader-chunk';
import httpCore from "@/api/httpCore";
const api = new httpCore();
export default {
  name: "KnowledgePublish",
  props: ['inTopic','topicInfo','id'],
  components:{videoComponent,uploaderChunk},
  data() {
    return {
    	knowledgeId:'',
	    iconConfig: {
		    placement: 'bottom',
		    size: '30px',
		    name: 'chart1-icon',
		    color: '#fff'
		  },
		  editAreaIsEmpty:true,
		  uploadPath:'/file-upload-service/v1/paas_attachments_tool/upload/lezhi-service',
		  downloadPath:'/file-upload-service/v1/paas_attachments_tool/download?id=',
		  pics:[],
		  dialogUrl: "",
      dialogVisible: false,
      dialogType:"",
		  audio:{},
		  videoUploadPercentShowFlag: false,
		  videoUploadPercent: "",
		  topicList:[],
		  topListShowFlag:false,
		  hotTopicQueryParam:{
      	pageNum:1,
    		pageSize:10,
      },
      topicTotalPage:0,
      editAreaId:'editer_'+(this.id?this.id:''),
      fileList:[],
      attaches:[],
      uploadingAttach:[],
      tipPromise:Promise.resolve(),
      attachDialog:{
      	visible:false
      }
    };
  },
  watch:{
  	'topicInfo' (val) {
		  if(val.id){
		  	if(this.inTopic){
		  		this.addTopic(this.topicInfo.id,this.topicInfo.topic);
		  	}
		  }
		}
  },
  mounted(){
  	if(this.id){
  		this.knowledgeId=this.id;
  		this.getKnowledgeContent();
  		this.editAreaIsEmpty=false;
  	}else{
  		this.getKnowledgeId();
  	}
  	this.getTopicList();
  },
  methods: {
  	getKnowledgeId:function(){
  		api.getKnowledgeId().then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.knowledgeId = res.data;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	calcLength:function(){
  		var html=document.getElementById(this.editAreaId).innerHTML;
  		var content=document.getElementById(this.editAreaId).innerText.trim();
  		this.editAreaIsEmpty = (content==null || content=='') && (html==null || html.indexOf("<img")<0) ;
  	},
  	preventBlur:function(event){
  		event.preventDefault();
  	},
  	_insertHtml:function(str){
  		let id = this.editAreaId;
			let acitveId = document.activeElement.id;
			var selection= window.getSelection ? window.getSelection() : document.selection;
			if(id != acitveId){
				var range = document.createRange();
				range.selectNodeContents(document.getElementById(this.editAreaId));
				range.collapse(false);
				selection.removeAllRanges();   
				selection.addRange(range);
			}
  		document.getElementById(this.editAreaId).focus();
			var range= selection.createRange ? selection.createRange() : selection.getRangeAt(0);
			if (!window.getSelection){
				range.pasteHTML(str);
				range.collapse(false);
				range.select();
			}else{
				range.collapse(false);
				var hasR = range.createContextualFragment(str);
				var hasR_lastChild = hasR.lastChild;
				while (hasR_lastChild && hasR_lastChild.nodeName.toLowerCase() == "br" && hasR_lastChild.previousSibling && hasR_lastChild.previousSibling.nodeName.toLowerCase() == "br") {
					var e = hasR_lastChild;
					hasR_lastChild = hasR_lastChild.previousSibling;
					hasR.removeChild(e);
				}                                
				range.insertNode(hasR);
				if (hasR_lastChild) {
					range.setEndAfter(hasR_lastChild);
					range.setStartAfter(hasR_lastChild);
				}
				selection.removeAllRanges();
				selection.addRange(range);
			}
		},
		/*文本复制格式化*/
    textInit:function(e) {
      e.preventDefault();
      var text;
      var clp = (e.originalEvent || e).clipboardData;
      if (clp === undefined || clp === null) {
        text = window.clipboardData.getData("text") || "";
        if (text !== "") {
          if (window.getSelection) {
            var newNode = document.createElement("span");
            newNode.innerHTML = text;
            window.getSelection().getRangeAt(0).insertNode(newNode);
          } else {
            document.selection.createRange().pasteHTML(text);
          }
        }
      } else {
        text = clp.getData('text/plain') || "";
        if (text !== "") {
          document.execCommand('insertText', false, text);
        }
      }
    },
  	selectIcon(img){
  		img = img.replace(">"," style='vertical-align:text-top;'>")
      this._insertHtml("<span>"+img+"</span>");
      this.calcLength();
  	},
  	uploadPicCheck(){
  		var e = document.createEvent("MouseEvents");
	    e.initEvent("click", true, true);
  		if(this.audio.path){
  			this.$confirm('此操作将删除已上传的视频, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          document.getElementById("picUploadButton").dispatchEvent(e);
        });
  		}else{
	      document.getElementById("picUploadButton").dispatchEvent(e);
  		}
    },
    delAttachByType(type){
    	api.delAttach(this.knowledgeId,{type:type}).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					if(type=='1'){
						this.pics=[];
						this.fileList=[];
					}else{
						this.audio={};
					}
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    beforeUpload(file) {
      this.delAttachByType("2");
    },
  	handleSuccess(resp,file,fileList){
  		this.fileList=fileList;
  		var pic = {
  			id: resp.data,
  			name: file.name,
  			path: this.downloadPath+resp.data,
  			tipsId:this.knowledgeId,
  			type:"1",
  			size:file.size
  		};
  		api.addAttach(pic).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.pics.push(pic);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 9张图片，本次选择了 ${files.length} 个文件，共选择了 ${files.length + this.pics.length} 个文件`);
    },
		removePicture(file,index){
			api.delAttach(file.tipsId,{id:file.id,type:'1'}).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.pics.splice(index,1);
					this.fileList.splice(index,1);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
		},
  	getFrameClass(){
  		if(this.pics.length>0){
  			return "frame_image";
  		}else if(this.videoUploadPercentShowFlag||this.audio.path){
  			return "frame_video";
  		}
  	},
  	
    beforeUploadVideo(file) {
      var fileSize = file.size / 1024 / 1024 < 300;
      if (!fileSize) {
        this.$message({
          message: '视频大小不能超过300MB',
          type: 'warning'
        });
        return false;
      }
      this.delAttachByType("2");
      this.delAttachByType("1");
    },
    //进度条
    uploadVideoProcess(event, file, fileList) {
      this.videoUploadPercentShowFlag = true;
      this.videoUploadPercent = parseInt(file.percentage);
    },
    //上传成功回调
    handleVideoSuccess(res, file) {
      this.videoUploadPercentShowFlag = false;
      
      if (res.code == "SUCCESS") {
      	var audio = {
      		id: res.data,
	  			name: file.name,
	  			path: this.downloadPath+res.data,
	  			tipsId:this.knowledgeId,
	  			type:"2",
	  			size:file.size
	  		};
	  		api.addAttach(audio).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.audio=audio;
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
      }
    },
    removeAudio:function(audio){
    	api.delAttach(audio.tipsId,{id:audio.id,type:'2'}).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.audio={};
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    playAudio(url){
  		this.dialogUrl=url;
  		this.dialogVisible = true;
  		this.dialogType="audio";
  	},
  	uploadAudioCheck(){
  		var e = document.createEvent("MouseEvents");
	    e.initEvent("click", true, true);
  		if(this.audio.path){
  			this.$confirm('此操作将删除已上传的视频, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          document.getElementById("audioUploadButton").dispatchEvent(e);
        });
  		}else if(this.pics.length>0){
  			this.$confirm('此操作将删除已上传的图片, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          document.getElementById("audioUploadButton").dispatchEvent(e);
        });
  		}else{
	      document.getElementById("audioUploadButton").dispatchEvent(e);
  		}
    },
    getTopicList:function(){
    	api.getHotTopic(this.hotTopicQueryParam).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.topicList= res.data.list;
					this.topicTotalPage = res.data.pages;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    changeHotTopicPage:function(toFistPage){
    	if(toFistPage){
    		this.hotTopicQueryParam.pageNum=1;
    	}else{
    		if(this.hotTopicQueryParam.pageNum<this.topicTotalPage){
	    		this.hotTopicQueryParam.pageNum++;
	    	}else{
	    		this.hotTopicQueryParam.pageNum=1;
	    	}
    	}
    	this.getTopicList();
    },
    addTopic:function(id,name){
    	var topic = "<span class='topic' topicId='" + id + "' contentEditable='false' style='color:#3E8CFF'><font class='topic-mark'>#</font>" + name + "<font class='topic-mark'>#</font></span><span>&nbsp;</span>";
      this._insertHtml(topic);
      this.editAreaIsEmpty=false;
    },
    newTopic:function(){
      this.$prompt('请输入话题名称', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
      	if(value){
      		api.addTopic({topic:value}).then(response => {
		    		var res = response.data;
						if(res.status == "0"){
							var topic = "<span class='topic' topicId='" + id + "' contentEditable='false' style='color:#3E8CFF'><font class='topic-mark'>#</font>" + value + "<font class='topic-mark'>#</font></span><span>&nbsp;</span>";
		    			var sel = window.getSelection();
							var range = document.createRange();
							range.selectNodeContents(document.getElementById(this.editAreaId));
							range.collapse(false);
							sel.removeAllRanges();   
							sel.addRange(range);
		    			this._insertHtml(topic);
		    			this.editAreaIsEmpty=false;
						}else {
							this.$message({message: res.msg ,type: 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
      	}
      });
    },
    getImgList:function(){
    	return this.pics.map(function(item){
	    	return item.path;
	    });
    },
    publishKnowledge:function(){
    	var content = document.getElementById(this.editAreaId).innerHTML;
    	var data={id:this.knowledgeId,content:content};
    	api.addKnowledge(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.getKnowledgeId();
					document.getElementById(this.editAreaId).innerHTML="";
					this.pics=[];
					this.fileList=[];
					this.audio={};
					this.attaches=[];
					this.$message({message: "知识发布成功" ,type: 'success'});
					if(this.inTopic){
						this.addTopic(this.topicInfo.id,this.topicInfo.topic);
						this.$emit('refereshPage');
					}else if(this.id){
						this.$emit('refereshPage',res.data);
					}else{
						this.$emit('refereshPage');
					}
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
    getKnowledgeContent(){
    	api.getKnowledgeContent(this.knowledgeId).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					document.getElementById(this.editAreaId).innerHTML=res.data.content;
					this.pics = res.data.tipsImages;
					var that = this;
					this.pics.forEach(function(item){
						var fileObj={id:item.id,uid:item.id,size:item.size,percentage:100,url:item.path,name:item.name};
			   	  that.fileList.push(fileObj);
					});
					this.audio = res.data.tipsVideo;
					this.attaches = res.data.tipsAttach;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
    },
  	removeAttach(file,index){
			api.delAttach(file.tipsId,{id:file.id,type:'5'}).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.attaches.splice(index,1);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
		},
		changeAttachProgress(obj){
			var num = this.uploadingAttach.filter(item => item.uniqueIdentifier==obj.uniqueIdentifier).length;
			if(num==0){
				this.uploadingAttach.push(obj);
			}else{
				this.uploadingAttach.forEach(function(item){
					if(item.uniqueIdentifier==obj.uniqueIdentifier){
						item.uploadProgress = obj.uploadProgress;
						item.completed = obj.completed;
					}
				});
			}
		},
		handleAttachSuccess(att){
			this.attaches.push(att);
  	}
  },
};
</script>
<style lang="scss">
	.chart1-icon{
	  width:14px;
	  height:14px;
	  background: url(../../../assets/image/chart1.png) no-repeat;
	  background-size: 100% 100%;
	}
	.emoji-wrap.emoji-bottom{
		left:-76px !important;
		>div{
			width:100%;
			.emoji-container:nth-child(1){
				width:76px;
			}
			.emoji-container:nth-child(2){
				width:calc(100% - 76px);
			}
		}
	}
	.attach-delete-icon{
		color: #000000;
		opacity: 0.7;
		position:absolute;
		right:2px;
		top:2px;
		content:'';
		font-size:12px;
		cursor: pointer;
	}
	.el-message__content{
		font-size:13px;
	}
	.el-dialog__body {
    padding: 20px 20px;
  }
  .emoji-wrap:before{
  	content: '';
    width: 8px;
    height: 8px;
    display: inline-block;
    position: absolute;
    top: -4px;
    left: 78px;
    background: #fff;
    border-bottom: 1px solid rgba(192,196,204,.6);
    border-right: 1px solid rgba(192,196,204,.6);
    transform: rotate(225deg);
    z-index: 9999;
  }
  .video-play-icon{
		color: white;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    content: '';
    background:url(../../../assets/image/audio-play.png);
    width:30px;
    height:30px;
    background-size: cover;
    display:block;
	}
	
	
</style>
