<template>
	<div class="article_content" :class="contentObj.topFlag?'top_article':''">
    <div class="top_content">
      <div class="user_infor">
        <div class="image">
          <img @click="toPerCenter(contentObj.author)" v-if="contentObj.authorHeadImg" :src="contentObj.authorHeadImg" alt="" :onerror="errorImg"/>
        </div>
        <div class="user">
          <div class="name">{{contentObj.authorName}}</div>
          <div class="sign">
            <span>{{contentObj.cteTime|fomatTime()}}</span>
            <span>来自</span>
            <span>{{contentObj.authorDept}}</span>
          </div>
        </div>
      </div>
      <div class="follow" v-if="currentUser!=contentObj.author" @click="follow()" :class="contentObj.authorFollowFlag?'already_follow':''">{{contentObj.authorFollowFlag?'已关注':'关注'}}</div>
    </div>

    <div class="middle_content" v-if="contentObj.type=='2'">
    	<div @click="toArticleDetail(contentObj.id)">
	      <!-- <div class="article_title" v-html="contentObj.title"></div> -->
	      <div class="article_title" v-dompurify-html="contentObj.title"></div>
	      <div class="article">
	      	<articleComponent :contentObj="contentObj"></articleComponent>
	      </div>
    	</div>
    </div>
    <div class="knowledge_content" v-else-if="contentObj.type=='1'">
      <!-- <div :id="'content-know1'+sort" v-show="allShow" class="content" v-html="contentObj.content"></div> -->
      <div :id="'content-know1'+sort" v-show="allShow" class="content" v-dompurify-html="contentObj.content"></div>
    	<!-- <div :id="'content-know2'+sort" v-show="!allShow" class="content" v-html="contentObj.content" v-clampy="4"></div> -->
    	<div :id="'content-know2'+sort" v-show="!allShow" class="content" v-dompurify-html="contentObj.content" v-clampy="4"></div>
      <div v-show="allButtonShow" style="margin-top: 10px;overflow: hidden;">
      	<a style="color:#3E8CFF;"  class="fr pointer mgr-10" @click="allShow=true;allButtonShow=false;">展示全文</a>
      </div>

      <div v-if="contentObj.tipsImages.length>0" class="picture" :style="contentObj.tipsImages.length==9?'width:90%;':(contentObj.tipsImages.length>=6?'width:90%;':'')">
      	<div v-for="(pic,index) in contentObj.tipsImages">
      		<el-image
			      :style="contentObj.tipsImages.length==9?'height:150px;width:150px':(contentObj.tipsImages.length>=6?'height:120px;width:120px':'height:100px;width:100px')"
			      v-if="pic.path"
			      :src="pic.path"
			      fit="cover"
			      :preview-src-list="picList">
			    </el-image>
      	</div>
      </div>
      <div v-if="contentObj.tipsVideo.path">
      	<div class="video">
      		<videoComponent style="height:236px;width:100%" :id="'video_' + sort" :url="contentObj.tipsVideo.path"></videoComponent>
        </div>
      </div>
			<div class="mgt-15" v-if="contentObj.tipsAttach.length>0">
				<div class="color-9"><i class="mgr-5 el-icon-paperclip"></i>附件信息：</div>
    		<div v-for="(att,index) in contentObj.tipsAttach" class="att-block">
          <a :href="att.path">{{att.name}}</a>
	      </div>
      </div>
    </div>
    <div class="bottom_content">
      <div class="pointer" @click="goComment(contentObj.type)">
        <img src="../../../assets/image/art1.png" alt="" />{{contentObj.commentCount}}评论
      </div>
      <div class="pointer" @click="recommend(contentObj.type,contentObj.id)"><img :src="require(`../../../assets/image/recommend_${contentObj.recommendFlag}.png`)" alt="" />{{contentObj.recommendCount}}推荐</div>
      <div class="pointer" @click="collect(contentObj.type,contentObj.id)"><img :src="require(`../../../assets/image/collect_${contentObj.collectionFlag}.png`)" alt="" />{{contentObj.collectionCount}}收藏</div>
      <div class="pointer" v-if='highManagePower' @click="top()"><i class="iconfont icon-zhiding"></i>{{contentObj.topCount}}置顶</div>
      <div v-if="showSetTop" class="pointer" @click="setTop(contentObj.type,contentObj.id)"><img style="width:13px;margin-top: -2px;" :src="require(`../../../assets/image/top_${contentObj.topFlag}.png`)" alt="" />置顶</div>
      <!--<div class="pointer" @click="cancelTop(contentObj.type,contentObj.id)"><img style="width:13px;margin-top: -2px;" :src="require(`../../../assets/image/top_${contentObj.topFlag}.png`)" alt="" />取消置顶</div>-->
    	<div @click="requestTrain" class="train-request pointer"><img src="../../../assets/image/train-request.png" style="width:20px"/>培训申请</div>
    </div>
    <commentComponent v-if="commentShow" :type="'knowledge'" :id="contentObj.id" :author="contentObj.author" @updateCommentCount="updateCommentCount"></commentComponent>
  </div>
</template>


<script>
	import commentComponent from '../../comment/comment';
	import videoComponent from '../../video/video';
	import articleComponent from '../../article/article';
	import httpCore from "@/api/httpCore";
	import errorPic from "@/assets/image/errorPic.png";
  import httpNew from "@/api/httpNew";
  const apiNew = new httpNew();
	const api = new httpCore();
	export default {
    data(){
      return{
	      allShow:false,
	      allButtonShow:false,
	      picList:[],
	      commentShow:false,
	      currentUser:this.$store.state.loginInfo.userId,
	      errorImg: 'this.src="' + errorPic + '"',
        highManagePower: false
      }
    },
    props:['contentObj','sort','showSetTop'],
    components:{commentComponent,videoComponent,articleComponent},
    mounted(){
    	if(this.contentObj.type=='1'){
    		this.$nextTick(() => {
	    		this.checkContent();
	    	});
	    	this.picList = this.contentObj.tipsImages.map(function(item){
	    		return item.path;
	    	});
    	}
      this.handleGetHighManagePower()
    },
    methods:{
    	goComment(type){
    		if(type=='1'){
    			this.commentShow=!this.commentShow;
    		}else{
    			var routeUrl=this.$router.resolve({name:'articleDetail',query: { id: this.contentObj.id,goComment: true }});
      		window.open(routeUrl.href, '_blank');
    		}
    	},
	  	checkContent(){
        var partHtml=document.getElementById('content-know2'+this.sort).innerHTML;
	  		var allHtml=document.getElementById('content-know1'+this.sort).innerHTML;
	  		if(allHtml == partHtml){
	  			this.allButtonShow = false;
	  		}else{
	  			this.allButtonShow =  true;
	  		}
	  	},
	  	updateCommentCount(num){
	  		this.contentObj.commentCount=num;
	  	},
	  	toArticleDetail(id){
	  		var routeUrl=this.$router.resolve({name:'articleDetail',query: { id: id}});
	  		window.open(routeUrl.href, '_blank');
	  	},
	  	recommend(type,id){
    		var data={
    			recommendId:id,
    			recommendType:type
    		};
    		api.recommend(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.contentObj.recommendFlag=!this.contentObj.recommendFlag;
						this.contentObj.recommendFlag?this.contentObj.recommendCount++:this.contentObj.recommendCount--;
						this.$message({message: res.msg ,type: 'success'});
						this.$emit('getUserInfo');
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	},
    	collect(type,id){
    		var data={
    			collectionId:id,
    			collectionType:type
    		};
    		api.collect(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.contentObj.collectionFlag=!this.contentObj.collectionFlag;
						this.contentObj.collectionFlag?this.contentObj.collectionCount++:this.contentObj.collectionCount--;
						this.$message({message: res.msg ,type: 'success'});
						this.$emit('getUserInfo');
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	},
      top(){

      },
    	toPerCenter(id) {
	      // var routeUrl=this.$router.resolve({name:'perCenter',query: { id: id }});
        localStorage.setItem("forwordUser", id);
        var routeUrl=this.$router.resolve({name:'perCenter'});
	      window.open(routeUrl.href, '_blank');
	    },
	    follow:function(){
	  		var data={
					followId:this.contentObj.author,
					followType:'1'
				};
	    	api.follow(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
	      		this.$emit("updateFollowStatus",this.contentObj.author);
	      		this.$message.success(res.msg);
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	setTop(type,id){
	  		var data={
					topId:id,
					topType:type
				};
	    	api.setTop(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.contentObj.topFlag=!this.contentObj.topFlag;
	      		this.$emit("setTop",this.contentObj);
	      		this.$message.success(res.msg);
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	requestTrain(){
	  		var title = '';
	  		var content = '';
	  		if(this.contentObj.type=='1'){
	  			let reg=/<\/?.+?\/?>/g;
    			var tmp = this.contentObj.content.replace(reg,'');
    			tmp = tmp.replace(/&nbsp;/g,' ');
	  			title = tmp.length>=50?tmp.substr(0,50)+"...":tmp;
	  		}else{
	  			let reg=/<\/?.+?\/?>/g;
    			var tmp = this.contentObj.title.replace(reg,'');
	  			title = tmp;
	  		}
	  		var data={trainingId:this.contentObj.id,title:title,type:this.contentObj.type};
		  	api.addNotice({noticeType:'2',noticeContent: JSON.stringify(data)}).then(response => {
		  		var res = response.data;
					if(res.status == "0"){
						if(res.data=='1'){
							this.$message({'message': '您已申请过，无法重复申请' ,'type': 'warning'});
						}else{
							this.$message({'message': '申请成功' ,'type': 'success'});
						}
					}else {
						this.$message({'message': res.msg ,'type': 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
      cancelTop(id, type){
        var data={
          topId:id,
          topType:type
        };
        api.cancelTop(data).then(response => {
          var res = response.data;
          if(res.status == "0"){
            this.contentObj.topFlag=!this.contentObj.topFlag;
            this.$emit("setTop",this.contentObj);
            this.$message.success(res.msg);
          }else {
            this.$message({message: res.msg ,type: 'error'});
          }
        }).catch(err => {
            console.log(err);
          });
      },
      handleGetHighManagePower(){
        apiNew.getHighManagePower().then(response => {
          var res = response.data;
          if(res.status == "0"){
            this.highManagePower = res.data;
          }else {
            this.$message({message: res.msg ,type: 'error'});
          }
        }).catch(err => {
          console.log(err);
        });
      },
    }
  }
</script>

<style>
	.top_article{
		background: #fbfbfb;
	}
</style>
