<template>
  <div>
    <NavComp/>
    <el-card class="pdt-60 box-card">
      <div slot="header" class="clearfix">
        <el-row :gutter="14">
          <el-col :span="14" :offset="5">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="flex: 1; text-align: center;">
                <h2>话题小知识推荐管理</h2>
              </div>
              <div>
                <el-button type="primary" plain @click="handleOpenAddDialog">新增</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="14">
        <el-col :span="14" :offset="5">
          <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
              fixed
              prop="id"
              label="ID"
              width="80">
            </el-table-column>
            <el-table-column
              prop="tip"
              label="小知识"
              width="200">
            </el-table-column>
            <el-table-column
              prop="content"
              label="推荐内容"
              width="300">
            </el-table-column>
            <!--<el-table-column
              prop="authorName"
              label="作者"
              width="80">
            </el-table-column>-->
            <el-table-column
              prop="status"
              label="状态"
              width="60">
            </el-table-column>
            <el-table-column
              prop="cteTime"
              label="创建时间"
              width="120">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作">
              <template slot-scope="scope">
                <el-button @click="detail(scope.row)" type="text" size="small">详情</el-button>
                <!--<el-button @click='handleAddArtTipRecommend(scope.row)' type="text" size="small">新增推荐</el-button>-->
                <el-button @click='handleRemoveArtTipRecommend(scope.row)' type="text" size="small">删除推荐</el-button>
                <el-button @click='handleUpdateArtTipRecommend(scope.row)' type="text" size="small">更新推荐</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-dialog title="新增推荐" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="推荐小知识" :label-width="formLabelWidth" :style="{width: '40%'}">
            <el-select v-model="form.tipId" placeholder="请选择小知识" >
              <el-option v-for='item in tips()' :key='item.id' :label="item.content" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="推荐内容" :label-width="formLabelWidth">
            <el-input v-model="form.content" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleAddArtTipRecommendSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="编辑推荐" :visible.sync="editDialogFormVisible">
        <el-form :model="form">
          <el-form-item label="内容" :label-width="formLabelWidth">
            <el-input v-model="form.content" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="状态" :label-width="formLabelWidth" :style="{width: '40%'}">
            <el-select v-model="form.status" placeholder="请选择状态" >
              <el-option label="删除" value="删除"></el-option>
              <el-option label="正常" value="正常"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmitEditFormSubmit">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 详情弹窗 -->
      <el-dialog title="推荐详情" :visible.sync="detailDialogVisible" width="70%" top="5vh">
        <div class="detail-content">
          <div class="detail-section">
            <div class="detail-item">
              <span class="label">推荐ID：</span>
              <span class="value">{{ artTipsDetail.id }}</span>
            </div>
            <div class="detail-item">
              <span class="label">推荐内容：</span>
              <span class="value content-text">{{ artTipsDetail.content }}</span>
            </div>
            <div class="detail-item">
              <span class="label">小知识：</span>
              <span class="value tip-content">{{ artTipsDetail.tip }}</span>
            </div>
            <div class="detail-item">
              <span class="label">作者：</span>
              <span class="value">{{ artTipsDetail.authorName }}</span>
            </div>
            <div class="detail-item">
              <span class="label">状态：</span>
              <span class="value" :class="artTipsDetail.status === '正常' ? 'status-normal' : 'status-other'">{{ artTipsDetail.status }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ artTipsDetail.cteTime }}</span>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import httpNew from '@/api/httpNew';
import {artTipsRecommends, topics, tips, getRandomNumber} from './data';
import NavComp from './navComp.vue';

const api = new httpNew();

export default {
  name: 'artTipsRecommend',
  components: {NavComp},
  data() {
    return {
      tableData: [],
      localArtTipsRecommends: JSON.parse(JSON.stringify(artTipsRecommends)), // 深拷贝推荐数据
      artTipsDetail: {},
      dialogFormVisible: false,
      editDialogFormVisible: false,
      detailDialogVisible: false,
      form: {
        id: '',
        content: '',
        topicId: '',
        tipId: '',
        status: '正常',
      },
      formLabelWidth: '120px',
    };
  },
  mounted() {
    this.handleGetTipsRecommend();
  },
  methods: {
    // 获取当前时间的格式化字符串
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    tips() {
      return tips
    },
    async handleGetTipsRecommend() {
      // 保留API调用但不依赖其结果
      try {
        let res = await api.getArtTipRecommendList();
        console.log('推荐列表API调用成功', res);
      } catch (err) {
        console.log('推荐列表API调用失败', err);
      }

      // 使用本地数据
      this.tableData = [...this.localArtTipsRecommends];
    },
    handleOpenAddDialog(){
      // 重置表单
      this.form = {
        id: '',
        content: '',
        topicId: '',
        tipId: '',
        status: '正常',
      };
      this.dialogFormVisible = true;
    },
    detail(row) {
      // 先执行本地操作
      const recommend = this.localArtTipsRecommends.find(item => item.id === row.id);
      if (recommend) {
        this.artTipsDetail = {...recommend};
        this.detailDialogVisible = true; // 打开详情弹窗
      } else {
        this.$message.error("详情查询失败");
        return;
      }

      // 保留API调用但不依赖其结果
      api.getArtTipsDetail(row.id).then(res => {
        console.log('详情API调用成功', res);
      }).catch(err => {
        console.log('详情API调用失败', err);
      });
    },
    // 新增推荐提交方法
    handleAddArtTipRecommendSubmit() {
      // 表单验证
      if (!this.form.tipId) {
        this.$message.error("请选择要推荐的小知识");
        return;
      }
      if (!this.form.content || this.form.content.trim() === '') {
        this.$message.error("请输入推荐内容");
        return;
      }

      this.handleAddArtTipRecommend();
      this.dialogFormVisible = false; // 关闭弹窗
    },

    handleAddArtTipRecommend() {
      // 找到对应的tip内容
      const selectedTip = tips.find(tip => tip.id === this.form.tipId);

      // 生成新的推荐对象
      const newRecommend = {
        id: getRandomNumber(10000, 99999),
        content: this.form.content.trim(),
        authorName: '当前用户', // 可以根据实际情况修改
        status: this.form.status || '正常',
        tip: selectedTip ? selectedTip.content : '未知小知识',
        cteTime: this.getCurrentTime()
      };

      // 先执行本地操作
      this.localArtTipsRecommends.unshift(newRecommend);
      this.handleGetTipsRecommend(); // 刷新表格数据
      this.$message.success('新增推荐成功');

      // 保留API调用但不依赖其结果
      api.addArtTipRecommend(this.form).then(res => {
        console.log('新增推荐API调用成功', res);
      }).catch(err => {
        console.log('新增推荐API调用失败', err);
      });
    },
    handleRemoveArtTipRecommend(row) {
      // 添加二次确认弹窗
      this.$confirm('此操作将永久删除该推荐, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认删除，执行本地操作
        const index = this.localArtTipsRecommends.findIndex(item => item.id === row.id);
        if (index !== -1) {
          this.localArtTipsRecommends.splice(index, 1); // 从数组中删除
          this.handleGetTipsRecommend(); // 刷新表格数据
          this.$message.success('删除推荐成功');

          // 保留API调用但不依赖其结果
          api.removeArtTipRecommend(row.id).then(res => {
            console.log('删除推荐API调用成功', res);
          }).catch(err => {
            console.log('删除推荐API调用失败', err);
          });
        } else {
          this.$message.error('未找到要删除的推荐');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handleUpdateArtTipRecommend(row) {
      // 使用深拷贝避免数据引用问题
      this.form = {
        id: row.id,
        content: row.content,
        authorName: row.authorName,
        status: row.status,
        tip: row.tip,
        cteTime: row.cteTime
      };
      this.editDialogFormVisible = true;
    },

    // 编辑推荐提交方法
    handleSubmitEditFormSubmit() {
      // 表单验证
      if (!this.form.content || this.form.content.trim() === '') {
        this.$message.error("请输入推荐内容");
        return;
      }
      if (!this.form.status) {
        this.$message.error("请选择状态");
        return;
      }

      this.handleSubmitEditForm();
      this.editDialogFormVisible = false; // 关闭弹窗
    },

    handleSubmitEditForm() {
      // 先执行本地操作
      const index = this.localArtTipsRecommends.findIndex(item => item.id === this.form.id);
      if (index !== -1) {
        // 更新本地数据
        this.localArtTipsRecommends[index] = {
          ...this.localArtTipsRecommends[index],
          content: this.form.content.trim(),
          status: this.form.status
        };
        this.handleGetTipsRecommend(); // 刷新表格数据
        this.$message.success('更新推荐成功');
      } else {
        this.$message.error('未找到要更新的推荐');
        return;
      }

      // 保留API调用但不依赖其结果
      api.updateArtTipRecommend(this.form).then(res => {
        console.log('更新推荐API调用成功', res);
      }).catch(err => {
        console.log('更新推荐API调用失败', err);
      });
    }
  }
};
</script>
<style scoped>
/* 详情弹窗样式 */
.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.detail-item .label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.content-text {
  background-color: #F5F7FA;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.tip-content {
  background-color: #FDF6EC;
  color: #E6A23C;
  padding: 6px 10px;
  border-radius: 4px;
  font-weight: 500;
}

.status-normal {
  color: #67C23A;
  font-weight: 600;
}

.status-other {
  color: #F56C6C;
  font-weight: 600;
}
</style>
