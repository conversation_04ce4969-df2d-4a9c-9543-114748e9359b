<template>
  <div>
    <NavComp/>
    <el-card class="pdt-60 box-card">
      <div slot="header" class="clearfix">
        <el-row :gutter="14">
          <el-col :span="14" :offset="5">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="flex: 1; text-align: center;">
                <h2>系统公告管理</h2>
              </div>
              <div>
                <el-button type="primary" plain @click="handleOpenAddDialog">新增</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="14">
        <el-col :span="14" :offset="5">
          <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
              fixed
              prop="id"
              label="ID"
              width="150">
            </el-table-column>
            <el-table-column
              prop="content"
              label="内容">
            </el-table-column>
            <el-table-column
              prop="cteTime"
              label="创建时间">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="160">
              <template slot-scope="scope">
                <el-button @click="handleGetAnnouncement(scope.row.id)" type="text" size="small">查看</el-button>
                <el-button @click='handleOpenEditDialog(scope.row)' type="text" size="small">编辑</el-button>
                <el-button @click='handleDeleteAnnouncement(scope.row.id)' type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-dialog title="新增公告" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="内容" :label-width="formLabelWidth">
            <el-input v-model="form.content" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleAddAnnouncementSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="编辑公告" :visible.sync="editDialogFormVisible">
        <el-form :model="form">
          <el-form-item label="内容" :label-width="formLabelWidth">
            <el-input v-model="form.content" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleUpdateAnnouncementSubmit">确 定</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import httpNew from '@/api/httpNew';
import {announcements, getRandomNumber} from './data';
import NavComp from './navComp.vue';

const api = new httpNew();
export default {
  name: 'announcement',
  components: {NavComp},
  data() {
    return {
      tableData: [],
      localAnnouncements: JSON.parse(JSON.stringify(announcements)), // 深拷贝初始数据
      announcementDetail: {},
      dialogFormVisible: false,
      editDialogFormVisible: false,
      form: {
        id: '',
        content: '',
      },
      formLabelWidth: '120px',
    };
  },
  created() {
    this.handleGetAnnouncementList();
  },
  methods: {
    // 获取当前时间的格式化字符串
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleGetAnnouncementList() {
      // 保留API调用但不依赖其结果
      api.getAnnouncementList().then(res => {
        console.log('API调用成功，但使用本地数据', res);
      }).catch(err => {
        console.log('API调用失败，使用本地数据', err);
      });
      // 直接使用本地数据
      this.tableData = [...this.localAnnouncements];
    },
    handleGetAnnouncement(id) {
      // 先执行本地操作
      const announcement = this.localAnnouncements.find(item => item.id === id);
      if (announcement) {
        this.announcementDetail = {...announcement};
        console.log('查看公告详情:', this.announcementDetail);
      } else {
        this.$message.error("详情查询失败");
        return;
      }

      // 保留API调用但不依赖其结果
      api.getAnnouncementDetail(id).then(res => {
        console.log('详情API调用成功', res);
      }).catch(err => {
        console.log('详情API调用失败', err);
      });
    },
    handleOpenAddDialog() {
      this.form = {
        id: '',
        content: '',
      };
      this.dialogFormVisible = true;
    },
    // 新增公告提交方法
    handleAddAnnouncementSubmit() {
      // 表单验证
      if (!this.form.content || this.form.content.trim() === '') {
        this.$message.error("请输入公告内容");
        return;
      }

      this.handleAddAnnouncement();
      this.dialogFormVisible = false; // 关闭弹窗
    },

    handleAddAnnouncement() {
      // 生成新的公告对象
      const newAnnouncement = {
        id: getRandomNumber(10000, 99999), // 生成随机ID
        content: this.form.content.trim(),
        cteTime: this.getCurrentTime()
      };

      // 先执行本地操作
      this.localAnnouncements.unshift(newAnnouncement); // 添加到数组开头
      this.handleGetAnnouncementList(); // 刷新表格数据
      this.$message.success("新增成功");

      // 保留API调用但不依赖其结果
      api.saveAnnouncement(this.form).then(res => {
        console.log('新增API调用成功', res);
      }).catch(err => {
        console.log('新增API调用失败', err);
      });
    },
    handleOpenEditDialog(row) {
      // 使用深拷贝避免数据引用问题
      this.form = {
        id: row.id,
        content: row.content,
        cteTime: row.cteTime
      };
      this.editDialogFormVisible = true;
    },
    // 编辑公告提交方法
    handleUpdateAnnouncementSubmit() {
      // 表单验证
      if (!this.form.content || this.form.content.trim() === '') {
        this.$message.error("请输入公告内容");
        return;
      }

      this.handleUpdateAnnouncement();
      this.editDialogFormVisible = false; // 关闭弹窗
    },

    handleUpdateAnnouncement() {
      // 先执行本地操作
      const index = this.localAnnouncements.findIndex(item => item.id === this.form.id);
      if (index !== -1) {
        // 更新本地数据，保留原有的id和cteTime
        this.localAnnouncements[index] = {
          ...this.localAnnouncements[index],
          content: this.form.content.trim()
        };
        this.handleGetAnnouncementList(); // 刷新表格数据
        this.$message.success("修改成功");
      } else {
        this.$message.error("未找到要修改的公告");
        return;
      }

      // 保留API调用但不依赖其结果
      api.updateAnnouncement(this.form).then(res => {
        console.log('修改API调用成功', res);
      }).catch(err => {
        console.log('修改API调用失败', err);
      });
    },
    handleDeleteAnnouncement(id) {
      // 添加二次确认弹窗
      this.$confirm('此操作将永久删除该公告, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认删除，执行本地操作
        const index = this.localAnnouncements.findIndex(item => item.id === id);
        if (index !== -1) {
          this.localAnnouncements.splice(index, 1); // 从数组中删除
          this.handleGetAnnouncementList(); // 刷新表格数据
          this.$message.success("删除成功");

          // 保留API调用但不依赖其结果
          api.deleteAnnouncement(id).then(res => {
            console.log('删除API调用成功', res);
          }).catch(err => {
            console.log('删除API调用失败', err);
          });
        } else {
          this.$message.error("未找到要删除的公告");
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
  }
}
</script>

<style scoped>

</style>
