<template>
  <div class="center_home article-contain">
    <div class="user_infor">
      <div class="left_infor">
        <img
          v-if="userForm.headImg"
          :src="userForm.headImg"
          :onerror="errorImg"
        />
      </div>
      <div class="right_infor">
        <div class="name">{{userForm.nickName}}</div>
        <div class="sign">{{userForm.personalSignature}}</div>
        <div class="sign">{{userForm.gender}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import httpCore from '../../api/httpCore';

const api = new httpCore();
import errorPic from "@/assets/image/errorPic.png";
import {EncryptData,DecryptData} from "@/utils/aesUtils";
export default {
  name: "Info",
  data() {
    return {
      id:localStorage.getItem('forwordUser'),
      userForm:{},
      currentUserId:this.$store.state.loginInfo.userId,
      desc:'我',
      errorImg: 'this.src="' + errorPic + '"',
    };
  },
  mounted() {
    this.checkUser();
    this.getUserInfo();
  },
  methods: {
    checkUser:function(){
      var userId = this.$store.state.loginInfo.userId;
      if (userId == this.id){
        this.getUserInfo()
      }
    },
    getUserInfo:function(){
      api.getUserInfoAES(localStorage.getItem('forwordUser')).then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.userForm = res.data;
          if(this.id!=this.currentUserId){
            this.desc=this.userForm.gender=='2'?'她':'他';
          }
        }else {
          this.$message({message: res.msg ,type: 'error'});
        }
      }).catch(err => {
          console.log(err);
        });
    },
  },

};
</script>
<style>
</style>
