<template>
  <div>
    <NavComp/>
    <el-card class="pdt-60 box-card">
      <div slot="header" class="clearfix">
        <el-row :gutter="14">
          <el-col :span="14" :offset="5">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="flex: 1; text-align: center;">
                <h2>敏感词管理</h2>
              </div>
              <div>
                <el-button type="primary" plain @click="handleOpenAddDialog">新增</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="14">
        <el-col :span="14" :offset="5">
          <el-table
            :data="wordlist"
            border
            style="width: 100%">
            <el-table-column
              fixed
              prop="id"
              label="ID"
              width="50">
            </el-table-column>
            <el-table-column
              prop="content"
              label="内容">
            </el-table-column>
            <el-table-column
              label="当前规则"
              width="300">
              <template slot-scope="scope">
                <span v-if="getCurrentRule(scope.row.id)" class="rule-status">
                  {{ getRuleStatusText(getCurrentRule(scope.row.id)) }}
                </span>
                <span v-else class="no-rule">未配置规则</span>
              </template>
            </el-table-column>
            <!--<el-table-column
              prop="author"
              label="作者"
              width="120">
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
              width="120">
            </el-table-column>
            <el-table-column
              prop="cteTime"
              label="创建时间"
              width="300">
            </el-table-column>-->
            <el-table-column
              fixed="right"
              label="操作">
              <template slot-scope="scope">
                <el-button @click="handleGetSentiveWordDetail(scope.row.id)" type="text" size="small">查看</el-button>
                <el-button @click='handleOpenEditDialog(scope.row)' type="text" size="small">编辑</el-button>
                <el-button @click='getSentiveWordRule(scope.row)' type="text" size="small">屏蔽规则</el-button>
                <el-button @click='deleteSentiveWord(scope.row)' type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-dialog title="新增敏感词" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="内容" :label-width="formLabelWidth">
            <el-input v-model="form.content" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleAddSentiveWordSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="编辑敏感词" :visible.sync="editDialogFormVisible">
        <el-form :model="form">
          <el-form-item label="内容" :label-width="formLabelWidth">
            <el-input v-model="form.content" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleUpdateSentiveWordSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="屏蔽规则配置" :visible.sync="ruleDialogFormVisible" width="600px">
        <el-form :model="ruleForm" label-width="120px" label-position="left">
          <el-form-item label="敏感词">
            <el-input v-model="ruleForm.wordContent" disabled></el-input>
          </el-form-item>
          <el-form-item label="作用范围">
            <el-checkbox-group v-model="ruleForm.scopes" style="text-align: left;">
              <el-checkbox label="topic">话题发布</el-checkbox>
              <el-checkbox label="tips">小知识发布</el-checkbox>
              <el-checkbox label="comment">评论发布</el-checkbox>
              <el-checkbox label="article">文章发布</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="屏蔽级别" class="radio-form-item">
            <div style="display: flex; align-items: center; justify-content: flex-start; width: 100%; min-height: 40px;">
              <el-radio-group v-model="ruleForm.level" style="margin: 0;">
                <el-radio label="warn">警告提示</el-radio>
                <el-radio label="forbid">禁止发布</el-radio>
                <el-radio label="replace">自动替换</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item label="处理方式">
            <el-select v-model="ruleForm.action" placeholder="请选择处理方式" style="width: 100%;">
              <el-option label="直接拦截" value="block"></el-option>
              <el-option label="人工审核" value="review"></el-option>
              <el-option label="自动过滤" value="filter"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="替换文本" v-if="ruleForm.level === 'replace'">
            <el-input v-model="ruleForm.replaceText" placeholder="请输入替换文本"></el-input>
          </el-form-item>
          <el-form-item label="规则状态" class="switch-form-item">
            <div style="display: flex; align-items: center; justify-content: flex-start; width: 100%; min-height: 40px;">
              <el-switch
                v-model="ruleForm.status"
                active-text="启用"
                inactive-text="禁用"
                active-value="enabled"
                inactive-value="disabled"
                style="margin: 0;">
              </el-switch>
            </div>
          </el-form-item>
          <el-form-item label="规则描述">
            <el-input
              type="textarea"
              :rows="3"
              placeholder="请输入规则描述"
              v-model="ruleForm.description">
            </el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="ruleDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSaveRuleSubmit">保 存</el-button>
        </div>
      </el-dialog>

      <!-- 详情弹窗 -->
      <el-dialog title="敏感词详情" :visible.sync="detailDialogVisible" width="70%" top="5vh">
        <div class="detail-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-section">
                <h4 class="section-title">基础信息</h4>
                <div class="detail-item">
                  <span class="label">敏感词ID：</span>
                  <span class="value">{{ wordDetail.id }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">敏感词内容：</span>
                  <span class="value highlight">{{ wordDetail.content }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">创建时间：</span>
                  <span class="value">{{ wordDetail.cteTime }}</span>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-section">
                <h4 class="section-title">屏蔽规则</h4>
                <div v-if="wordDetail.rules && wordDetail.rules.length > 0">
                  <div v-for="(rule, index) in wordDetail.rules" :key="index" class="rule-item">
                    <div class="rule-header">规则 {{ index + 1 }}</div>
                    <div class="detail-item">
                      <span class="label">作用范围：</span>
                      <span class="value">{{ formatScopes(rule.scopes) }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">屏蔽级别：</span>
                      <span class="value">{{ formatLevel(rule.level) }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">处理方式：</span>
                      <span class="value">{{ formatAction(rule.action) }}</span>
                    </div>
                    <div class="detail-item" v-if="rule.replaceText">
                      <span class="label">替换文本：</span>
                      <span class="value">{{ rule.replaceText }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">规则状态：</span>
                      <span class="value" :class="rule.status === 'enabled' ? 'status-enabled' : 'status-disabled'">
                        {{ rule.status === 'enabled' ? '启用' : '禁用' }}
                      </span>
                    </div>
                    <div class="detail-item" v-if="rule.description">
                      <span class="label">规则描述：</span>
                      <span class="value">{{ rule.description }}</span>
                    </div>
                  </div>
                </div>
                <div v-else class="no-rules">
                  <span class="no-rule">暂无屏蔽规则</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import httpNew from "@/api/httpNew";
const api = new httpNew();
import {sentivewords, sentiveWordRules, getRandomNumber} from './data';
import NavComp from './navComp.vue';

export default {
  name: "sentiveWord",
  components: {NavComp},
  data() {
    return {
      wordForm: {},
      wordlist: [],
      localSentiveWords: JSON.parse(JSON.stringify(sentivewords)), // 深拷贝敏感词数据
      localSentiveWordRules: JSON.parse(JSON.stringify(sentiveWordRules)), // 深拷贝屏蔽规则数据
      wordRule: {},
      wordDetail: {},
      dialogFormVisible: false,
      editDialogFormVisible: false,
      ruleDialogFormVisible: false,
      detailDialogVisible: false,
      form: {
        id: '',
        content: '',
      },
      ruleForm: {
        id: '',
        wordId: '',
        wordContent: '',
        scopes: [],
        level: 'warn',
        action: 'block',
        status: 'enabled',
        replaceText: '',
        description: ''
      },
      formLabelWidth: '120px',
    };
  },
  mounted(){
    this.handleGetSentiveWordList();
  },
  methods: {
    // 获取敏感词列表
    handleGetSentiveWordList() {
      // 保留API调用但不依赖其结果
      this.getSentiveWordByUserId();

      // 使用本地数据
      this.wordlist = [...this.localSentiveWords];
    },

    // 打开新增对话框
    handleOpenAddDialog() {
      // 重置表单
      this.form = {
        id: '',
        content: '',
      };
      this.dialogFormVisible = true;
    },

    // 打开编辑对话框
    handleOpenEditDialog(row) {
      // 使用深拷贝避免数据引用问题
      this.form = {
        id: row.id,
        content: row.content
      };
      this.editDialogFormVisible = true;
    },

    // 新增敏感词提交方法
    handleAddSentiveWordSubmit() {
      // 表单验证
      if (!this.form.content || this.form.content.trim() === '') {
        this.$message.error("请输入敏感词内容");
        return;
      }

      // 检查敏感词是否重复
      const exists = this.localSentiveWords.find(item => item.content === this.form.content.trim());
      if (exists) {
        this.$message.error("敏感词已存在");
        return;
      }

      this.handleAddSentiveWord();
      this.dialogFormVisible = false; // 关闭弹窗
    },

    // 新增敏感词
    handleAddSentiveWord() {
      // 生成新的敏感词对象
      const newWord = {
        id: getRandomNumber(100, 999),
        content: this.form.content.trim()
      };

      // 先执行本地操作
      this.localSentiveWords.push(newWord);
      this.handleGetSentiveWordList(); // 刷新表格数据
      this.$message({message: '新增敏感词成功', type:'success'});

      // 保留API调用但不依赖其结果
      api.saveSentiveWord([newWord]).then(res => {
        console.log('新增敏感词API调用成功', res);
      }).catch(err => {
        console.log('新增敏感词API调用失败', err);
      });
    },

    // 编辑敏感词提交方法
    handleUpdateSentiveWordSubmit() {
      // 表单验证
      if (!this.form.content || this.form.content.trim() === '') {
        this.$message.error("请输入敏感词内容");
        return;
      }

      // 检查敏感词是否重复（排除自己）
      const exists = this.localSentiveWords.find(item => item.content === this.form.content.trim() && item.id !== this.form.id);
      if (exists) {
        this.$message.error("敏感词已存在");
        return;
      }

      this.handleUpdateSentiveWord();
      this.editDialogFormVisible = false; // 关闭弹窗
    },

    // 编辑敏感词
    handleUpdateSentiveWord() {
      // 先执行本地操作
      const index = this.localSentiveWords.findIndex(item => item.id === this.form.id);
      if (index !== -1) {
        // 更新本地数据
        this.localSentiveWords[index] = {
          ...this.localSentiveWords[index],
          content: this.form.content.trim()
        };
        this.handleGetSentiveWordList(); // 刷新表格数据
        this.$message({message: '编辑敏感词成功', type:'success'});
      } else {
        this.$message({message: '未找到要编辑的敏感词', type: 'error'});
        return;
      }

      // 保留API调用但不依赖其结果
      api.updateSentiveWord(this.form).then(res => {
        console.log('编辑敏感词API调用成功', res);
      }).catch(err => {
        console.log('编辑敏感词API调用失败', err);
      });
    },

    // 查看敏感词详情
    handleGetSentiveWordDetail(id) {
      // 从本地数据中查找
      const word = this.localSentiveWords.find(item => item.id === id);
      if (word) {
        // 查找该敏感词的所有屏蔽规则
        const wordRules = this.localSentiveWordRules.filter(rule => rule.wordId === id);
        this.wordDetail = {
          ...word,
          rules: wordRules
        };
        this.detailDialogVisible = true; // 打开详情弹窗
      } else {
        this.$message.error("详情查询失败");
        return;
      }

      // 保留API调用但不依赖其结果
      api.getSentiveWordDetail(id).then(res => {
        console.log('详情API调用成功', res);
      }).catch(err => {
        console.log('详情API调用失败', err);
      });
    },

    // 格式化作用范围显示
    formatScopes(scopes) {
      const scopeMap = {
        'topic': '话题发布',
        'tips': '小知识发布',
        'comment': '评论发布',
        'article': '文章发布'
      };
      return scopes.map(scope => scopeMap[scope] || scope).join('、');
    },

    // 格式化屏蔽级别显示
    formatLevel(level) {
      const levelMap = {
        'warn': '警告提示',
        'forbid': '禁止发布',
        'replace': '自动替换'
      };
      return levelMap[level] || level;
    },

    // 格式化处理方式显示
    formatAction(action) {
      const actionMap = {
        'block': '直接拦截',
        'review': '人工审核',
        'filter': '自动过滤'
      };
      return actionMap[action] || action;
    },
    addWord() {
      this.checkSentiveWord(this.wordForm.word);
      this.wordlist.unshift(this.wordForm.word);
      this.wordForm = {};
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          api.saveSentiveWord(this.wordlist).then(response => {
            var res = response.data;
            if(res.status == "0"){
              this.$message({message: '保存成功' ,type: 'success'});
              this.$router.go(-1);
            }else {
              this.$message({'message': res.msg ,'type': 'error'});
            }
          }).catch(err => {
              console.log(err);
            });
        } else {
          return false;
        }
      });
    },
    checkSentiveWord(word) {
      api.checkSentiveWord(word).then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.$message({message: '敏感词不允许提交' ,type: 'error'});
          return false;
        }else {
          return true;
        }
      })
    },
    getSentiveWordByUserId(){
      api.getSentiveWordByUserId(this.$store.state.loginInfo.userId).then(response => {
        var res = response.data;
        if (res.status == "0") {
          this.wordlist = res.data;
        } else {
          this.$message({'message': res.msg, 'type': 'error'});
        }
      })
    },
    updateSentiveWord(row) {
      // 这个方法现在被handleOpenEditDialog替代
      this.handleOpenEditDialog(row);
    },
    deleteSentiveWord(row) {
      this.$confirm('此操作将永久删除该敏感词, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认删除，执行本地操作
        const index = this.localSentiveWords.findIndex(item => item.id === row.id);
        if (index !== -1) {
          this.localSentiveWords.splice(index, 1); // 从数组中删除
          this.handleGetSentiveWordList(); // 刷新表格数据
          this.$message({message: '删除成功', type: 'success'});

          // 保留API调用但不依赖其结果
          api.deleteSentiveWord(row.id).then(res => {
            console.log('删除敏感词API调用成功', res);
          }).catch(err => {
            console.log('删除敏感词API调用失败', err);
          });
        } else {
          this.$message({message: '未找到要删除的敏感词', type: 'error'});
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    // 查看/编辑屏蔽规则
    async getSentiveWordRule(row){
      // 从本地规则数据中查找
      const rule = this.localSentiveWordRules.find(item => item.wordId === row.id);

      if (rule) {
        // 找到规则，打开编辑对话框
        this.ruleForm = {
          id: rule.id,
          wordId: rule.wordId,
          wordContent: rule.wordContent,
          scopes: [...rule.scopes],
          level: rule.level,
          action: rule.action,
          status: rule.status,
          replaceText: rule.replaceText,
          description: rule.description
        };
        this.ruleDialogFormVisible = true;
      } else {
        // 没有找到规则，创建新规则
        this.ruleForm = {
          id: '',
          wordId: row.id,
          wordContent: row.content,
          scopes: ['topic'],
          level: 'warn',
          action: 'block',
          status: 'enabled',
          replaceText: '***',
          description: `${row.content}的屏蔽规则`
        };
        this.ruleDialogFormVisible = true;
      }

      // 保留API调用但不依赖其结果
      try {
        let res = await api.getSentiveWordDetail(row.id)
        console.log('屏蔽规则API调用成功', res);
      } catch (err) {
        console.log('屏蔽规则API调用失败', err);
      }
    },

    // 保存屏蔽规则
    handleSaveRuleSubmit() {
      // 表单验证
      if (this.ruleForm.scopes.length === 0) {
        this.$message.error("请至少选择一个作用范围");
        return;
      }

      if (this.ruleForm.level === 'replace' && (!this.ruleForm.replaceText || this.ruleForm.replaceText.trim() === '')) {
        this.$message.error("自动替换模式下请输入替换文本");
        return;
      }

      this.handleSaveRule();
      this.ruleDialogFormVisible = false; // 关闭弹窗
    },

    // 保存规则到本地
    handleSaveRule() {
      const now = new Date();
      const timeStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

      if (this.ruleForm.id) {
        // 更新现有规则
        const index = this.localSentiveWordRules.findIndex(item => item.id === this.ruleForm.id);
        if (index !== -1) {
          this.localSentiveWordRules[index] = {
            ...this.ruleForm,
            updateTime: timeStr
          };
          this.$message.success("屏蔽规则更新成功");
        }
      } else {
        // 新增规则
        const newRule = {
          ...this.ruleForm,
          id: getRandomNumber(1000, 9999),
          cteTime: timeStr,
          updateTime: timeStr
        };
        this.localSentiveWordRules.push(newRule);
        this.$message.success("屏蔽规则创建成功");
      }

      // 保留API调用但不依赖其结果
      api.wordRules && api.wordRules(this.ruleForm).then(res => {
        console.log('保存屏蔽规则API调用成功', res);
      }).catch(err => {
        console.log('保存屏蔽规则API调用失败', err);
      });
    },

    // 获取规则状态文本
    getRuleStatusText(rule) {
      const scopeTexts = {
        'topic': '话题',
        'tips': '小知识',
        'comment': '评论',
        'article': '文章'
      };

      const levelTexts = {
        'warn': '警告',
        'forbid': '禁止',
        'replace': '替换'
      };

      const actionTexts = {
        'block': '拦截',
        'review': '审核',
        'filter': '过滤'
      };

      const scopes = rule.scopes.map(scope => scopeTexts[scope]).join('、');
      return `${scopes} | ${levelTexts[rule.level]} | ${actionTexts[rule.action]} | ${rule.status === 'enabled' ? '启用' : '禁用'}`;
    },

    // 获取敏感词的当前规则
    getCurrentRule(wordId) {
      return this.localSentiveWordRules.find(rule => rule.wordId === wordId);
    }
  },
};
</script>
<style scoped>
.rule-status {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.no-rule {
  font-size: 12px;
  color: #C0C4CC;
  font-style: italic;
}

.el-checkbox-group {
  text-align: left !important;
}

.el-checkbox-group .el-checkbox {
  margin-right: 15px;
  display: inline-block;
}

.el-radio-group {
  text-align: left !important;
}

.el-radio-group .el-radio {
  margin-right: 15px;
  display: inline-block;
}

.el-form-item__content {
  text-align: left !important;
}

/* 输入框内容居左对齐 */
.el-input__inner {
  text-align: left !important;
}

/* 文本域内容居左对齐 */
.el-textarea__inner {
  text-align: left !important;
}

/* 下拉选择框内容居左对齐 */
.el-select .el-input__inner {
  text-align: left !important;
}

/* 开关组件既居左又垂直居中 */
.switch-form-item .el-form-item__content {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  min-height: 40px !important;
  text-align: left !important;
}

.switch-form-item .el-form-item__content > div {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
  width: 100% !important;
  text-align: left !important;
}

.switch-form-item .el-switch {
  margin-left: 0 !important;
  margin-right: auto !important;
}

/* 强制开关居左显示 */
.switch-form-item .el-form-item__content .el-switch {
  position: relative !important;
  left: 0 !important;
  transform: none !important;
}

/* 单选框组既居左又垂直居中 */
.radio-form-item .el-form-item__content {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  min-height: 40px !important;
}

.radio-form-item .el-form-item__content > div {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  width: 100% !important;
}

.radio-form-item .el-radio-group {
  display: flex !important;
  align-items: center !important;
  margin: 0 !important;
}

.radio-form-item .el-radio {
  margin-right: 15px !important;
  display: inline-flex !important;
  align-items: center !important;
}

/* 单选框组强制居左对齐 */
.el-radio-group {
  display: block !important;
  text-align: left !important;
}

.el-radio-group .el-radio {
  display: inline-block !important;
  margin-right: 15px !important;
}

/* 确保表单项内容区域居左 */
.el-form-item__content > * {
  text-align: left !important;
}

/* 修复单选框的对齐问题 */
.el-form-item__content .el-radio-group {
  width: 100% !important;
  text-align: left !important;
}

/* 详情弹窗样式 */
.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 20px;
}

.section-title {
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.detail-item .label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.detail-item .value.highlight {
  color: #E6A23C;
  font-weight: 600;
  background-color: #FDF6EC;
  padding: 2px 6px;
  border-radius: 4px;
}

.rule-item {
  background-color: #F5F7FA;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 15px;
  border-left: 4px solid #409EFF;
}

.rule-header {
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 10px;
  font-size: 14px;
}

.status-enabled {
  color: #67C23A;
  font-weight: 600;
}

.status-disabled {
  color: #F56C6C;
  font-weight: 600;
}

.no-rules {
  text-align: center;
  padding: 40px 0;
  color: #C0C4CC;
  font-style: italic;
}
</style>
