<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <h2>通知管理</h2>
      <!--<el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>-->
    </div>
    <el-row :gutter="20">
      <el-col :span="10" :offset="1">
        <el-row>
          <el-button type="success" @click='dialogFormVisible = true'>新增</el-button>
        </el-row>
      </el-col>
      <el-col :span="14" :offset="5">
        <el-table
          :data="tableData"
          border
          style="width: 100%">
          <el-table-column
            fixed
            prop="id"
            label="ID"
            width="150">
          </el-table-column>
          <el-table-column
            prop="noticeContent"
            label="内容"
            width="300">
          </el-table-column>
          <el-table-column
            prop="noticeStatusStr"
            label="状态"
            width="120">
          </el-table-column>
          <el-table-column
            prop="type"
            label="通知类型"
            width="120">
            <template slot-scope="scope">
              培训通知
            </template>
          </el-table-column>
          <el-table-column
            prop="noticeId"
            label="通知对象"
            width="120">
          </el-table-column>
          <el-table-column
            prop="noticeTime"
            label="通知时间"
            width="120">
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="160">
            <template slot-scope="scope">
              <el-button @click="handleSetRead(scope.row)" type="text" size="small" v-if="scope.row.noticeStatus=='0'">已读</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog title="新增通知" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="关联文章" :label-width="formLabelWidth" :style="{width: '40%'}">
          <el-select v-model="form.articleId" placeholder="请选择文章" >
            <el-option v-for="item in articleList" :key="item.id" :label="item.title" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通知内容" :label-width="formLabelWidth">
          <el-input v-model="form.content" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="通知人" :label-width="formLabelWidth" :style="{width: '40%'}">
          <el-select
            v-model="form.noticeId"
            multiple
            filterable
            remote
            reserve-keyword
            placeholder="请输入关键词"
            :remote-method="remoteMethod"
            :loading="loading">
            <el-option
              v-for="item in userList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false,handleAddTrainApplyNotice()">确 定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import httpNew from '@/api/httpNew';

const api = new httpNew();

export default {
  name: 'notice',
  data() {
    return {
      tableData: [],
      articleList: [],
      trainApplyObj: {},
      dialogFormVisible: false,
      form: {
        content: '',
        articleId: '',
        noticeId: [],
      },
      formLabelWidth: '120px',
      userList: [],
      loading: false,
    };
  },
  mounted() {
    this.handleGetArticles();
    this.handleGetTrainApplyList();
    // this.tableData = notices;
  },
  methods: {
    handleGetArticles() {
      api.getArticleList().then(res => {
        this.articleList = res.data.data.list;
      })
    },
    handleGetTrainApplyList(){
      api.getTrainApplyList().then(res => {
        let noticeList = res.data.data;
        if (noticeList && noticeList.length > 0) {
          let arr = noticeList.map(item => {
            return {
              ...item,
              noticeStatusStr: item.noticeStatus == '0' ? "未读" : "已读",
            }
          })
          this.tableData = arr;
        }
      })
    },
    handleSetRead(row){
      api.noticeRead(row.id).then(response => {
        if(response.status == "0"){
          this.$message.success("已读成功")
        }
      })
    },
    handleAddTrainApplyNotice(){
      api.addTrainApplyNotice(this.trainApplyObj).then(response => {
        if(response.status == "0"){
          this.$message.success("新增成功")
          this.handleGetTrainApplyList();
        }
      })
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        api.searchUser(query).then(response => {
          this.userList = response.data.data.map(item => {
            return {
              value: item.id,
              label: item.nickName,
            };
          });
        });
        setTimeout(() => {
          this.loading = false;
        }, 200);
      } else {
        this.userList = [];
      }
    }
  }
};
</script>
<style>
</style>
