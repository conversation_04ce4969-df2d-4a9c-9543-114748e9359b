<template>
  <div class="home_content article-contain">
    <div style="width:100%;">
      <select v-model="searchType" class="search_select">
        <option value="">分类</option>
        <option v-for='item in searchTypeList' value="{{ item }}">{{ item }}</option>
      </select>
      <div class="tl mgt-20 mgb-10">
        <span class="fs-13" style="color:#C0C4CC;">搜索结果{{resultCount}}个</span>
      </div>
      <div v-for="(item,index) in searchResultList">
        <contentComponent
          :contentObj="item"
          :sort="index"
        ></contentComponent>
      </div>
      <div v-show="totalPage>1&&queryParam.pageNum!=totalPage&&!loading" class="mgt-10 fs-13 tc" style="opacity: 0.5;">
	    	<span class="pointer" @click="loadMore">
		      <span>查看更多</span>
	      </span>
      </div>
      <div class="mgt-10" style="height:80px" v-loading ="loading" element-loading-text = "数据正在加载中"></div>
      <!-- 首页为空时，展示内容 -->
      <div class="empty" v-if="!loading&&searchResultList.length==0">
        <img src="../../assets/image/empty.png" alt="" />
        <div>暂无足迹~</div>
      </div>
    </div>
  </div>
</template>

<script>
import httpCore from "@/api/httpCore";
const api = new httpCore();
import httpNew from "@/api/httpNew";
const apiNew = new httpNew();
import contentComponent from '../home/<USER>/content';
export default {
  data(){
    return{
      searchResultList:[],
      queryParam:{
        pageNum:1,
        pageSize:5,
        keyword:this.$route.query.searchWord,
      },
      totalPage:0,
      resultCount:0,
      loading:true,
      type:this.$route.query.type,
      searchType: [],
      searchTypeList: []
    }
  },
  components:{contentComponent},
  mounted(){
    this.listSearchType()
    this.getSearchResult();
  },
  watch: {
    '$route' (to, from) { //监听路由是否变化
      if(to.query.searchWord != from.query.searchWord){
        this.queryParam.keyword = to.query.searchWord;
        this.getSearchResult();//重新加载数据
      }
    }
  },
  methods:{
    listSearchType(){
      apiNew.listSearchType().then(res => {
        this.searchTypeList = res.data;
      })
    },
    replaceURLWithHTMLLinks:function(text) {
      var exp = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
      return text.replace(exp,"<a style='color:#3E8CFF;' target='_blank' href='$1'><i class='el-icon-link fs-14 mgr-2'></i>网页链接</a>");
    },
    getSearchResult:function(){
      var that = this;
      this.loading = true;
      if(this.type=='全文搜索'){
        api.getSearchResult(this.queryParam).then(response => {
          var res = response.data;
          this.loading=false;
          if(res.status == "0"){
            var searchResultList= res.data.list;
            searchResultList.forEach(function(item){
              if(item.type=='2'){
                item.content = item.content.replace(/style=\"[^\"]*\"/g, "");
                item.title = item.title.replace(new RegExp(that.queryParam.keyword,'g'), `<span style="color:#3E8CFF">${that.queryParam.keyword}</span>`);
              }else{
                item.content = that.replaceURLWithHTMLLinks(item.content);
              }
              item.content = item.content.replace(new RegExp(that.queryParam.keyword,'g'), `<span style="color:#3E8CFF">${that.queryParam.keyword}</span>`);
            });
            this.searchResultList = this.searchResultList.concat(searchResultList);
            this.totalPage = res.data.pages;
            this.resultCount=res.data.total;
          }else {
            this.$message({message: res.msg ,type: 'error'});
          }
        })
          .catch(err => {
            console.log(err);
          });
      }else if(this.type=='关键字搜索'){
        api.getSearchKeywordResult(this.queryParam).then(response => {
          var res = response.data;
          this.loading=false;
          if(res.status == "0"){
            var searchResultList= res.data.list;
            searchResultList.forEach(function(item){
              item.content = item.content.replace(/style=\"[^\"]*\"/g, "");
            });
            this.searchResultList = this.searchResultList.concat(searchResultList);
            this.totalPage = res.data.pages;
            this.resultCount=res.data.total;
          }else {
            this.$message({message: res.msg ,type: 'error'});
          }
        })
          .catch(err => {
            console.log(err);
          });
      }
    },
    loadMore(){
      if(this.queryParam.pageNum<this.totalPage){
        this.queryParam.pageNum++;
        this.getSearchResult();
      }
    },
  }
}
</script>

<style>
</style>
