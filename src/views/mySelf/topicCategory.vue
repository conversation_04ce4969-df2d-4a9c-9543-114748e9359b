<template>
  <div>
    <NavComp/>
    <el-card class="pdt-60 box-card">
      <div slot="header" class="clearfix">
        <el-row :gutter="14">
          <el-col :span="14" :offset="5">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="flex: 1; text-align: center;">
                <h2>话题分类管理</h2>
              </div>
              <div>
                <el-button type="primary" plain @click="handleOpenAddDialog">新增分类</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="14">
        <el-col :span="14" :offset="5">
          <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
              fixed
              prop="id"
              label="ID"
              width="80">
            </el-table-column>
            <el-table-column
              prop="name"
              label="分类名称"
              width="150">
            </el-table-column>
            <el-table-column
              prop="description"
              label="描述">
            </el-table-column>
            <el-table-column
              prop="cteTime"
              label="创建时间"
              width="120">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="160">
              <template slot-scope="scope">
                <el-button @click="handleGetCategoryDetail(scope.row)" type="text" size="small">详情</el-button>
                <el-button @click='handleOpenEditDialog(scope.row)' type="text" size="small">编辑</el-button>
                <el-button @click='handleDeleteCategory(scope.row)' type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      
      <!-- 新增分类对话框 -->
      <el-dialog title="新增分类" :visible.sync="addDialogFormVisible">
        <el-form :model="form">
          <el-form-item label="分类名称" :label-width="formLabelWidth">
            <el-input v-model="form.name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="描述" :label-width="formLabelWidth">
            <el-input v-model="form.description" type="textarea" :rows="3" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleAddCategorySubmit">确 定</el-button>
        </div>
      </el-dialog>
      
      <!-- 编辑分类对话框 -->
      <el-dialog title="编辑分类" :visible.sync="editDialogFormVisible">
        <el-form :model="form">
          <el-form-item label="分类名称" :label-width="formLabelWidth">
            <el-input v-model="form.name" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="描述" :label-width="formLabelWidth">
            <el-input v-model="form.description" type="textarea" :rows="3" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleUpdateCategorySubmit">确 定</el-button>
        </div>
      </el-dialog>
      
      <!-- 详情弹窗 -->
      <el-dialog title="分类详情" :visible.sync="detailDialogVisible" width="70%" top="5vh">
        <div class="detail-content">
          <div class="detail-section">
            <div class="detail-item">
              <span class="label">分类ID：</span>
              <span class="value">{{ categoryDetail.id }}</span>
            </div>
            <div class="detail-item">
              <span class="label">分类名称：</span>
              <span class="value category-name">{{ categoryDetail.name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">描述：</span>
              <span class="value description-text">{{ categoryDetail.description }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ categoryDetail.cteTime }}</span>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import httpNew from '@/api/httpNew';
import {topicCategories, getRandomNumber} from './data';
import NavComp from './navComp.vue';

const api = new httpNew();

export default {
  name: 'topicCategory',
  components: {NavComp},
  data() {
    return {
      tableData: [],
      localTopicCategories: JSON.parse(JSON.stringify(topicCategories)), // 深拷贝分类数据
      categoryDetail: {},
      addDialogFormVisible: false,
      editDialogFormVisible: false,
      detailDialogVisible: false,
      form: {
        id: '',
        name: '',
        description: '',
      },
      formLabelWidth: '120px',
    };
  },
  mounted() {
    this.handleGetCategoryList();
  },
  methods: {
    // 获取当前时间的格式化字符串
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 获取分类列表
    handleGetCategoryList() {
      // 保留API调用但不依赖其结果
      api.getTopicCategoryList && api.getTopicCategoryList().then(res => {
        console.log('分类列表API调用成功', res);
      }).catch(err => {
        console.log('分类列表API调用失败', err);
      });

      // 使用本地数据
      this.tableData = [...this.localTopicCategories];
    },

    // 打开新增对话框
    handleOpenAddDialog() {
      // 重置表单
      this.form = {
        id: '',
        name: '',
        description: '',
      };
      this.addDialogFormVisible = true;
    },

    // 新增分类提交方法
    handleAddCategorySubmit() {
      // 表单验证
      if (!this.form.name || this.form.name.trim() === '') {
        this.$message.error("请输入分类名称");
        return;
      }

      // 检查分类名称是否重复
      const exists = this.localTopicCategories.find(item => item.name === this.form.name.trim());
      if (exists) {
        this.$message.error("分类名称已存在");
        return;
      }

      this.handleAddCategory();
      this.addDialogFormVisible = false; // 关闭弹窗
    },

    // 新增分类
    handleAddCategory() {
      // 生成新的分类对象
      const newCategory = {
        id: getRandomNumber(100, 999),
        name: this.form.name.trim(),
        description: this.form.description.trim() || '',
        cteTime: this.getCurrentTime()
      };

      // 先执行本地操作
      this.localTopicCategories.unshift(newCategory);
      this.handleGetCategoryList(); // 刷新表格数据
      this.$message.success('新增分类成功');

      // 保留API调用但不依赖其结果
      api.addTopicCategory && api.addTopicCategory(this.form).then(res => {
        console.log('新增分类API调用成功', res);
      }).catch(err => {
        console.log('新增分类API调用失败', err);
      });
    },

    // 打开编辑对话框
    handleOpenEditDialog(row) {
      // 使用深拷贝避免数据引用问题
      this.form = {
        id: row.id,
        name: row.name,
        description: row.description,
        cteTime: row.cteTime
      };
      this.editDialogFormVisible = true;
    },

    // 编辑分类提交方法
    handleUpdateCategorySubmit() {
      // 表单验证
      if (!this.form.name || this.form.name.trim() === '') {
        this.$message.error("请输入分类名称");
        return;
      }

      // 检查分类名称是否重复（排除自己）
      const exists = this.localTopicCategories.find(item => item.name === this.form.name.trim() && item.id !== this.form.id);
      if (exists) {
        this.$message.error("分类名称已存在");
        return;
      }

      this.handleUpdateCategory();
      this.editDialogFormVisible = false; // 关闭弹窗
    },

    // 编辑分类
    handleUpdateCategory() {
      // 先执行本地操作
      const index = this.localTopicCategories.findIndex(item => item.id === this.form.id);
      if (index !== -1) {
        // 更新本地数据
        this.localTopicCategories[index] = {
          ...this.localTopicCategories[index],
          name: this.form.name.trim(),
          description: this.form.description.trim() || ''
        };
        this.handleGetCategoryList(); // 刷新表格数据
        this.$message.success('编辑分类成功');
      } else {
        this.$message.error('未找到要编辑的分类');
        return;
      }

      // 保留API调用但不依赖其结果
      api.updateTopicCategory && api.updateTopicCategory(this.form).then(res => {
        console.log('编辑分类API调用成功', res);
      }).catch(err => {
        console.log('编辑分类API调用失败', err);
      });
    },

    // 删除分类
    handleDeleteCategory(row) {
      // 添加二次确认弹窗
      this.$confirm('此操作将永久删除该分类, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认删除，执行本地操作
        const index = this.localTopicCategories.findIndex(item => item.id === row.id);
        if (index !== -1) {
          this.localTopicCategories.splice(index, 1); // 从数组中删除
          this.handleGetCategoryList(); // 刷新表格数据
          this.$message.success('删除分类成功');

          // 保留API调用但不依赖其结果
          api.deleteTopicCategory && api.deleteTopicCategory(row.id).then(res => {
            console.log('删除分类API调用成功', res);
          }).catch(err => {
            console.log('删除分类API调用失败', err);
          });
        } else {
          this.$message.error('未找到要删除的分类');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 查看分类详情
    handleGetCategoryDetail(row) {
      this.categoryDetail = {...row};
      this.detailDialogVisible = true; // 打开详情弹窗
    }
  }
};
</script>

<style scoped>
/* 详情弹窗样式 */
.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.detail-item .label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.category-name {
  background-color: #E1F3D8;
  color: #67C23A;
  padding: 6px 10px;
  border-radius: 4px;
  font-weight: 500;
}

.description-text {
  background-color: #F5F7FA;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}
</style>
