<template>
  <div>
    <NavComp/>
    <el-card class="pdt-60 box-card">
      <div slot="header" class="clearfix">
        <el-row :gutter="14">
          <el-col :span="14" :offset="5">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="flex: 1; text-align: center;">
                <h2>话题管理</h2>
              </div>
              <div>
                <el-button type="success" plain @click="gotoTopicCategoryManage">分类管理</el-button>
                <el-button type="primary" plain @click="handleOpenAddDialog">新增</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="14">
        <el-col :span="14" :offset="5">
          <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
              fixed
              prop="id"
              label="ID"
              width="80">
            </el-table-column>
            <el-table-column
              prop="topic"
              label="内容">
            </el-table-column>
            <el-table-column
              prop="categoryName"
              label="分类"
              width="120">
            </el-table-column>
            <!--<el-table-column
              prop="author"
              label="作者"
              width="120">
            </el-table-column>-->
            <el-table-column
              prop="status"
              label="状态"
              width="120">
            </el-table-column>
            <el-table-column
              prop="cteTime"
              label="创建时间"
              width="120">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="160">
              <template slot-scope="scope">
                <el-button @click="gotoTipsManage(scope.row)" type="text" size="small">小知识</el-button>
                <el-button @click="gotoTopicRecommend(scope.row)" type="text" size="small">推荐管理</el-button>
                <el-button @click="selectTopic(scope.row)" type="text" size="small">详情</el-button>
                <el-button @click='updateTopic(scope.row)' type="text" size="small">编辑</el-button>
                <el-button @click='handleDeleteTopic(scope.row)' type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-dialog title="新增话题" :visible.sync="addDialogFormVisible">
        <el-form :model="form">
          <el-form-item label="内容" :label-width="formLabelWidth">
            <el-input v-model="form.topic" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="分类" :label-width="formLabelWidth" :style="{width: '60%'}">
            <el-select v-model="form.categoryId" placeholder="请选择分类" style="width: 100%;">
              <el-option
                v-for="category in localTopicCategories"
                :key="category.id"
                :label="category.name"
                :value="category.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" :label-width="formLabelWidth" :style="{width: '40%'}">
            <el-select v-model="form.status" placeholder="请选择状态" >
              <el-option label="正常" value="正常"></el-option>
              <el-option label="删除" value="删除"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleAddTopicSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="编辑话题" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="内容" :label-width="formLabelWidth">
            <el-input v-model="form.topic" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="分类" :label-width="formLabelWidth" :style="{width: '60%'}">
            <el-select v-model="form.categoryId" placeholder="请选择分类" style="width: 100%;">
              <el-option
                v-for="category in localTopicCategories"
                :key="category.id"
                :label="category.name"
                :value="category.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" :label-width="formLabelWidth" :style="{width: '40%'}">
            <el-select v-model="form.status" placeholder="请选择状态" >
              <el-option label="删除" value="删除"></el-option>
              <el-option label="正常" value="正常"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmitFormSubmit">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 详情弹窗 -->
      <el-dialog title="话题详情" :visible.sync="detailDialogVisible" width="70%" top="5vh">
        <div class="detail-content">
          <div class="detail-section">
            <div class="detail-item">
              <span class="label">话题ID：</span>
              <span class="value">{{ topicDetail.id }}</span>
            </div>
            <div class="detail-item">
              <span class="label">话题内容：</span>
              <span class="value topic-content">{{ topicDetail.topic }}</span>
            </div>
            <div class="detail-item">
              <span class="label">分类：</span>
              <span class="value category-tag">{{ topicDetail.categoryName || '未分类' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">作者：</span>
              <span class="value">{{ topicDetail.author }}</span>
            </div>
            <div class="detail-item">
              <span class="label">状态：</span>
              <span class="value" :class="topicDetail.status === '正常' ? 'status-normal' : 'status-other'">{{ topicDetail.status }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ topicDetail.cteTime }}</span>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import httpNew from '@/api/httpNew';

const api = new httpNew();
import {topics, topicCategories, getRandomNumber} from './data';
import NavComp from './navComp.vue';

export default {
  name: 'topic',
  components: {
    NavComp
  },
  data() {
    return {
      topicId: this.$route.query.id,
      topicInfo: {},
      myInfo: {},
      tableData: [],
      localTopics: JSON.parse(JSON.stringify(topics)), // 深拷贝话题数据
      localTopicCategories: JSON.parse(JSON.stringify(topicCategories)), // 深拷贝分类数据
      topicDetail: {},
      dialogFormVisible: false,
      addDialogFormVisible: false,
      detailDialogVisible: false,
      form: {
        id: '',
        topic: '',
        categoryId: '',
        status: '正常',
      },
      formLabelWidth: '120px',
    };
  },
  mounted() {
    this.selectTopicList();
    // this.getMyInfo();
  },
  methods: {
    // 获取当前时间的格式化字符串
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    getMyInfo: function () {
      api.getCurrentUser().then(response => {
        var res = response.data;
        if (res.status == '0') {
          this.myInfo = res.data;
        } else {
          this.$message({message: res.msg, type: 'error'});
        }
      }).catch(err => {
        console.log(err);
      });
    },
    gotoTipsManage(row){
      var routeUrl=this.$router.resolve({name:'tipsManage',query: { topicId: row.id }});
      window.open(routeUrl.href, '_blank');
    },
    gotoTopicRecommend(row){
      var routeUrl=this.$router.resolve({name:'topicRecommendManage',query: { topicId: row.id }});
      window.open(routeUrl.href, '_blank');
    },

    // 跳转到分类管理页面
    gotoTopicCategoryManage() {
      this.$router.push({name: 'topicCategory'});
    },
    selectTopic(row) {
      // 从本地数据中查找
      const topic = this.localTopics.find(item => item.id === row.id);
      if (topic) {
        this.topicDetail = {...topic};
        this.detailDialogVisible = true; // 打开详情弹窗
      } else {
        this.$message.error("详情查询失败");
        return;
      }

      // 保留API调用但不依赖其结果
      api.getTopicInfo(row.id).then(res => {
        console.log('详情API调用成功', res);
      }).catch(err => {
        console.log('详情API调用失败', err);
      });
    },
    async selectTopicList() {
      // 保留API调用但不依赖其结果
      try {
        let res = await api.getTopicList()
        console.log('话题列表API调用成功', res);
      } catch (e) {
        console.log('话题列表API调用失败', e);
      }

      // 使用本地数据
      this.tableData = [...this.localTopics];
    },

    // 打开新增对话框
    handleOpenAddDialog() {
      // 重置表单
      this.form = {
        id: '',
        topic: '',
        categoryId: '',
        status: '正常',
      };
      this.addDialogFormVisible = true;
    },

    // 新增话题提交方法
    handleAddTopicSubmit() {
      // 表单验证
      if (!this.form.topic || this.form.topic.trim() === '') {
        this.$message.error("请输入话题内容");
        return;
      }

      if (!this.form.categoryId) {
        this.$message.error("请选择话题分类");
        return;
      }

      // 检查话题是否重复
      const exists = this.localTopics.find(item => item.topic === this.form.topic.trim());
      if (exists) {
        this.$message.error("话题内容已存在");
        return;
      }

      this.handleAddTopic();
      this.addDialogFormVisible = false; // 关闭弹窗
    },

    // 新增话题
    handleAddTopic() {
      // 查找分类名称
      const category = this.localTopicCategories.find(cat => cat.id === this.form.categoryId);

      // 生成新的话题对象
      const newTopic = {
        id: getRandomNumber(200000, 299999),
        topic: this.form.topic.trim(),
        categoryId: this.form.categoryId,
        categoryName: category ? category.name : '未分类',
        author: '当前用户', // 可以根据实际情况修改
        status: this.form.status,
        cteTime: this.getCurrentTime()
      };

      // 先执行本地操作
      this.localTopics.unshift(newTopic);
      this.selectTopicList(); // 刷新表格数据
      this.$message.success('新增话题成功');

      // 保留API调用但不依赖其结果
      api.addTopic && api.addTopic(this.form).then(res => {
        console.log('新增话题API调用成功', res);
      }).catch(err => {
        console.log('新增话题API调用失败', err);
      });
    },

    // 删除话题
    handleDeleteTopic(row) {
      // 添加二次确认弹窗
      this.$confirm('此操作将永久删除该话题, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认删除，执行本地操作
        const index = this.localTopics.findIndex(item => item.id === row.id);
        if (index !== -1) {
          this.localTopics.splice(index, 1); // 从数组中删除
          this.selectTopicList(); // 刷新表格数据
          this.$message.success('删除话题成功');

          // 保留API调用但不依赖其结果
          api.deleteTopic({topicId: row.id}).then(res => {
            console.log('删除话题API调用成功', res);
          }).catch(err => {
            console.log('删除话题API调用失败', err);
          });
        } else {
          this.$message.error('未找到要删除的话题');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    deleteTopic() {
      // 保留原有方法，但现在不使用
      this.checkTopic(this.topicId);
      var data = {
        topicId: this.topicId,
      };
      api.deleteTopic(data).then(response => {
        var res = response.data;
      }).catch(err => {
        console.log(err);
      });
    },
    checkTopic(id) {
      api.checkTopicDelete(id).then(response => {
        var res = response.data;
        if (res.status == '0') {
          this.$message({message: res.msg, type: 'error'});
        }
      })
    },
    updateTopic(row) {
      // 使用深拷贝避免数据引用问题
      this.form = {
        id: row.id,
        topic: row.topic,
        categoryId: row.categoryId,
        status: row.status,
        author: row.author,
        cteTime: row.cteTime
      };
      this.dialogFormVisible = true;
    },

    // 编辑话题提交方法
    handleSubmitFormSubmit() {
      // 表单验证
      if (!this.form.topic || this.form.topic.trim() === '') {
        this.$message.error("请输入话题内容");
        return;
      }

      if (!this.form.categoryId) {
        this.$message.error("请选择话题分类");
        return;
      }

      // 检查话题是否重复（排除自己）
      const exists = this.localTopics.find(item => item.topic === this.form.topic.trim() && item.id !== this.form.id);
      if (exists) {
        this.$message.error("话题内容已存在");
        return;
      }

      this.handleSubmitForm();
      this.dialogFormVisible = false; // 关闭弹窗
    },
    handleSubmitForm() {
      // 查找分类名称
      const category = this.localTopicCategories.find(cat => cat.id === this.form.categoryId);

      // 先执行本地操作
      const index = this.localTopics.findIndex(item => item.id === this.form.id);
      if (index !== -1) {
        // 更新本地数据
        this.localTopics[index] = {
          ...this.localTopics[index],
          topic: this.form.topic.trim(),
          categoryId: this.form.categoryId,
          categoryName: category ? category.name : '未分类',
          status: this.form.status
        };
        this.selectTopicList(); // 刷新表格数据
        this.$message.success('编辑话题成功');
      } else {
        this.$message.error('未找到要编辑的话题');
        return;
      }

      // 保留API调用但不依赖其结果
      api.updateTopic(this.form).then(res => {
        console.log('编辑话题API调用成功', res);
      }).catch(err => {
        console.log('编辑话题API调用失败', err);
      });
    }
  }
};
</script>
<style scoped>
/* 详情弹窗样式 */
.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 20px;
}

.section-title {
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.detail-item .label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.topic-content {
  background-color: #F5F7FA;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.status-normal {
  color: #67C23A;
  font-weight: 600;
}

.status-other {
  color: #F56C6C;
  font-weight: 600;
}

.category-tag {
  background-color: #E1F3D8;
  color: #67C23A;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}
</style>
