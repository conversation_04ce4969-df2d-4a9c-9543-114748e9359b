export const tips = [
  {
    cteTime: '2023-10-12',
    commentCount: 10,
    recommendCount: 5,
    collectionCount: 2,
    author: '贺翔',
    status: '删除',
    content: 'DCI出访和放行当前分为三种情况',
    topicId: 200331,
    topicName: '如何调整业务系统的部门?',
    id: 10001
  }, {
    cteTime: '2023-10-22',
    commentCount: 20,
    recommendCount: 10,
    collectionCount: 3,
    author: '贺翔',
    status: '正常',
    content: '如何分区挂载',
    topicId: 200332,
    topicName: '低效资源如何判定?',
    id: 10002
  }, {
    cteTime: '2023-11-01',
    commentCount: 30,
    recommendCount: 15,
    collectionCount: 4,
    author: '贺翔',
    status: '正常',
    content: '云窍agent掉线应如何处理',
    topicId: 200333,
    topicName: 'SDP权限如何延期?',
    id: 10003
  }, {
    cteTime: '2023-11-23',
    commentCount: 40,
    recommendCount: 20,
    collectionCount: 5,
    author: '贺翔',
    status: '正常',
    content: '云窍agent掉线排查步骤',
    topicId: 200334,
    topicName: '一级云系统如何延期上线?',
    id: 10004
  }
]

// 话题分类数据
export const topicCategories = [
  {
    id: 1,
    name: '技术问题',
    description: '技术相关的问题和讨论',
    cteTime: '2023-01-01'
  },
  {
    id: 2,
    name: '业务流程',
    description: '业务流程相关的话题',
    cteTime: '2023-01-01'
  },
  {
    id: 3,
    name: '系统运维',
    description: '系统运维和管理相关',
    cteTime: '2023-01-01'
  },
  {
    id: 4,
    name: '权限管理',
    description: '权限配置和管理相关',
    cteTime: '2023-01-01'
  },
  {
    id: 5,
    name: '其他',
    description: '其他类型的话题',
    cteTime: '2023-01-01'
  }
]

export const topics = [
  {
    cteTime: '2023-04-02',
    author: '卢莹',
    status: '正常',
    topic: '如何调整业务系统的部门?',
    categoryId: 2,
    categoryName: '业务流程',
    id: 200331
  }, {
    cteTime: '2023-05-22',
    author: '卢莹',
    status: '正常',
    topic: '低效资源如何判定?',
    categoryId: 3,
    categoryName: '系统运维',
    id: 200332
  }, {
    cteTime: '2023-06-12',
    author: '卢莹',
    status: '正常',
    topic: 'SDP权限如何延期?',
    categoryId: 4,
    categoryName: '权限管理',
    id: 200333
  }, {
    cteTime: '2023-08-24',
    author: '卢莹',
    status: '正常',
    topic: '一级云系统如何延期上线?',
    categoryId: 1,
    categoryName: '技术问题',
    id: 200334
  }
]

export const sentivewords = [
  {
    id: 1,
    content: "国家"
  },
  {
    id: 2,
    content: "政府"
  },
  {
    id: 3,
    content: "中国移动"
  },
  {
    id: 4,
    content: "内部"
  }
]

// 敏感词屏蔽规则配置
export const sentiveWordRules = [
  {
    id: 1,
    wordId: 1, // 对应敏感词ID
    wordContent: "国家", // 敏感词内容
    scopes: ['topic', 'tips', 'comment'], // 作用范围：话题、小知识、评论
    level: 'forbid', // 屏蔽级别：warn-警告, forbid-禁止发布, replace-自动替换
    action: 'block', // 处理方式：block-直接拦截, review-人工审核, filter-自动过滤
    status: 'enabled', // 规则状态：enabled-启用, disabled-禁用
    replaceText: '***', // 替换文本（当level为replace时使用）
    description: '涉及国家相关敏感内容，需要严格控制',
    cteTime: '2023-10-11 15:20:04',
    updateTime: '2023-10-11 15:20:04'
  },
  {
    id: 2,
    wordId: 2,
    wordContent: "政府",
    scopes: ['topic', 'tips'],
    level: 'warn',
    action: 'review',
    status: 'enabled',
    replaceText: '相关部门',
    description: '政府相关内容需要人工审核',
    cteTime: '2023-10-11 15:20:10',
    updateTime: '2023-10-11 15:20:10'
  },
  {
    id: 3,
    wordId: 3,
    wordContent: "中国移动",
    scopes: ['comment'],
    level: 'replace',
    action: 'filter',
    status: 'enabled',
    replaceText: '运营商',
    description: '自动替换为通用词汇',
    cteTime: '2023-10-11 15:20:30',
    updateTime: '2023-10-11 15:20:30'
  },
  {
    id: 4,
    wordId: 4,
    wordContent: "内部",
    scopes: ['topic', 'tips', 'comment'],
    level: 'warn',
    action: 'block',
    status: 'disabled',
    replaceText: '相关',
    description: '内部信息泄露风险提醒',
    cteTime: '2023-10-11 15:20:30',
    updateTime: '2023-10-11 15:20:30'
  }
]


export const announcements = [
  {
    id: 1,
    content: '新功能上线公告：系统新增了文章编辑功能，请各位用户尝新。',
    cteTime: '2023-05-21 19:30:23'
  },
  {
    id: 2,
    content: '漏洞修复公告：系统修复了用户登录功能的安全漏洞，系统安全性得到提升。',
    cteTime: '2023-07-20 17:23:46'
  },
  {
    id: 3,
    content: '漏洞修复公告：系统修复了用户信息越权访问漏洞，系统安全性得到提升。',
    cteTime: '2023-08-15 22:10:32'
  },
  {
    id: 4,
    content: '性能优化公告：系统优化了文章查询逻辑，提升了查询效率，优化了用户体验。',
    cteTime: '2023-09-23 20:43:32'
  },
  {
    id: 5,
    content: '漏洞修复公告：系统修复了附件上传功能的安全漏洞，系统安全性得到提升。',
    cteTime: '2023-10-20 22:13:24'
  },
]

export const collectTypes = [
  {
    id: 1,
    name: '需细读',
  },{
    id: 2,
    name: '优质',
  },{
    id: 3,
    name: '待阅读',
  }
]

export const artTipsCollects = [
  {
    id: 1001,
    content: '先收藏，后续细读',
    tip: '《一级云系统如何延期上线?》不错不错',
    authorName: '贺翔',
    type: 1,
    cteTime: '2024-01-10'
  },{
    id: 1002,
    content: '先收藏，后续细读',
    tip: '《SDP权限如何延期?》该话题真不错',
    authorName: '贺翔',
    type: 1,
    cteTime: '2024-03-20'
  },{
    id: 1003,
    content: '后面阅读',
    tip: '《低效资源如何判定?》值得阅读',
    authorName: '贺翔',
    type: 3,
    cteTime: '2024-04-02'
  },{
    id: 1004,
    content: '值得收藏',
    tip: '《如何调整业务系统的部门?》话题含金量很高',
    authorName: '贺翔',
    type: 2,
    cteTime: '2024-04-02'
  },{
    id: 1005,
    content: '值得收藏',
    tip: '《SDP权限如何延期?》不错不错',
    authorName: '贺翔',
    type: 2,
    cteTime: '2023-05-02'
  }
]

export const artTipsRecommends = [
  {
    id: 1001,
    content: '写的真不错',
    authorName: '贺翔',
    status: '正常',
    tip: '《SDP权限如何延期?》该话题真不错',
    cteTime: '2023-11-23'
  }, {
    id: 1002,
    content: '好！',
    authorName: '贺翔',
    status: '正常',
    tip: '《如何调整业务系统的部门?》值得阅读',
    cteTime: '2023-11-26'
  },{
    id: 1003,
    content: '推荐推荐',
    authorName: '贺翔',
    status: '正常',
    tip: '《低效资源如何判定?》话题含金量很高',
    cteTime: '2023-12-10'
  },{
    id: 1004,
    content: '很好！',
    authorName: '贺翔',
    status: '正常',
    tip: '《一级云系统如何延期上线?》不错不错',
    cteTime: '2023-12-14'
  }
]

export const notices = [
  {
    id: 1,
    content: '关于文章发布规范的说明',
    author: '贺翔',
    type: '草稿',
    cteTime: '2020-01-01 12:00:00'
  }
]

// 用户数据
export const users = [
  {
    id: 1001,
    name: '贺翔',
    account: 'hexiang',
    department: '思特奇',
    email: '<EMAIL>',
    phone: '***********'
  },
  {
    id: 1002,
    name: '刘兆光',
    account: 'liuzhaoguang',
    department: '思特奇',
    email: '<EMAIL>',
    phone: '***********'
  },
  {
    id: 1003,
    name: '杨永华',
    account: 'yangyonghua',
    department: '思特奇',
    email: '<EMAIL>',
    phone: '***********'
  },
]

export const roles = [
  {
    id: 1,
    name: '新增',
    cteTime: '2023-10-11 15:20:04'
  },
  {
    id: 2,
    name: '删除',
    cteTime: '2023-10-11 15:20:10'
  },
  {
    id: 3,
    name: '编辑',
    cteTime: '2023-10-11 15:20:30'
  },
  {
    id: 4,
    name: '导出',
    cteTime: '2023-10-11 15:20:30'
  }
]

// 角色人员关联数据
export const roleUserAssignments = [
  {
    roleId: 1,
    userIds: [1001] // 新增角色分配
  },
  {
    roleId: 2,
    userIds: [1002] // 删除角色分配
  },
  {
    roleId: 3,
    userIds: [1001, 1003] // 编辑角色分配
  },
  {
    roleId: 4,
    userIds: [1003] // 导出角色分配
  }
]

export const topicRecommends = [
  {
    cteTime: '2023-05-02',
    author: '贺翔',
    suggestion: '该话题相关文章较好的说明了一级云系统延期上线方面的问题，非常值得阅读',
    topicId: 200334,
    topic: '一级云系统如何延期上线?',
    id: 101
  },{
    cteTime: '2023-05-30',
    author: '贺翔',
    suggestion: '该话题非常值得阅读',
    topicId: 200332,
    topic: '低效资源如何判定?',
    id: 102
  },{
    cteTime: '2023-06-15',
    author: '贺翔',
    suggestion: '不错的话题',
    topicId: 200331,
    topic: '如何调整业务系统的部门?',
    id: 103
  },{
    cteTime: '2023-09-02',
    author: '贺翔',
    suggestion: '值得阅读',
    topicId: 200333,
    topic: 'SDP权限如何延期?',
    id: 104
  }
]

export function getRandomNumber(min, max) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return  Math.floor(Math.random() * (max - min + 1)) + min;
}
