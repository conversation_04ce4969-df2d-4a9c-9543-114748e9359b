<template>
  <div class="user_follow">
    <el-tabs class="tab1" v-model="activeName" tab-position="left">
      <el-tab-pane name="follow">
        <div slot="label" class="tab_name">
          <span>我的关注</span><span>{{userForm.followCount}}</span>
        </div>
        <myFollow v-if="activeName=='follow'" :id="id" @getUserInfo="getFollowerInfo"></myFollow>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import myFollow from '../perCenter/component/myFollow.vue';
import httpNew from '../../api/httpNew';

const api = new httpNew();
export default {
  name: 'follow' ,
  components: {myFollow},
  data() {
    return {
      activeName: 'follow',
      desc: '',
      id: '',
      userForm: {}
    };
  },
  methods: {
    getFollowerInfo:function(){
      api.getUserFollower(localStorage.getItem('forwordUser')).then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.userForm = res.data;
        }else {
          this.$message({message: res.msg ,type: 'error'});
        }
      }).catch(err => {
          console.log(err);
        });
    },
  }
}
</script>

<style scoped>

</style>
