<template>
  <div class="com-nav">
    <div class="nav">
      <div class="nav_list fs-14">
        <div class="left_nav">
          <div class="logo">
            <img class="image pointer" src="../../assets/image/tenant-logo1.png"  @click="returnHomePage"/>
          </div>
        </div>
        <div v-if="isSearch" class="search_box">
          <div>
            <i class="el-icon-close" @click="isSearch = false"></i>
            <el-popover
            	style="margin-top:-5px;"
					    placement="bottom"
					    width="100"
					    trigger="manual"
					    v-model="showSearchTypeMenu">
					    <ul class="type-list">
					    	<li @click="changeSearchType('全文搜索');">全文搜索</li>
					    	<li @click="changeSearchType('关键字搜索');">关键字搜索</li>
					    </ul>

					    <span slot="reference" class="type-choose" @click="showSearchTypeMenu=!showSearchTypeMenu">
			        	{{searchType}}<i class="mgl-2 el-icon-caret-bottom"></i>
							</span>
					  </el-popover>

            <el-select ref="search_box" v-model="searchData" filterable
            	placeholder=""
            	@blur="selectBlur"
            	@change="selectInfor"
            	@focus="getSearchInfo"
            	@keyup.enter.native="searchInfor">
              <el-option-group
                v-for="group in searchOptions"
                :key="group.label"
                :label="group.label"
              >
                <el-option
                  v-for="item in group.options"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-option-group>
            </el-select>
            <i class="el-icon-search" @click="toSearchPage"></i>
          </div>
        </div>
        <div class="nav_infor">
          <div class="input" v-show="!isSearch" @click="showSearchbox">
            <img src="../../assets/image/top1.png" alt="" />
            <span>搜索</span>
          </div>
          <div class="infor">
            <el-popover
					    placement="bottom"
					    width="400"
					    :append-to-body="false"
					    class="popbox"
					    @show="changeNoticeType('3');getNoticeCount();"
					    trigger="click">
					    <div class="notice-box">
					      <div class="header">
					      	<span class="classify fs-18" @click="changeNoticeType('3')" :class="noticeType=='3'?'active':''"><i class="iconfont icon-liebiao"><span v-if="noticeCount['3']&&noticeCount['3']>0" class="num">{{noticeCount['3']>99?'···':noticeCount['3']}}</span></i></span>
					      	<span class="line">|</span>
					      	<span class="classify fs-16" @click="changeNoticeType('2')" :class="noticeType=='2'?'active':''"><i class="iconfont icon-peixun"><span v-if="noticeCount['2']&&noticeCount['2']>0" class="num">{{noticeCount['2']>99?'···':noticeCount['2']}}</span></i></span>
					      	<span class="line">|</span>
					      	<span class="classify fs-16" @click="changeNoticeType('1')" :class="noticeType=='1'?'active':''"><i class="iconfont icon-aite"><span v-if="noticeCount['1']&&noticeCount['1']>0" class="num">{{noticeCount['1']>99?'···':noticeCount['1']}}</span></i></span>
					      </div>
					      <div class="content">
					      	<div v-if="noticeList.length>0">
					      		<el-scrollbar style="height:220px">
							      	<div class="content-item" v-for="(item,index) in noticeList">
							      		<template v-if="item.noticeType=='3'">
							      			{{item.noticeContent.authorName}}在专题
							      			<span class="href-addr" @click="toThemePage(item.noticeContent.parentSubjectId)">{{item.noticeContent.parentSubjectName}}<span v-if="item.noticeContent.subjectName">/{{item.noticeContent.subjectName}}</span></span>
							      			下发表了新文章<span class="href-addr" @click="toArticlePage(item.noticeContent.articleId)">《{{item.noticeContent.articleTitle}}》</span>，请查阅
							      		</template>
							      		<template v-else-if="item.noticeType=='2'">
							      			运维知识库<span class="href-addr" @click="queryTraining(item.noticeContent)">《{{item.noticeContent.title}}》</span>有{{item.noticeContent.userCount}}人希望组织相关培训，请查阅
							      		</template>
							      		<template v-if="item.noticeType=='1'">
							      			{{item.noticeContent.nick_name}}提醒你查看文章
							      			<span class="href-addr" @click="toArticlePage(item.noticeContent.articleId)">《{{item.noticeContent.articleTitle}}》</span>，
							      			提醒内容：{{item.noticeContent.content}}
							      		</template>
							      	</div>
						      	</el-scrollbar>
					      	</div>
					      	<div class="empty" v-else-if="!noticeLoading">
					      		<img src="../../assets/image/notice2.png" style="width:40px;" alt="" />
					      		<div class="mgt-10">暂时没有收到通知</div>
					      	</div>
					      	<div class="empty" v-loading ="noticeLoading" element-loading-text = "数据正在加载中"></div>
					      </div>
					    	<div class="footer"><div class="tr pdr-20" @click="toNoticePage">查看全部通知</div></div>
					    </div>
					    <span slot="reference" class="notice-icon"><img src="../../assets/image/top3.png" alt="" />
					    	<span v-if="noticeCountOfStore&&noticeCountOfStore>0" class="num"></span>
					    </span>
					  </el-popover>
          </div>
          <!--<div class="message">
          </div>-->
          <div class="avatar" v-if="currentUserId" @click="toPerCenter" @mouseenter="showTable">
            <img v-if="userAvatarOfStore" :src="userAvatarOfStore" alt="" :onerror="errorImg"/>
          </div>
          <div class="login_box color-9" v-else>
            <span @click="toLogin" style="margin-top:-2px;">登录</span>
          </div>
        </div>
      </div>
      <div class="hidden_box fs-13" v-show="isShow" @mouseleave="showTable">
        <!-- <p>{{currentUserAccount}}</p> -->
        <p>{{currentUserName}}</p>
        <ul>
          <li @click="setSystem" class="pointer">
            <img src="../../assets/image/shezhi.png" alt="" style="margin-bottom: -3px;"/>设置
          </li>
          <li @click="outSystem" class="pointer">
            <img src="../../assets/image/tuichu.png" alt=""/>退出
          </li>
        </ul>
      </div>
      <el-dialog class="common-dialog" title="申请培训话题" :visible.sync="knowledgeTrainDialog.visible" width="650px" :append-to-body="true">
	    	<div>
	    		<knowledgeComponent :id="knowledgeTrainDialog.id"></knowledgeComponent>
		  	</div>
	    </el-dialog>
    </div>
  </div>
</template>
<script>
import httpCore from "@/api/httpCore";
import httpNew from "@/api/httpNew";
const api = new httpCore();
const apiNew = new httpNew();
import errorPic from "@/assets/image/errorPic.png";
import knowledgeComponent from '@/views/knowledge/knowledge';
import {EncryptData,DecryptData} from "@/utils/aesUtils";
export default {
  name: "ComNav",
  data() {
    return {
      systemName: this.$store.getters.systemName,
      // activeIndex: "/home",
      activeIndex: "/theme",
      navList: [
        // { name: "首页", path: "/home" },
        { name: "专题", path: "/theme" },
      ],
      searchData: "",
      isSearch: false,
      searchOptions: [{label: "最近搜索",options: []},{label: "热门搜索",options: []}],
      isLogin: false,
      isShow: false,
      searchWord:"",
      currentUserId:this.$store.state.loginInfo.userId,
      currentUserAccount:this.$store.state.loginInfo.account,
      currentUserName:this.$store.state.loginInfo.userName,
      userForm:{},
      searchType:'全文搜索',
      showSearchTypeMenu:false,
      errorImg: 'this.src="' + errorPic + '"',
      noticeList:[],
      noticeType:'3',
      noticeLoading:false,
      noticeCount:{},
      knowledgeTrainDialog:{
    		visible:false,
    		id:''
    	},
      highManageMenu: [],
      highManagePower: false
    };
  },
  components:{knowledgeComponent},
  computed: {
    noticeCountOfStore () {
      return this.$store.getters.noticeCount
    },
    userAvatarOfStore () {
      return this.$store.getters.userAvatar
    },
  },
  methods: {
    toWritePage() {
      var routeUrl=this.$router.resolve({name:'writeArticle'});
      window.open(routeUrl.href, '_blank');
    },
    getSearchInfo:function(){
    	if(this.currentUserId){
    		this.searchData="";
	    	if(this.searchType=='全文搜索'){
	    		this.searchOptions=[{label: "最近搜索",options: []},{label: "热门搜索",options: []}];
	    		api.getSearchList().then(response => {
		    		var res = response.data;
						if(res.status == "0"){
							this.searchOptions[0].options = res.data.hisList;
							this.searchOptions[1].options = res.data.hotList;
						}else {
							this.$message({message: res.msg ,type: 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
	    	}else if(this.searchType=='关键字搜索'){
	    		this.searchOptions=[{label: "关键字搜索",options: []}];
	    		api.getKeyWords().then(response => {
		    		var res = response.data;
						if(res.status == "0"){
							this.searchOptions[0].options=res.data.list.map(function(item){
								return item.keyword;
							});
						}else {
							this.$message({message: res.msg ,type: 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
	    	}
    	}
    },
    showSearchbox() {
      this.isSearch = true;
      setTimeout(() => {
		    this.$refs.search_box.focus()
		  }, 500)
    },
    searchInfor(e) {
      // 搜索
      this.selectBlur(e);
      this.toSearchPage();
    },
    toSearchPage(){
    	var searchWord = this.searchData;
    	if(searchWord.trim()==""){
    		this.$message({
          message: '未输入搜索内容',
          type: 'warning'
        });
    	}else{
    		if(this.searchType=='全文搜索') this.saveSearchWord(searchWord);
    		var routeUrl=this.$router.resolve({name:'searchResultPage',query: { searchWord: searchWord,type: this.searchType }});
      	window.open(routeUrl.href, '_blank');
    		this.searchData="";
    		this.isSearch = false;
    	}
    },
    saveSearchWord:function(searchWord){
  		api.saveSearchWord({searchContent:searchWord}).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					//无处理
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
    selectInfor(value){
    	this.searchData = value;
    	this.toSearchPage();
    },
    selectBlur(e) {
			this.searchData = e.target.value;
    },
    toPerCenter() {
      // var routeUrl=this.$router.resolve({name:'perCenter',query:{id:this.$store.state.loginInfo.userId}});
      localStorage.setItem("forwordUser", this.$store.state.loginInfo.userId);
      var routeUrl=this.$router.resolve({name:'perCenter'});
      window.open(routeUrl.href, '_blank');
    },
    showTable(e) {
      console.log(e);
      this.isShow = !this.isShow;
    },
    outSystem() {
      // this.isLogin = true;
      window.close();
    },
    setSystem() {
    	var routeUrl=this.$router.resolve({name:'setsystem'});
      window.open(routeUrl.href, '_blank');
    },
    toLogin() {
//    this.$router.push("/login");
			// window.location.href ='/itsm/npage/lezhilogin.html';
			window.location.href ='/steward/index.html#/tenantLogin';
    },
    returnHomePage(){
    	this.$router.push("/home");
    	this.activeIndex="/home"
    },
    getUserInfo:function(){
    	if(this.currentUserId){
    		api.getUserInfoAES(this.currentUserId).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.userForm = res.data;
						this.$store.commit('setUserAvatar', this.userForm.headImg);
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	}
  	},
  	changeSearchType:function(type){
  		this.searchType=type;
  		this.showSearchTypeMenu=false;
  		this.$refs.search_box.focus();
  	},
  	changeNoticeType:function(type){
  		this.noticeLoading=true;
  		this.noticeType=type;
  		this.noticeList = [];
  		var queryParam={noticeType:type,noticeStatus:'0'};
  		api.queryNotice(queryParam).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.noticeLoading=false;
					var noticeList = res.data.list;
					noticeList.forEach(function(item){
						var obj = JSON.parse(item.noticeContent);
						item.noticeContent = obj;
					});
					this.noticeList = noticeList;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getNoticeCount:function(){
  		api.queryNoticeCount().then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.noticeCount = res.data;
					this.$store.commit('setNoticeCount', this.noticeCount['all']);
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	toNoticePage(){
	  	this.$router.push("/myNotice");
	  },
	  toThemePage(id){
	  	this.$router.push({ path: "/themeInner" , query: { themeId: id }});
	  },
	  toArticlePage(id){
	  	this.$router.push({ path: "/articleDetail" , query: { id: id }});
	  },
	  queryTraining(obj){
	  	if(obj.type=='1'){
	  		this.knowledgeTrainDialog.id = obj.trainingId;
	  		this.knowledgeTrainDialog.visible = true;
	  	}else if(obj.type=='2'){
	  		this.$router.push({ path: "/articleDetail" , query: { id: obj.trainingId }});
	  	}
	  },
    handleGoMenuPage(menu){
      // console.log(menu)
      this.activeIndex = "/" + menu.value
      var routeUrl = this.$router.resolve({name: menu.value});
      window.open(routeUrl.href, '_blank');
    },
    handleGetHighManagePower(){
      apiNew.getHighManagePower().then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.highManagePower = res.data;
        }else {
          this.$message({message: res.msg ,type: 'error'});
        }
      }).catch(err => {
        console.log(err);
      });
    },
    handleGetHighManageMenu(){
      apiNew.getHighManageMenu().then(res => {
        if (res.data.status == 0 && res.data.data){
          this.highManageMenu = res.data.data
        }
      })
    }
  },
  mounted() {
  	this.getUserInfo();
  	this.getSearchInfo();
    this.getNoticeCount();
    this.handleGetHighManagePower();
    this.handleGetHighManageMenu();
  },
};
</script>
<style lang="scss" scoped>
	.type-choose{
		font-size:13px;
		margin-left: 10px;
	}
	.type-list{
		font-size:13px;
		li{
			padding:5px 10px;
			color:#909199;
			text-align: center;
		}
		li:hover{
			color:#3E8CFF;
			cursor:pointer;
			opacity: 0.6;
		}
	}
  // 新增
  .left_nav{
    width: 300px !important;
  }
  .el-dropdown{
    width: 200px !important;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409EFF;
  }
  .el-icon-arrow-down {
    font-size: 12px;
  }
</style>
