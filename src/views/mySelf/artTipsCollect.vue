<template>
  <div>
    <NavComp/>
    <el-card class="pdt-60 box-card">
      <div slot="header" class="clearfix">
        <el-row :gutter="14">
          <el-col :span="14" :offset="5">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="flex: 1; text-align: center;">
                <h2>话题小知识收藏管理</h2>
              </div>
              <div>
                <el-button type="primary" plain @click="dialogFormVisible = true">新增分类</el-button>
                <el-button type="primary" plain @click="addCollectDialogFormVisible = true">新增收藏</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="14">
        <el-col :span="14" :offset="5">
          <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
              fixed
              prop="id"
              label="ID"
              width="60">
            </el-table-column>
            <el-table-column
              prop="tip"
              label="小知识"
              width="200">
            </el-table-column>
            <el-table-column
              prop="content"
              label="收藏意见">
            </el-table-column>
            <!--<el-table-column
              prop="authorName"
              label="收藏人"
              width="120">
            </el-table-column>-->
            <el-table-column
              prop="typeName"
              label="类别"
              width="120">
            </el-table-column>
            <el-table-column
              prop="cteTime"
              label="创建时间"
              width="120">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="200">
              <template slot-scope="scope">
                <el-button @click="handleGetCollectDetail(scope.row.id)" type="text" size="small">查看</el-button>
                <el-button @click='handleOpenEditCollectDialog(scope.row)' type="text" size="small">编辑</el-button>
                <el-button @click='handleDeleteCollect(scope.row.id)' type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-dialog title="新增分类" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="名称" :label-width="formLabelWidth">
            <el-input v-model="form.name" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSaveArtTipCollectTypeSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="新增收藏" :visible.sync="addCollectDialogFormVisible">
        <el-form :model="artTipsCollect">
          <el-form-item label="收藏小知识" :label-width="formLabelWidth">
            <el-select v-model="artTipsCollect.tipId" placeholder="请选择小知识" style="width: 100%;">
              <el-option v-for='item in tips()' :key='item.id' :label="item.content" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="收藏分类" :label-width="formLabelWidth">
            <el-select v-model="artTipsCollect.type" placeholder="请选择分类" style="width: 100%;">
              <el-option v-for='item in localCollectTypes' :key='item.id' :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="收藏意见" :label-width="formLabelWidth">
            <el-input v-model="artTipsCollect.content" placeholder="请输入收藏意见" autocomplete="off" style="width: 100%;"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addCollectDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleAddArtTipCollectSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="编辑收藏" :visible.sync="editCollectDialogFormVisible">
        <el-form :model="editForm">
          <el-form-item label="收藏小知识" :label-width="formLabelWidth">
            <el-input v-model="editForm.tip" disabled style="width: 100%;"></el-input>
          </el-form-item>
          <el-form-item label="收藏意见" :label-width="formLabelWidth">
            <el-input v-model="editForm.content" autocomplete="off" style="width: 100%;"></el-input>
          </el-form-item>
          <el-form-item label="收藏分类" :label-width="formLabelWidth">
            <el-select v-model="editForm.type" placeholder="请选择分类" style="width: 100%;">
              <el-option v-for='item in localCollectTypes' :key='item.id' :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editCollectDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleUpdateCollectSubmit">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 详情弹窗 -->
      <el-dialog title="收藏详情" :visible.sync="detailDialogVisible" width="70%" top="5vh">
        <div class="detail-content">
          <div class="detail-section">
            <div class="detail-item">
              <span class="label">收藏ID：</span>
              <span class="value">{{ artTipsDetail.id }}</span>
            </div>
            <div class="detail-item">
              <span class="label">收藏内容：</span>
              <span class="value content-text">{{ artTipsDetail.content }}</span>
            </div>
            <div class="detail-item">
              <span class="label">小知识：</span>
              <span class="value tip-content">{{ artTipsDetail.tip }}</span>
            </div>
            <div class="detail-item">
              <span class="label">分类：</span>
              <span class="value type-tag">{{ artTipsDetail.type }}</span>
            </div>
            <div class="detail-item">
              <span class="label">作者：</span>
              <span class="value">{{ artTipsDetail.authorName }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ artTipsDetail.cteTime }}</span>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import httpNew from '@/api/httpNew';
import {artTipsCollects, collectTypes, tips, getRandomNumber} from './data';
import NavComp from './navComp.vue';

const api = new httpNew();

export default {
  name: 'artTipsCollect',
  components: {NavComp},
  data() {
    return {
      tableData: [],
      localArtTipsCollects: JSON.parse(JSON.stringify(artTipsCollects)), // 深拷贝收藏数据
      localCollectTypes: JSON.parse(JSON.stringify(collectTypes)), // 深拷贝分类数据
      artTipsDetail: {},
      artTipsCollect: {},
      dialogFormVisible: false,
      addCollectDialogFormVisible: false,
      editCollectDialogFormVisible: false,
      detailDialogVisible: false,
      form: {
        name: '',
      },
      editForm: {
        id: '',
        content: '',
        tip: '',
        type: '',
        tipId: ''
      },
      formLabelWidth: '120px',
    };
  },
  mounted() {
    this.handleGetTipsCollectByTipId();
  },
  methods: {
    // 获取当前时间的格式化字符串
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    collectTypes() {
      return this.localCollectTypes
    },
    tips() {
      return tips
    },
    async handleGetTipsCollectByTipId() {
      // 保留API调用但不依赖其结果
      try {
        let res = await api.getArtTipCollectList()
        console.log('收藏列表API调用成功', res);
      } catch (err) {
        console.log('收藏列表API调用失败', err);
      }

      // 使用本地数据并添加类型名称
      this.tableData = [...this.localArtTipsCollects];
      this.tableData.forEach(item => {
        const typeObj = this.localCollectTypes.find(type => type.id == item.type);
        item.typeName = typeObj ? typeObj.name : '未知分类';
      })
    },
    // 新增分类提交方法
    handleSaveArtTipCollectTypeSubmit() {
      // 表单验证
      if (!this.form.name || this.form.name.trim() === '') {
        this.$message.error("请输入分类名称");
        return;
      }

      // 检查分类名称是否重复
      const exists = this.localCollectTypes.find(item => item.name === this.form.name.trim());
      if (exists) {
        this.$message.error("分类名称已存在");
        return;
      }

      this.handleSaveArtTipCollectType();
      this.dialogFormVisible = false; // 关闭弹窗
    },

    handleSaveArtTipCollectType(){
      // 生成新的分类对象
      const newType = {
        id: getRandomNumber(10, 999),
        name: this.form.name.trim()
      };

      // 先执行本地操作
      this.localCollectTypes.push(newType);
      this.$message.success("分类保存成功");

      // 重置表单
      this.form = { name: '' };

      // 保留API调用但不依赖其结果
      api.saveArtTipCollectType(this.form).then(res => {
        console.log('新增分类API调用成功', res);
      }).catch(err => {
        console.log('新增分类API调用失败', err);
      });
    },
    // 新增收藏提交方法
    handleAddArtTipCollectSubmit() {
      // 表单验证
      if (!this.artTipsCollect.tipId) {
        this.$message.error("请选择要收藏的小知识");
        return;
      }
      if (!this.artTipsCollect.type) {
        this.$message.error("请选择收藏分类");
        return;
      }
      if (!this.artTipsCollect.content || this.artTipsCollect.content.trim() === '') {
        this.$message.error("请输入收藏意见");
        return;
      }

      this.handleAddArtTipCollect();
      this.addCollectDialogFormVisible = false; // 关闭弹窗
    },

    handleAddArtTipCollect() {
      // 找到对应的tip内容
      const selectedTip = tips.find(tip => tip.id === this.artTipsCollect.tipId);

      // 生成新的收藏对象
      const newCollect = {
        id: getRandomNumber(10000, 99999),
        content: this.artTipsCollect.content.trim(),
        tip: selectedTip ? selectedTip.content : '未知小知识',
        authorName: '当前用户', // 可以根据实际情况修改
        type: this.artTipsCollect.type,
        cteTime: this.getCurrentTime()
      };

      // 先执行本地操作
      this.localArtTipsCollects.unshift(newCollect);
      this.handleGetTipsCollectByTipId(); // 刷新表格数据
      this.$message.success("收藏成功");

      // 重置表单
      this.artTipsCollect = {};

      // 保留API调用但不依赖其结果
      api.addArtTipCollect(this.artTipsCollect).then(res => {
        console.log('新增收藏API调用成功', res);
      }).catch(err => {
        console.log('新增收藏API调用失败', err);
      });
    },

    // 打开编辑收藏对话框
    handleOpenEditCollectDialog(row) {
      // 使用深拷贝避免数据引用问题
      this.editForm = {
        id: row.id,
        content: row.content,
        tip: row.tip,
        type: row.type,
        authorName: row.authorName,
        cteTime: row.cteTime
      };
      this.editCollectDialogFormVisible = true;
    },

    // 编辑收藏提交方法
    handleUpdateCollectSubmit() {
      // 表单验证
      if (!this.editForm.content || this.editForm.content.trim() === '') {
        this.$message.error("请输入收藏意见");
        return;
      }
      if (!this.editForm.type) {
        this.$message.error("请选择收藏分类");
        return;
      }

      this.handleUpdateCollect();
      this.editCollectDialogFormVisible = false; // 关闭弹窗
    },

    handleUpdateCollect() {
      // 先执行本地操作
      const index = this.localArtTipsCollects.findIndex(item => item.id === this.editForm.id);
      if (index !== -1) {
        // 更新本地数据
        this.localArtTipsCollects[index] = {
          ...this.localArtTipsCollects[index],
          content: this.editForm.content.trim(),
          type: this.editForm.type
        };
        this.handleGetTipsCollectByTipId(); // 刷新表格数据
        this.$message.success("修改成功");
      } else {
        this.$message.error("未找到要修改的收藏");
        return;
      }

      // 保留API调用但不依赖其结果
      api.updateArtTipCollect && api.updateArtTipCollect(this.editForm).then(res => {
        console.log('修改收藏API调用成功', res);
      }).catch(err => {
        console.log('修改收藏API调用失败', err);
      });
    },

    // 删除收藏
    handleDeleteCollect(id) {
      // 添加二次确认弹窗
      this.$confirm('此操作将永久删除该收藏, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认删除，执行本地操作
        const index = this.localArtTipsCollects.findIndex(item => item.id === id);
        if (index !== -1) {
          this.localArtTipsCollects.splice(index, 1); // 从数组中删除
          this.handleGetTipsCollectByTipId(); // 刷新表格数据
          this.$message.success("删除成功");

          // 保留API调用但不依赖其结果
          api.deleteArtTipCollect && api.deleteArtTipCollect(id).then(res => {
            console.log('删除收藏API调用成功', res);
          }).catch(err => {
            console.log('删除收藏API调用失败', err);
          });
        } else {
          this.$message.error("未找到要删除的收藏");
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 查看收藏详情
    handleGetCollectDetail(id) {
      // 从本地数据中查找
      const collect = this.localArtTipsCollects.find(item => item.id === id);
      if (collect) {
        this.artTipsDetail = {...collect};
        this.detailDialogVisible = true; // 打开详情弹窗
      } else {
        this.$message.error("详情查询失败");
        return;
      }

      // 保留API调用但不依赖其结果
      api.getArtTipCollectDetail && api.getArtTipCollectDetail(id).then(res => {
        console.log('详情API调用成功', res);
      }).catch(err => {
        console.log('详情API调用失败', err);
      });
    }
  }
};
</script>
<style scoped>
/* 详情弹窗样式 */
.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.detail-item .label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.content-text {
  background-color: #F5F7FA;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.tip-content {
  background-color: #FDF6EC;
  color: #E6A23C;
  padding: 6px 10px;
  border-radius: 4px;
  font-weight: 500;
}

.type-tag {
  background-color: #F0F9FF;
  color: #1890FF;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 确保下拉框内容居左对齐 */
.el-select .el-input__inner {
  text-align: left !important;
}

.el-select-dropdown .el-select-dropdown__item {
  text-align: left !important;
}
</style>
