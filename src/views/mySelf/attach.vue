<template>
  <div>
    <div class="mgt-15 fs-12" style="cursor: text;" v-if="contentObj.articleAttach.length>0" @click.stop="">
      <div class="color-9"><i class="mgr-5 el-icon-paperclip"></i>附件信息：</div>
      <div v-for="(att,index) in contentObj.articleAttach" class="att-block" @click='deleteAttach(att.id)'>
        <a :href="att.path">{{att.name}}</a>
      </div>
    </div>
  </div>
</template>

<script>
import httpNew from "@/api/httpNew";
const api = new httpNew();
export default{
  props:['articleId'],
  data(){
    return{
      contentObj: {}
    }
  },
  mounted(){
    this.getArticleAttach();
  },
  methods:{
    getArticleAttach(){
      api.getArticleAttach(this.articleId).then(response => {
        if(response.status == "0"){
          this.contentObj = response.data;
        }
      })
    },
    deleteAttach(id){
      api.deleteArticleAttach(this.articleId, id).then(response => {
        if(response.status == "0"){
          this.$message({message: '删除成功',type: 'info'});
        }
      })
    }
  }
}
</script>

<style>
</style>
