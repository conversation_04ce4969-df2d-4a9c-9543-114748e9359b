<template>
  <div class="topic_content article-contain">
    <div class="nav">
      <div class="left_nav">
        <div>话题推荐意见管理</div>
        <div class="title">#{{topicInfo.topic}}#</div>
      </div>
    </div>
    <div class="left_topic">
      <div class="topic_title">
        <div class="user_infor">
          <div class="image" style="border:none;">
            <div class="content">#</div>
            <img src="../../assets/image/talk.png" alt="" />
          </div>
          <div class="user">
            <div class="name" v-for='item in topicRecommSuggList'  :title="item.suggestion">#{{item.suggestion}}#</div>
          </div>
        </div>
        <div class="follow pointer" @click="updateTopicRecommSugg">更新</div>
        <div class="follow pointer" @click="deleteTopicRecommSugg">删除</div>
      </div>
    </div>
  </div>
</template>

<script>

import httpNew from "@/api/httpNew";
const api = new httpNew();
import errorPic from "@/assets/image/errorPic.png";
import {EncryptData,DecryptData} from "@/utils/aesUtils";
export default {
  name: "topicRecommendSuggestion",
  data() {
    return {
      topicId:this.$route.query.id,
      topicInfo:{},
      errorImg: 'this.src="' + errorPic + '"',
      topicRecommSuggList: [],
      submitForm: {},
      topicRecommSuggId: '',
      topicRecommSuggDetail: {}
    };
  },
  mounted(){
    this.selectTopicSuggList();
  },
  methods: {
    selectTopicSuggList(){
      api.getTopicRecommend(this.topicId).then(response => {
        var res = response.data;
        this.topicRecommSuggList = res.data.list;
        this.topicInfo = res.data.topicInfo;
      })
    },
    checkTopicRecommSuggUpdate(){
      api.checkTopicRecommendModify().then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.$message({message: res.msg ,type: 'error'});
        }
      })
    },
    updateTopicRecommSugg(){
      this.checkTopicRecommSuggUpdate()
      api.modifyTopicRecommend(this.submitForm).then(response => {
        var res = response.data;
        if (res.status == "0"){
          this.topicRecommSuggId = res.data;
          this.$message({message: res.msg ,type: 'success'});
        }
      })
    },
    getTopicRecommSuggDetail(id){
      api.getTopicRecommSuggDetail({suggId: id}).then(response => {
        var res = response.data;
        this.topicRecommSuggDetail = res.data;
      })
    },
    deleteTopicRecommSugg(id){
      this.checkDeleteTopicRecommSugg()
      api.removeTopicRecommend(id).then(response => {
        var res = response.data;
        if (res.status == "0"){
          this.$message({message: '删除成功' ,type: 'success'});
        }
      })
    },
    checkDeleteTopicRecommSugg(){
      api.checkTopicRecommendDelete().then(response => {
        var res = response.data;
        if (res.status == "0"){
          this.$message.error("无权限删除")
        }
      })
    }
  },
};
</script>
<style>
</style>
