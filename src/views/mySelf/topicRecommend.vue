<template>
  <div>
    <NavComp/>
    <el-card class="pdt-60 box-card">
      <div slot="header" class="clearfix">
        <el-row :gutter="14">
          <el-col :span="14" :offset="5">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="flex: 1; text-align: center;">
                <h2>话题推荐管理</h2>
              </div>
              <div>
                <el-button type="primary" plain @click="handleOpenAddDialog">新增</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="14">
        <el-col :span="14" :offset="5">
          <el-table
            :data="tableData"
            border
            style="width: 100%">
            <el-table-column
              fixed
              prop="id"
              label="ID"
              width="80">
            </el-table-column>
            <el-table-column
              prop="topic"
              label="话题">
            </el-table-column>
            <el-table-column
              prop="suggestion"
              label="推荐意见">
            </el-table-column>
            <el-table-column
              prop="cteTime"
              label="创建时间"
              width="120">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="160">
              <template slot-scope="scope">
                <el-button @click="handleGetTopicDetail(scope.row)" type="text" size="small">详情</el-button>
                <el-button @click='handleOpenEditDialog(scope.row)' type="text" size="small">编辑</el-button>
                <el-button @click='handleRemoveTopicRecommend(scope.row)' type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-dialog title="新增推荐" :visible.sync="addDialogFormVisible">
        <el-form :model="form">
          <el-form-item label="话题" :label-width="formLabelWidth">
            <el-select v-model="form.topicId" placeholder="请选择话题" style="width: 100%;" filterable>
              <el-option
                v-for="item in localTopics"
                :key="item.id"
                :label="item.topic"
                :value="String(item.id)">
                <span>{{ item.topic }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="推荐意见" :label-width="formLabelWidth">
            <el-input v-model="form.suggestion" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="addDialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleAddTopicRecommendSubmit">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="编辑推荐" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="推荐意见" :label-width="formLabelWidth">
            <el-input v-model="form.suggestion" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleUpdateTopicRecommendSubmit">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 详情弹窗 -->
      <el-dialog title="推荐详情" :visible.sync="detailDialogVisible" width="70%" top="5vh">
        <div class="detail-content">
          <div class="detail-section">
            <div class="detail-item">
              <span class="label">推荐ID：</span>
              <span class="value">{{ topicDetail.id }}</span>
            </div>
            <div class="detail-item">
              <span class="label">话题：</span>
              <span class="value topic-content">{{ topicDetail.topic }}</span>
            </div>
            <div class="detail-item">
              <span class="label">推荐意见：</span>
              <span class="value content-text">{{ topicDetail.suggestion }}</span>
            </div>
            <div class="detail-item">
              <span class="label">作者：</span>
              <span class="value">{{ topicDetail.author }}</span>
            </div>
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ topicDetail.cteTime }}</span>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import httpNew from '@/api/httpNew';
import {topicRecommends, topics, getRandomNumber} from './data';
import NavComp from './navComp.vue';

const api = new httpNew();

export default {
  name: 'topicRecommend',
  components: {NavComp},
  data() {
    return {
      tableData: [],
      localTopicRecommends: JSON.parse(JSON.stringify(topicRecommends)), // 深拷贝话题推荐数据
      localTopics: JSON.parse(JSON.stringify(topics)), // 深拷贝话题数据
      topicDetail: {},
      topicRecommend: {},
      dialogFormVisible: false,
      addDialogFormVisible: false,
      detailDialogVisible: false,
      form: {
        id: '',
        suggestion: '',
        topicId: '',
        topic: '',
      },
      formLabelWidth: '120px',
    };
  },
  mounted() {
    this.handleGetTopicSuggestionList();
  },
  methods: {
    // 获取当前时间的格式化字符串
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    async handleGetTopicSuggestionList() {
      // 保留API调用但不依赖其结果
      try {
        let res = await api.getTopicSuggestionList(this.$route.query.topicId)
        console.log('话题推荐列表API调用成功', res);
      } catch (e) {
        console.log('话题推荐列表API调用失败', e);
      }

      // 使用本地数据
      this.tableData = [...this.localTopicRecommends];

      // 如果有话题ID，过滤对应话题的推荐
      if (this.$route.query.topicId) {
        // 这里可以根据实际需求过滤特定话题的推荐
        // this.tableData = this.tableData.filter(item => item.topicId === this.$route.query.topicId);
      }
    },

    // 打开新增对话框
    handleOpenAddDialog() {
      // 重置表单
      this.form = {
        id: '',
        suggestion: '',
        topicId: '',
        topic: '',
      };
      this.addDialogFormVisible = true;
    },

    // 新增推荐提交方法
    handleAddTopicRecommendSubmit() {
      // 表单验证
      if (!this.form.topicId) {
        this.$message.error("请选择话题");
        return;
      }
      if (!this.form.suggestion || this.form.suggestion.trim() === '') {
        this.$message.error("请输入推荐意见");
        return;
      }

      this.handleAddTopicRecommend();
      this.addDialogFormVisible = false; // 关闭弹窗
    },

    // 新增推荐
    handleAddTopicRecommend() {
      // 将字符串ID转换为数字并查找话题名称
      const topicId = parseInt(this.form.topicId);
      const topic = this.localTopics.find(t => t.id === topicId);

      // 生成新的推荐对象
      const newRecommend = {
        id: getRandomNumber(1000, 9999),
        topicId: topicId,
        topic: topic ? topic.topic : '未知话题',
        suggestion: this.form.suggestion.trim(),
        author: '当前用户', // 可以根据实际情况修改
        cteTime: this.getCurrentTime()
      };

      // 先执行本地操作
      this.localTopicRecommends.unshift(newRecommend);
      this.handleGetTopicSuggestionList(); // 刷新表格数据
      this.$message.success('新增推荐成功');

      // 保留API调用但不依赖其结果
      api.addTopicRecommend && api.addTopicRecommend(this.form).then(res => {
        console.log('新增推荐API调用成功', res);
      }).catch(err => {
        console.log('新增推荐API调用失败', err);
      });
    },

    handleOpenEditDialog(row) {
      // 使用深拷贝避免数据引用问题
      this.form = {
        id: row.id,
        topic: row.topic,
        suggestion: row.suggestion,
        author: row.author,
        cteTime: row.cteTime
      };
      this.dialogFormVisible = true;
    },

    // 编辑推荐提交方法
    handleUpdateTopicRecommendSubmit() {
      // 表单验证
      if (!this.form.suggestion || this.form.suggestion.trim() === '') {
        this.$message.error("请输入推荐意见");
        return;
      }

      this.handleUpdateTopicRecommend();
      this.dialogFormVisible = false; // 关闭弹窗
    },
    // 查看推荐详情
    handleGetTopicDetail(row) {
      // 从本地数据中查找
      const recommend = this.localTopicRecommends.find(item => item.id === row.id);
      if (recommend) {
        this.topicDetail = {...recommend};
        this.detailDialogVisible = true; // 打开详情弹窗
      } else {
        this.$message.error("详情查询失败");
        return;
      }

      // 保留API调用但不依赖其结果
      api.getTopicsDetail && api.getTopicsDetail(row.id).then(res => {
        console.log('详情API调用成功', res);
      }).catch(err => {
        console.log('详情API调用失败', err);
      });
    },
    /*handleAddTopicRecommend(row) {
      api.addTopicRecommend(this.topicRecommend).then(res => {
        if (res.status == "0") {
          this.$message.error('新增推荐失败');
        }
        this.$message.success('新增推荐成功');
      });
    },*/
    handleRemoveTopicRecommend(row) {
      // 添加二次确认弹窗
      this.$confirm('此操作将永久删除该推荐, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认删除，执行本地操作
        const index = this.localTopicRecommends.findIndex(item => item.id === row.id);
        if (index !== -1) {
          this.localTopicRecommends.splice(index, 1); // 从数组中删除
          this.handleGetTopicSuggestionList(); // 刷新表格数据
          this.$message.success('删除推荐成功');

          // 保留API调用但不依赖其结果
          api.removeTopicRecommend(row.id).then(res => {
            console.log('删除推荐API调用成功', res);
          }).catch(err => {
            console.log('删除推荐API调用失败', err);
          });
        } else {
          this.$message.error('未找到要删除的推荐');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    handleUpdateTopicRecommend() {
      // 先执行本地操作
      const index = this.localTopicRecommends.findIndex(item => item.id === this.form.id);
      if (index !== -1) {
        // 更新本地数据
        this.localTopicRecommends[index] = {
          ...this.localTopicRecommends[index],
          suggestion: this.form.suggestion.trim()
        };
        this.handleGetTopicSuggestionList(); // 刷新表格数据
        this.$message.success('更新推荐成功');
      } else {
        this.$message.error('未找到要更新的推荐');
        return;
      }

      // 保留API调用但不依赖其结果
      api.updateTopicRecommend(this.form).then(res => {
        console.log('更新推荐API调用成功', res);
      }).catch(err => {
        console.log('更新推荐API调用失败', err);
      });
    }
  }
};
</script>
<style scoped>
/* 详情弹窗样式 */
.detail-content {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.detail-item .label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.content-text {
  background-color: #F5F7FA;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.topic-content {
  background-color: #ECF5FF;
  color: #409EFF;
  padding: 6px 10px;
  border-radius: 4px;
  font-weight: 500;
}
</style>
