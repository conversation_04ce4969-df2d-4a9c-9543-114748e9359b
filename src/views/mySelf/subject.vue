<template>
  <div class="login_home">
    <div class="login_box">
      <div class="register">专题</div>
      <div class="form_content">
        <el-form :model="subjectForm" :rules="rules1" ref="subjectForm">
          <el-form-item prop="name">
            <el-input
              id="el_name"
              placeholder="名称"
              v-model="subjectForm.name"
              prefix-icon="el-icon-user-solid"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-button type="primary" @click="submitForm('subjectForm')">提交</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <p>专题下文章</p>
    <ul>
      <li v-for="(item,index) in articleList" :key="index">{{item.title}}</li>
    </ul>
  </div>
</template>

<script>
import httpNew from "@/api/httpNew";
const api = new httpNew();
export default {
  name: "subject",
  data() {
    return {
      subjectForm: {},
      rules1: {
        name: [
          { required: true, message: "请输入名称", trigger: "blur" },
        ],
      },
      subjectId: '',
      articleList: []
    };
  },
  created() {
    this.getDraftBySubject()
  },
  methods: {
    submitForm() {},
    getDraftBySubject(){
      api.getDraftBySubject(this.subjectId).then(response => {
        var res = response.data;
        this.articleList = res.data;
      })
    }
  },
  computed: {},
  mounted() {

  },
};
</script>
<style>
</style>
