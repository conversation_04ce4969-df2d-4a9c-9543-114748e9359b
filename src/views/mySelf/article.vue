<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <h2>文章管理</h2>
      <!--<el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>-->
    </div>
    <el-row :gutter="20">
      <!--<el-col :span="10" :offset="1">
        <el-row>
          <el-button type="success">新增</el-button>
        </el-row>
      </el-col>-->
      <el-col :span="14" :offset="5">
        <el-table
          :data="tableData"
          border
          style="width: 100%">
          <el-table-column
            fixed
            prop="id"
            label="ID"
            width="150">
          </el-table-column>
          <el-table-column
            prop="title"
            label="标题"
            width="300">
          </el-table-column>
          <el-table-column
            prop="authorName"
            label="作者"
            width="120">
          </el-table-column>
          <el-table-column
            prop="status"
            label="状态"
            width="120">
          </el-table-column>
          <el-table-column
            prop="cteTime"
            label="创建时间"
            width="120">
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="160">
            <template slot-scope="scope">
              <el-button @click="handleSetTop(scope.row)" type="text" size="small">置顶</el-button>
              <el-button @click='handleCancelRecommend(scope.row)' type="text" size="small">取消推荐</el-button>
              <el-button @click='handleCancelFollow(scope.row)' type="text" size="small">取消关注</el-button>
              <el-button @click='handleCancelCollect(scope.row)' type="text" size="small">取消收藏</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import httpNew from '@/api/httpNew';
import {articles} from './data';

const api = new httpNew();

export default {
  name: 'article',
  data() {
    return {
      tableData: []
    };
  },
  mounted() {
    this.handleGetArticleList();
    // this.tableData = articles;
  },
  methods: {
    handleGetArticleList(){
      api.getArticleList().then(res => {
        let articleList = res.data.data.list;
        if (articleList && articleList.length > 0) {
          let arr = articleList.map(item => {
            return {
              ...item,
              status: item.status == '1' ? "发布" : (item.status == '2' ? "草稿" : "删除"),
            }
          })
          this.tableData = arr;
        }
      })
    },
    handleSetTop(row){
      api.setArticleTop(row.id).then(response => {
        if(response.status == "0"){
          this.$message.success("文章置顶成功")
        }
      })
    },
    handleCancelRecommend(row){
      api.cancelRecommend(row.id).then(response => {
        if(response.status == "0"){
          this.$message.success("取消推荐成功")
        }
      })
    },
    handleCancelFollow(row){
      api.cancelFollow(row.id).then(response => {
        if(response.status == "0"){
          this.$message.success("取消关注成功")
        }
      })
    },
    handleCancelCollect(row){
      api.cancelCollect(row.id).then(response => {
        if(response.status == "0"){
          this.$message.success("取消收藏成功")
        }
      })
    }
  }
};
</script>
<style>
</style>
