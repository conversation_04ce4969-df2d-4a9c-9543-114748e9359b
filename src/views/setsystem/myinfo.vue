<template>
  <div class="setUser pdt-30">
    <div class="userInfor">
      <div class="user_title">我的信息</div>
      <div class="avatar_box">
        <div class="image">
          <!-- 背景上传 -->
          <el-image
            class="avatar"
            v-if="userForm.headImg"
            :src="userForm.headImg"
            fit="cover"></el-image>
        </div>
      </div>
      <el-form
        :model="userForm"
        ref="userForm"
        label-width="80px"
        label-position="left"
      >
        <el-form-item label="姓名">
          <el-input v-model="userForm.nickName" disabled readonly></el-input>
        </el-form-item>
        <el-form-item label="个人介绍">
          <el-input type="textarea" v-model="userForm.personalSignature" disabled readonly></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="userForm.gender" disabled readonly>
            <el-radio label="1">男</el-radio>
            <el-radio label="2">女</el-radio>
            <el-radio label="3">保密</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="userForm.phone" disabled readonly></el-input>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import httpCore from "@/api/httpCore";
const api = new httpCore();
export default {
  name: 'myinfo',
  data() {
    return {
      userForm: {
        headImg: '',
        nickName: '',
        personalSignature: '',
        gender: '',
        phone: '',
      },
      downloadPath:'/file-upload-service/v1/paas_attachments_tool/download?id=',
    }
  },
  created() {
    this.getUserInfoById(this.$store.state.loginInfo.userId)
  },
  methods: {
    getUserInfoById(id){
      api.getUserInfoById(id).then(response => {
        var res = response.data;
        if(res.status == "0"){
          this.userForm = res.data;
          this.userForm.headImg = this.downloadPath + this.userForm.headImg;
        }else {
          this.$message({message: res.msg ,type: 'error'});
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
