<template>
  <div class="setUser pdt-30">
    <div class="userInfor">
      <div class="user_title">个人资料</div>
      <div class="avatar_box">
        <div class="image">
          <!-- 背景上传 -->
          <el-image
			      class="avatar"
			      v-if="userForm.headImg"
			      :src="userForm.headImg"
			      fit="cover"></el-image>
        </div>
        <el-upload
          class="avatar-uploader"
	      	accept=".gif,.jpg,.jpeg,.bmp,.png"
	       	:show-file-list="false"
	       	:action="uploadPath"
	       	:on-success="handleAvatarSuccess"
          :before-upload="checkAcceptType"
          :on-error="uploadError"
		    >
          <div class="change_avatar">更改头像</div>
        </el-upload>
      </div>
      <el-form
        :model="userForm"
        ref="userForm"
        label-width="80px"
        label-position="left"
      >
        <el-form-item label="姓名" prop="nickName"
        	:rules="[
			      { required: true, message: '请输入姓名', trigger: 'blur' }
			    ]"
			  >
          <el-input v-model="userForm.nickName" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="个人介绍" prop="personalSignature">
          <el-input type="textarea" v-model="userForm.personalSignature" placeholder="填写你的个人简介"></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="userForm.gender">
            <el-radio label="1">男</el-radio>
            <el-radio label="2">女</el-radio>
            <el-radio label="3">保密</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="手机" prop="phone" class=""
        	:rules="[
        		{pattern: /^1[3-9]\d{9}$/,message:'请输入正确的手机号', trigger: ['change','blur']}
			    ]"
			  >
          <el-input v-model="userForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <!--<el-form-item label="密码" prop="password" class=""
        	:rules="[
        		{ min: 5,max: 25,message: '长度在 5 到 25个字符'},
						{ pattern: /^(\w){5,25}$/, message: '只能输入5-25个字母、数字、下划线'}
			    ]"
			  >
          <el-input
            show-password
            v-model="userForm.password"
            placeholder="请输入密码"
            autocomplete="new-password"
          ></el-input>
        </el-form-item>-->
        <el-button @click="submitForm('userForm')" class="submit_button"
          >保存</el-button
        >
      </el-form>
    </div>
    <div class="nav">设置</div>
  </div>
</template>

<script>
import httpCore from "@/api/httpCore";
const api = new httpCore();
export default {
  name: "SetSystem",
  data() {
    return {
      userForm: {},
      uploadPath:'/file-upload-service/v1/paas_attachments_tool/upload/lezhi-service',
      downloadPath:'/file-upload-service/v1/paas_attachments_tool/download?id=',
      typeList:[
        'image/bmp',//.bmp
        'image/gif',//.gif
        'image/vnd.microsoft.icon',//.ico
        'image/jpeg',//.jpeg .jpg
        'image/png',//.png
        'image/svg+xml',//.svg
        'image/webp',//.webp
      ],
      extList:[
        '.bmp',
        '.gif',
        '.ico',
        '.jpeg','.jpg',
        '.png',
        '.svg',
        '.webp',
      ],
    };
  },
  mounted(){
  	this.getMyInfo();
  },
  methods: {
  	getMyInfo:function(){
  		api.getCurrentUser().then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.userForm = res.data;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
        	api.updateUserInfo(this.userForm.id,this.userForm).then(response => {
		    		var res = response.data;
						if(res.status == "0"){
							this.$store.commit('setUserAvatar', this.userForm.headImg);
							this.$message({message: '个人信息保存成功' ,type: 'success'});
							this.$router.go(-1);
						}else {
							this.$message({'message': res.msg ,'type': 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
        } else {
          return false;
        }
      });
    },
    handleAvatarSuccess(res, file) {
      this.userForm.headImg = this.downloadPath+res.data;
    },
    checkAcceptType(file) {
      // console.log(file);
      if(this.typeList.indexOf(file.type)<0 && this.extList.findIndex(ext => file.name.endsWith(ext))<0){
        this.$message({message: '附件格式不允许上传' ,type: 'warning'});
        return false;
      }
    },
    uploadError(err, file, fileList){
      this.$message({message: '附件上传失败' ,type: 'error'});
    },

  },
};
</script>
<style>
</style>
