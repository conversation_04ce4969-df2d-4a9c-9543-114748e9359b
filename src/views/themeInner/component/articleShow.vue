<template>
	<div>
		<div @click="toArticleDetail(contentObj.id)">
			<div style="cursor:text" @click.stop="" v-if="contentObj.subjectSequence&&showClassify" class="theme-classify mgr-10"
  			:class="'theme-classify-'+contentObj.subjectSequence%5" >{{ contentObj.subjectName }}</div>
			<div class="title inline-block">{{ contentObj.title }}</div>
	    <div class="content">
	    	<articleComponent :contentObj="contentObj"></articleComponent>
	    </div>
    </div>
    <div class="footer">
      <div class="user">
        <img @click="toPerCenter(contentObj.author)" v-if="contentObj.authorHeadImg" :src="contentObj.authorHeadImg" alt="" :onerror="errorImg"/><span>{{contentObj.authorName}}</span>
      </div>
      <div class="infor">
        <span class="pointer" @click="goComment(contentObj.id)"><img src="../../../assets/image/inner2.png" alt="" />{{contentObj.commentCount}}评论</span>
        <span class="pointer" @click="recommend(contentObj.id)"><img :src="require(`../../../assets/image/recommend_${contentObj.recommendFlag}.png`)" alt="" />{{contentObj.recommendCount}}推荐</span>
        <span class="pointer" @click="collect(contentObj.id)"><img :src="require(`../../../assets/image/collect_${contentObj.collectionFlag}.png`)" alt="" />{{contentObj.collectionCount}}收藏</span>
      	<div @click="requestTrain" class="train-request pointer"><img src="../../../assets/image/train-request.png" style="width:20px"/>培训申请</div>
      </div>
    </div>
	</div>
</template>

<script>
	import httpCore from "@/api/httpCore";
	const api = new httpCore();
	import articleComponent from '../../article/article';
	import errorPic from "@/assets/image/errorPic.png";
  import {EncryptData,DecryptData} from "@/utils/aesUtils";
	export default {
    props:['contentObj','showClassify'],
    components:{articleComponent},
    data(){
    	return{
    		errorImg: 'this.src="' + errorPic + '"',
    	}
    },
    methods:{
    	goComment(id){
  			var routeUrl=this.$router.resolve({name:'articleDetail',query: { id: id,goComment: true }});
    		window.open(routeUrl.href, '_blank');
    	},
    	toArticleDetail(id){
    		var routeUrl=this.$router.resolve({name:'articleDetail',query: { id: id}});
    		window.open(routeUrl.href, '_blank');
    	},
    	recommend(id){
    		var data={
    			recommendId:id,
    			recommendType:'2'
    		};
    		api.recommend(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.contentObj.recommendFlag=!this.contentObj.recommendFlag;
						this.contentObj.recommendFlag?this.contentObj.recommendCount++:this.contentObj.recommendCount--;
						this.$message({message: res.msg ,type: 'success'});
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	},
    	collect(id){
    		var data={
    			collectionId:id,
    			collectionType:'2'
    		};
    		api.collect(data).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.contentObj.collectionFlag=!this.contentObj.collectionFlag;
						this.contentObj.collectionFlag?this.contentObj.collectionCount++:this.contentObj.collectionCount--;
						this.$message({message: res.msg ,type: 'success'});
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
    	},
    	toPerCenter(id) {
	      // var routeUrl=this.$router.resolve({name:'perCenter',query: { id: id }});
        localStorage.setItem("forwordUser", id);
        var routeUrl=this.$router.resolve({name:'perCenter'});
	      window.open(routeUrl.href, '_blank');
	    },
	    requestTrain(){
	  		var data={trainingId:this.contentObj.id,title:this.contentObj.title,type:"2"};
		  	api.addNotice({noticeType:'2',noticeContent: JSON.stringify(data)}).then(response => {
		  		var res = response.data;
					if(res.status == "0"){
						if(res.data=='1'){
							this.$message({'message': '您已申请过，无法重复申请' ,'type': 'warning'});
						}else{
							this.$message({'message': '申请成功' ,'type': 'success'});
						}
					}else {
						this.$message({'message': res.msg ,'type': 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
    }
  }
</script>

<style>
</style>
