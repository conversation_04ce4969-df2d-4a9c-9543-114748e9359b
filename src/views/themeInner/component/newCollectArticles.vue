<template>
	<div class="article_infor">
		<div class="article" v-for="(contentObj, index) in newCollectArticles" :key="index">
      <articleComponent :key="Math.random()" :contentObj="contentObj" :showClassify="themeId==queryParam.subjectId"></articleComponent>
    </div>
    <div v-show="totalPage>1&&queryParam.pageNum!=totalPage&&!loading" class="mgt-10 fs-13" style="opacity: 0.5;">
    	<span class="pointer" @click="loadMore">
	      <span>查看更多</span>
      </span>
    </div>
	  <div style="margin-top:150px" v-if="!loading&&newCollectArticles.length==0">
    	<img src="../../../assets/image/empty.png" alt="" />
    	<div>暂无足迹~</div>
    </div>
    <div class="mgt-30" v-loading ="loading" element-loading-text = "数据正在加载中"></div>
  </div>
</template>

<script>
	import httpCore from "@/api/httpCore";
	const api = new httpCore();
	import articleComponent from './articleShow';
	export default {
    data(){
      return{
      	loading:true,
	      newCollectArticles:[],
	      queryParam:{
	      	pageNum:1,
      		pageSize:5,
      		subjectId:this.themeId
	      },
	      totalPage:0,
      }
    },
    props:['themeId','classify'],
    components:{articleComponent},
    mounted(){
//	  	this.getNewCollectArticles();
	  },
	  watch:{
	  	'classify':{
	  		immediate: true,
        handler(val, oldVal) {
          this.queryParam.pageNum=1;
          this.queryParam.subjectId=val;
			  	this.newCollectArticles=[];
			  	this.getNewCollectArticles();
        }
      }
	  },
    methods:{
    	getNewCollectArticles(){
	    	this.loading=true;
	    	var param = this.queryParam;
	  		api.getNewCollectArticlesOfTheme(param).then(response => {
	    		var res = response.data;
	    		this.loading=false;
					if(res.status == "0"){
						var newCollectArticles = res.data.list;
						newCollectArticles.forEach(function(item){
			  			item.content = item.content.replace(/style=\"[^\"]*\"/g, "");
			  		});
			  		this.newCollectArticles = this.newCollectArticles.concat(newCollectArticles);
						this.totalPage = res.data.pages;
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	loadMore:function(){
	  		if(this.queryParam.pageNum<this.totalPage){
	    		this.queryParam.pageNum++;
	    		this.getNewCollectArticles();
	    	}
		  },
    }
  }
</script>

<style>
</style>