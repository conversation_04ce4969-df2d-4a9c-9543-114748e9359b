<template>
	<div class="article_infor">
		<div class="article" v-for="(contentObj, index) in hotArticles" :key="index">
      <articleComponent :key="Math.random()" :contentObj="contentObj"  :showClassify="themeId==queryParam.subjectId"></articleComponent>
    </div>
    <div v-show="totalPage>1&&queryParam.pageNum!=totalPage" class="mgt-10 fs-13" style="opacity: 0.5;">
    	<span class="pointer" @click="loadMore">
	      <span>查看更多</span>
      </span>
    </div>
	  <div style="margin-top:150px" v-if="!loading&&hotArticles.length==0">
    	<img src="../../../assets/image/empty.png" alt="" />
    	<div>暂无足迹~</div>
    </div>
    <div class="mgt-30" v-loading ="loading" element-loading-text = "数据正在加载中"></div>
  </div>
</template>

<script>
	import httpCore from "@/api/httpCore";
	const api = new httpCore();
	import articleComponent from './articleShow';
	export default {
    data(){
      return{
      	loading:true,
	      hotArticles:[],
	      queryParam:{
	      	pageNum:1,
      		pageSize:5,
      		subjectId:this.themeId,
      		sorts:'hotList'
	      },
	      totalPage:0,
      }
    },
    props:['themeId','classify'],
    components:{articleComponent},
    mounted(){
//  	this.hotArticles=[];
//	  this.getHotArticles();
//    this.getHotArticleList();
	  },
	  watch:{
	  	'classify':{
	  		immediate: true,
        handler(val, oldVal) {
          this.queryParam.pageNum=1;
          this.queryParam.subjectId=val;
			  	this.hotArticles=[];
			  	this.getHotArticles();
        }
      }
	  },
    methods:{
    	getHotArticles(){
	    	this.loading=true;
	    	var param = this.queryParam;
	  		api.getNewCollectArticlesOfTheme(param).then(response => {
	    		var res = response.data;
	    		this.loading=false;
					if(res.status == "0"){
						var hotArticles = res.data.list;
						hotArticles.forEach(function(item){
			  			item.content = item.content.replace(/style=\"[^\"]*\"/g, "");
			  		});
			  		this.hotArticles = this.hotArticles.concat(hotArticles);
						this.totalPage = res.data.pages;
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
      getHotArticleList(){
        api.getHotArticleList().then(response => {
          var res = response.data;
          if(res.status == "0"){
            this.hotArticles = res.data.list;
          }else {
            this.$message({message: res.msg ,type: 'error'});
          }
        }).catch(err => {
           console.log(err);
         });
      },
	  	loadMore:function(){
	  		if(this.queryParam.pageNum<this.totalPage){
	    		this.queryParam.pageNum++;
	    		this.getHotArticles();
	    	}
		  },
    }
  }
</script>

<style>
</style>
