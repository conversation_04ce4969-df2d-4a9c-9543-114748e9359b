<template>
  <div class="themeInner_content article-contain">
    <div class="left_content">
      <div class="theme_infor">
        <div class="theme">
          <div class="image">
            <img v-if="themeInfo.cover" :src="themeInfo.cover" alt="" :onerror="errorImg"/>
          </div>
          <div class="infor">
            <div class="title">{{themeInfo.name}}</div>
            <div class="detail">
              <span class="mgr-5">收录了{{themeInfo.articleCount}}篇文章</span>·<span class="mgl-5"
                >{{themeInfo.followCount}}人关注</span
              >
            </div>
          </div>
        </div>
        <el-button @click="toWriteArticle" v-if="havePower"
          ><img src="../../assets/image/inner1.png" alt="" /><span
            >投稿</span
          ></el-button
        >
        <el-button  v-if="!havePower" disabled
          ><img src="../../assets/image/inner1.png" alt="" /><span
            >无发布权限</span
          ></el-button
        >
      </div>
      <div class="articel_title">
      	<el-popover
      		class="classify-position"
			    placement="bottom"
			    width="200"
			    trigger="manual"
			    v-model="classifySelectShow">
			    <ul class="classify-item">
			    	<li :style="classify.id==themeId?'color:#3e8cff;font-weight:bold':''" @click="changeClassify({id:themeId,name:'全部'})">全部</li>
			    	<li :style="classify.id==item.id?'color:#3e8cff;font-weight:bold':''" v-for="(item,index) in themeInfo.children" @click="changeClassify(item)">{{item.name}}</li>
			    </ul>

			    <span slot="reference" class="classify-select" @click="classifySelectShow = !classifySelectShow">
	        	{{classify.name}}<i class="mgl-2 el-icon-caret-bottom"></i>
					</span>
			  </el-popover>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="最新收录" name="newest" >
          </el-tab-pane>
          <el-tab-pane label="热门" name="hot">
          </el-tab-pane>
        </el-tabs>
        <newCollectArticles v-if="newCollectLoadFlag" v-show="activeName=='newest'" :themeId="themeId" :classify="classify.id"></newCollectArticles>
      	<hotArticles v-if="hotLoadFlag" v-show="activeName=='hot'" :themeId="themeId" :classify="classify.id"></hotArticles>
      </div>
    </div>
    <div class="right_content">
      <div class="theme_des">
        <div class="title">专题描述</div>
        <div class="content" v-if="themeInfo.desc">
        	<!-- <div id="desc1" v-show="descAllShow" v-html="themeInfo.desc"></div> -->
        	<div id="desc1" v-show="descAllShow" v-dompurify-html="themeInfo.desc"></div>
        	<!-- <div id="desc2" v-show="!descAllShow" v-html="themeInfo.desc" v-clampy="4"></div> -->
        	<div id="desc2" v-show="!descAllShow" v-dompurify-html="themeInfo.desc" v-clampy="4"></div>
		      <div v-show="descAllButtonShow" style="margin-top: 10px;overflow: hidden;">
		      	<a v-if="descAllShow" style="color:#3E8CFF;" class="fr pointer mgr-10" @click="descAllShow=false;">隐藏部分</a>
		      	<a v-else style="color:#3E8CFF;" class="fr pointer mgr-10" @click="descAllShow=true;">展示全文</a>
		      </div>
	      </div>
      </div>
      <div class="creator">
        <div class="title">创建者</div>
        <div class="infor">
          <img
          	v-if="themeInfo.authorHeadImg"
          	@click="toPerCenter(themeInfo.author)"
            :src="themeInfo.authorHeadImg"
            alt=""
            :onerror="errorImg"
          /><span>{{themeInfo.authorName}}</span>
        </div>
      </div>
      <div class="follow" v-if="themeInfo.author==currentUser">
        <div class="title">可投稿人</div>
        <div @click="publishRole"  class="follow_theme mgt-5"><i style="margin-bottom: -1px;" class="fs-14 el-icon-plus mgr-5 fwb"></i><span>配置可投稿人</span></div>
      </div>
      <div class="follow">
        <div class="title">我关注的专题</div>
        <img v-if="followThemeList&&followThemeList.length>0"
          :src="item.cover"
          :title="item.name"
          v-for="(item, index) in followThemeList"
          :key="index"
          @click="toTheme(item.id)"
          :onerror="errorImg"
        />
        <div @click="follow" v-if="!themeInfo.followFlag" class="follow_theme mgt-5"><i style="margin-bottom: -1px;" class="fs-14 el-icon-plus mgr-5 fwb"></i><span>关注该专题</span></div>
      </div>
      <div class="subtheme-manage" v-if="currentUser==themeInfo.author||themeInfo.moderatorFlag">
        <div class="operate" @click="subThemeManageDialog.visible=true;"><img style="height:16px;margin-bottom: -3px;" src="../../assets/image/fenji.png" alt=""/><span>子专题管理</span></div>
      	<el-dialog class="common-dialog" title="子专题管理" :visible.sync="subThemeManageDialog.visible" width="800px">
		    	<div class="pdt-20 fs-13" style="min-height: 150px;">
			  		<div class="subtheme-classify"
			  			v-for="(item,index) in themeInfo.children"><span @click="saveClassify(item.id,item.name)">{{item.name}}</span>
			  			<i class="el-icon-remove subtheme-del-icon" @click="deleteClassify(item.id)"></i>
			  		</div>
			  		<div class="subtheme-classify subtheme-create" @click="saveClassify('','')"><i class="el-icon-plus mgr-5"></i><span>添加专题分级</span></div>
			  	</div>
		    </el-dialog>
        <!--<el-dialog class="common-dialog" title="子专题管理" :visible.sync="subThemeManageDialog.visible" width="800px">
          <div class="pdt-20 fs-13" style="min-height: 150px;">
            <div class="subtheme-classify"
                 v-for="(item,index) in themeInfo.children"><span @click="handleAddSubTheme(item.id,item.name)">{{item.name}}</span>
              <i class="el-icon-remove subtheme-del-icon" @click="handleDelSubTheme(item.id)"></i>
            </div>
            <div class="subtheme-classify subtheme-create" @click="handleAddSubTheme('','')"><i class="el-icon-plus mgr-5"></i><span>添加专题分级</span></div>
          </div>
        </el-dialog>-->
      </div>
    </div>
    <div class="nav" style=" width: 600px;">
      <div class="left_nav">
        <div>专题</div>
        <div class="title">{{themeInfo.name}}</div>
      </div>
      <div class="right_nav fs-13">
      	<span class="pointer" @click="toMyDraft"><img style="margin-bottom: -4px;" src="../../assets/image/draft.png" alt=""/><span class="mgl-5">草稿</span></span>
      	<template v-if="currentUser==themeInfo.author||themeInfo.moderatorFlag">
      		<span class="mgl-10 mgr-10" style="color:#D8D8D8;">|</span>
	      	<span class="pointer" @click="editThemeInfo"><img style="margin-bottom: -5px;" src="../../assets/image/edit.png" alt=""/><span class="mgl-5">修改</span></span>
	      	<span class="mgl-10 mgr-10" style="color:#D8D8D8;">|</span>
	      	<span class="pointer" @click="deleteTheme"><img style="height:16px;margin-bottom: -3px;" src="../../assets/image/delete.png" alt=""/><span class="mgl-5">删除</span></span>
      	</template>
      </div>
    </div>
    <el-dialog title="专题文章发布权限配置" :visible.sync="dialogFormVisible">
      <div>
      	<div style="display: flex;">
      		<div style="flex:1;border:1px solid #E2E2E2;border-left:none">
      			<el-input class="priority-tree-filter" placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
      		  <div style="overflow-y:auto;height:460px;">
      		      <el-tree class="priority-tree-def pdt-5"
      		      	ref="procTree" :data="roleList"
      		      	:props="defaultProps"
      		        :filter-node-method="filterNode"
      		        show-checkbox
      		        @check-change="checkChange"
      		        node-key="id"
      		        :default-checked-keys="defaultCheckVal">
                  <span class="custom-tree-node" slot-scope="{ node, data }" style="flex: 1;display: flex;align-items: center;justify-content: space-between;">
                          <span>{{ data.nick_name }}</span>
                          <span>{{ data.account }}</span>
                  </span>
      		      </el-tree>
      		  </div>
      	  </div>
      	  <div style="flex:1;border:1px solid #E2E2E2;border-left:none">
      			<div class="tree-title">已选角色<span @click="clearTree()" class="fr color-light-black mgr-10 pointer">清空全部<i class="el-icon-delete pdl-5 fs-14" style="color:#FF6262;font-weight:600;"></i></span></div>
      			<ul class="role-list" style="overflow-y:auto;height:460px;">
      				<template v-if="submitNodes.length>0">
      					<li v-for="(obj,index) in submitNodes" style="text-align: left;">
      							<!-- <span :title="obj.account">{{  obj.nick_name  }}({{ obj.account }})</span> -->
      							<span >{{  obj.nick_name  }}({{ obj.account }})</span>
      	            <span class="fr"><i @click="delCheckNodes(obj.id)" class="el-icon-error fs-14 pointer" style="color:#909199;background-color:#F5F6F6;"></i></span>
      					</li>
      				</template>
      				<li class="pd-12 fs-13 tc" style="opacity: 0.5;" v-else>暂无数据</li>
      			</ul>
      		</div>
      	</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitTree">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import newCollectArticles  from './component/newCollectArticles';
import hotArticles  from './component/hotArticles';
import httpCore from "@/api/httpCore";
const api = new httpCore();
import errorPic from "@/assets/image/errorPic.png";
import {EncryptData,DecryptData} from "@/utils/aesUtils";
export default {
  name: "ThemeInner",
  components:{newCollectArticles,hotArticles},
  data() {
    return {
    	themeId:this.$route.query.themeId,
    	themeInfo:{},
    	activeName:'newest',
    	newCollectLoadFlag:true,
    	hotLoadFlag:false,
    	followThemeList:[],
    	currentUser:this.$store.state.loginInfo.userId,
    	classify:{id:this.$route.query.themeId,name:'全部'},
    	classifySelectShow:false,
    	subThemeManageDialog:{
    		visible:false
    	},
    	themeClassify:[],
    	descAllShow:false,
	    descAllButtonShow:false,
	    errorImg: 'this.src="' + errorPic + '"',
      dialogFormVisible: false,
      filterText: '',
    	defaultProps: {
        children: 'children',
        label: 'account',
        id: 'id'
      },
      submitNodes:[],
      defaultCheckVal:[],
      roleList: [],
      havePower: false,
    };
  },
  mounted(){
  	this.getThemeInfo();
  	this.getFollowThemeList();
  },
  watch: {
    filterText(val) {
      this.$refs.procTree.filter(val);
    }
  },
  methods: {
  	//获取专题基本信息
  	getThemeInfo:function(){
	  	api.queryThemeInfo(this.themeId).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.themeInfo = res.data;
          if(this.themeInfo.desc==null){
            this.themeInfo.desc='';
          }
					this.themeInfo.desc = this.themeInfo.desc.replace(/\n/g, '<br/>')
					this.$nextTick(() => {
		    		this.checkDesc();
            this.checkPublishRole();
		    	});
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	checkDesc(){
      if(document.getElementById('desc1')==null||document.getElementById('desc2')==null){
        this.descAllButtonShow = false;
        return;
      }
      var partHtml=document.getElementById('desc1').innerHTML;
  		var allHtml=document.getElementById('desc2').innerHTML;
  		if(allHtml == partHtml){
  			this.descAllButtonShow = false;
  		}else{
  			this.descAllButtonShow = true;
  		}
  	},
  	//tab切换
  	handleClick:function(tab){
			if(tab.name=='newest'){
				this.newCollectLoadFlag=true;
			}else{
				this.hotLoadFlag=true;
			}
  	},
  	//获取我关注的专题列表
  	getFollowThemeList:function(){
  		var data={accountId:this.$store.state.loginInfo.userId};
  		api.getMyFollowThemeList(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.followThemeList = res.data.list;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	//跳转到其他专题
  	toTheme(id){
  		var routeUrl=this.$router.resolve({name:'themeInner',query: { themeId: id }});
      window.open(routeUrl.href, '_blank');
  	},
    //投稿
    toWriteArticle() {
    	if(this.themeInfo.contribution=='1'||this.themeInfo.author==this.currentUser){
    		this.$router.push({ path: "/writeArticle", query: { themeId: this.themeId } });
    	}else{
    		this.$message({message: '您没有投稿权限！' ,type: 'warning'});
    	}
		},
		//跳转草稿
		toMyDraft(){
			this.$router.push({ path: "/myDraft", query: { themeId: this.themeId,themeName:this.themeInfo.name } });
		},
		//专题修改
		editThemeInfo(){
			this.$router.push({ path: "/createTheme", query: { themeId: this.themeId } });
		},
		//专题关注
		follow(){
			var data={
				followId:this.themeId,
				followType:'2'
			};
			api.follow(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.themeInfo.followFlag = true;
					this.getFollowThemeList();
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
		},
		//跳转到个人中心
  	toPerCenter(id) {
      // var routeUrl=this.$router.resolve({name:'perCenter',query: { id: id }});
      localStorage.setItem("forwordUser", id);
      var routeUrl=this.$router.resolve({name:'perCenter'});
      window.open(routeUrl.href, '_blank');
    },
    //切换子专题
    changeClassify(obj){
    	if(this.classify.id!=obj.id){
    		this.activeName=='newest'?this.hotLoadFlag=false:this.newCollectLoadFlag=false;
    		this.classify=obj;
    	}
    	this.classifySelectShow=false;
    },
    //删除专题
    deleteTheme(){
    	this.$confirm('此操作将删除专题下的所有子专题和文章, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.deleteTheme(this.themeId).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$message({message: res.msg ,type: 'success'});
						this.$router.push({ path: "/theme"});
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
      });
    },
    //---------子专题操作 start-----------
    saveClassify:function(id,defaultValue){
			this.$prompt('请输入分级名称', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue:defaultValue
      }).then(({ value }) => {
      	if(value){
      		var data = {
      			parentId:this.themeId,
      			name:value,
      			cover:'?= ',
      			contribution:'1',
      			approval:'1',
      			sequence:this.themeInfo.children.length
      		};
      		api.saveTheme(id,data).then(response => {
        		var res = response.data;
						if(res.status == "0"){
							this.$message({'message': res.msg,'type': 'success'});
							this.getThemeInfo();
						}else {
							this.$message({'message': res.msg ,'type': 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
      	}else{
      		this.$message({'message': '分级名称不能为空' ,'type': 'warning'});
      	}
      });
		},
		deleteClassify(id){
    	this.$confirm('此操作将删除子专题下的所有文章, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.deleteTheme(id).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$message({message: res.msg ,type: 'success'});
						if(id==this.classify.id){
							this.changeClassify({id:this.themeId,name:'全部'});
						}
						this.getThemeInfo();
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
      });
    },
		//---------子专题操作 end-----------
    //---------专题发布文章权限 start-----------
    checkPublishRole(){
      if(this.themeInfo.author==this.currentUser){
        this.havePower=true;
        return;
      }
      api.publishCheck(this.themeId).then(response => {
      	var res = response.data;
      	if(res.status == "0"){
      		this.havePower=res.data;
      	}else {
      		this.$message({message: res.msg ,type: 'error'});
      	}
      })
      .catch(err => {
      	console.log(err);
      });
    },
    publishRole(){
			api.getSysUser(this.themeId).then(response => {
    		var res = response.data;
				if(res.status == "0"){
          this.roleList = res.data.userList;
          var checklist=res.data.publishList.map(function(value){
            return value.account_id;
          });
          this.defaultCheckVal=checklist;
          // this.submitNodes=res.data;
          // this.submitNodes=this.$refs.procTree.getCheckedNodes();
          this.submitNodes=res.data.userList.filter(item => checklist.indexOf(item.id)>-1);
          this.dialogFormVisible = true;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
		},
    filterNode: function(value, data) {
      if (!value) return true;
      return data.account.indexOf(value) !== -1 || data.nick_name.indexOf(value) !== -1;
    },
    checkChange:function(data,checked,indeterminate){
    	this.submitNodes=this.$refs.procTree.getCheckedNodes();
    },
    //删除按钮
    delCheckNodes:function(id,name){
    	this.submitNodes=this.submitNodes.filter(item => item.id != id);
    	this.setUnchecked(id);
    },
    //把某个节点设成未选中的状态
    setUnchecked:function(id){
    	this.$refs.procTree.setChecked(id,false,false);
    },
    //清空按钮
    clearTree:function(){
    	this.submitNodes=[];
    	this.$refs.procTree.setCheckedKeys([]);
    },
    submitTree:function(){
    	var ids=[];
      this.submitNodes.forEach(item =>{
        ids.push(item.id);
    	});
			var data={
        userIdlist: ids,
        themeId: this.themeId
			};
    	api.publishConfig(data).then(response => {
    		var res = response.data;
    		if(res.status == "0"){
    			this.$message({message: "配置成功" ,type: 'success'});
          this.dialogFormVisible = false;
    		}else {
    			this.$message({message: res.msg ,type: 'error'});
    		}
    	})
    	.catch(err => {
    		console.log(err);
    	});
    },
    //---------专题发布文章权限 end-----------
    handleAddSubTheme(id, value){
      var data = {
        id:id,
        parentId:this.themeId,
        name:value,
        cover:'?= ',
        contribution:'1',
        approval:'1',
        sequence:this.themeInfo.children.length
      };
      api.addSubTheme(data).then(response => {
    		var res = response.data;
				if(res.status == "0"){
          this.$message({message: res.msg, type:'success'});
					this.getThemeInfo();
				}else {
          this.$message({message: res.msg, type: 'error'});
        }
      })
    },
    handleDelSubTheme(id){
      this.$confirm('此操作将删除子专题, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.deleteSubTheme(id).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
            this.$message({message: res.msg, type:'success'});
						this.getThemeInfo();
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				}).catch(err => {
					console.log(err);
				});
      });
    }
  }
};
</script>
<style lang="scss">
	.subtheme-classify{
		height: 30px;
		background: #E6F0FF;
		border-radius: 2px;
		font-weight: bold;
		color: #408CFE;
		font-size:12px;
		display: inline-block;
		margin-right:24px;
		padding:0px 13px;
		line-height: 30px;
		margin-bottom:20px;
		position:relative;
		cursor:pointer;
	}
	.subtheme-del-icon{
		position:absolute;
		right:-8px;
		top:-4px;
		color:#FF6262;
		width:15px;
		height:15px;
	}
	.subtheme-create{
		background: #E6E6E6;
		font-weight: 400;
		color: #909199;
	}
  .priority-tree-filter input{
    border:none;
    background-color:transparent;
    border-bottom: 1px solid #E2E2E2;
    border-radius: 0;
    font-size:13px;
  }
  .priority-tree-def{
  	background: transparent;
    .el-tree-node{
      font-size:13px;
      .el-tree-node__content:hover{
        color:#409EFF;
      }
      .el-tree-node__label{
      	font-size:13px;
      }
    }
    .el-tree__empty-text{
    	font-size:13px;
    }
  }
  .tree-checked-item{
    color:#409EFF;
    font-weight: 600;
  }
  .tree-title{
  	height:40px;
  	line-height: 40px;
  	background: #F5F6F6;
  	border-bottom: 1px solid #E9E9E9;
  	padding-left: 20px;
  }
  .role-list{
  	padding: 10px 20px;
  	li{
  		height:34px;
  		line-height:34px;
  	}
  }
</style>
