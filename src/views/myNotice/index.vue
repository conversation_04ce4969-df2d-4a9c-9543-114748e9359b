<template>
	<div class="home_content article-contain">
		<div style="width:100%;" class="notice-header">
			<div class="tl mgt-40 mgb-20 pdb-10" style="border-bottom: 1px solid #ddd;">
				<span class="fs-16 fwb color-9">通知中心</span>
				<el-dropdown class="classify" trigger="click" @command="handleCommand">
				  <span class="el-dropdown-link">
				    	{{currentType}}<i class="el-icon-caret-bottom el-icon--right"></i>
				  </span>
				  <el-dropdown-menu slot="dropdown">
				  	<el-dropdown-item v-for="(type,index) in typeList" :key="index" :command="type">{{type.name}}</el-dropdown-item>
				  </el-dropdown-menu>
				</el-dropdown>
				<el-button class="fr fs-12" style="margin-top: -4px;" type="text" v-if="currentType=='培训提醒'">新增培训申请</el-button>&nbsp;&nbsp;
				<el-button class="fr fs-12" style="margin-top: -4px;" type="text" :disabled="!isChecked" @click="batchMarkRead">批量标记已读</el-button>
			</div>
			<div class="notice-list">
				<div class="notice-item" v-for="(item,index) in noticeList">
					<div class="left">
						<span class="mark" v-if="item.noticeStatus=='1'">已读</span>
						<span class="mark" style="background: transparent;" v-else><el-checkbox v-model="item.isChecked"></el-checkbox></span>
						<span style="color:#C0C4CC">{{item.noticeTime}}</span>
						<span v-if="item.noticeType=='3'">【新文章提醒】</span>
						<span v-else-if="item.noticeType=='2'">【培训提醒】</span>
						<span v-else-if="item.noticeType=='1'">【@提醒】</span>
					</div>
					<div class="right" title="标记已读" v-show="item.noticeStatus=='0'"><img style="width:20px" @click="markRead(item.id,index)" src="../../assets/image/yidu.png"/></div>
					<div class="content">
						<template v-if="item.noticeType=='3'">
	      			{{item.noticeContent.authorName}}在专题
	      			<span class="href-addr" @click="toThemePage(item.noticeContent.parentSubjectId)">{{item.noticeContent.parentSubjectName}}<span v-if="item.noticeContent.subjectName">/{{item.noticeContent.subjectName}}</span></span>
	      			下发表了新文章<span class="href-addr" @click="toArticlePage(item.noticeContent.articleId)">《{{item.noticeContent.articleTitle}}》</span>，请查阅
	      		</template>
	      		<template v-else-if="item.noticeType=='2'">
	      			运维知识库<span class="href-addr" @click="queryTraining(item.noticeContent)">《{{item.noticeContent.title}}》</span>有{{item.noticeContent.userCount}}人希望组织相关培训，请查阅
	      		</template>
	      		<template v-if="item.noticeType=='1'">
	      			{{item.noticeContent.nick_name}}提醒你查看文章
	      			<span class="href-addr" @click="toArticlePage(item.noticeContent.articleId)">《{{item.noticeContent.articleTitle}}》</span>，
	      			提醒内容：{{item.noticeContent.content}}
	      		</template>
					</div>
				</div>
				<div v-show="totalPage>1&&queryParam.pageNum!=totalPage" class="mgt-10 fs-13" style="opacity: 0.5;">
	      	<span class="pointer" @click="loadMore">
			      <span>查看更多</span>
		      </span>
		    </div>
				<div class="mgt-10" style="height:80px" v-loading ="noticeLoading" element-loading-text = "数据正在加载中"></div>
	      <div class="empty" v-if="!noticeLoading&&noticeList.length==0">
	      	<img src="../../assets/image/notice2.png" alt="" />
	      	<div class="mgt-5">暂时没有收到通知</div>
	      </div>
			</div>
			<el-dialog class="common-dialog" title="申请培训话题" :visible.sync="knowledgeTrainDialog.visible" width="650px" :append-to-body="true">
	    	<div>
	    		<knowledgeComponent :id="knowledgeTrainDialog.id"></knowledgeComponent>
		  	</div>
	    </el-dialog>
		</div>
  </div>
</template>

<script>
	import httpCore from "@/api/httpCore";
	import knowledgeComponent from '../knowledge/knowledge';
	const api = new httpCore();
	export default {
    data(){
      return{
      	queryParam:{
      		pageNum:1,
      		pageSize:8,
      		noticeType:null,
      		noticeStatus:null
      	},
      	noticeLoading:true,
      	noticeList:[],
      	totalPage:0,
      	currentType:'全部分类',
      	typeList:[{type:null,name:'全部分类'},{type:3,name:'新文章提醒'},{type:2,name:'培训提醒'},{type:1,name:'@提醒'}],
      	knowledgeTrainDialog:{
	    		visible:false,
	    		id:''
	    	}
      }
    },
    components:{knowledgeComponent},
    mounted(){
    	this.queryNotice();
    },
    computed:{
		  isChecked: function () {
	      return this.noticeList.some(function(val){
	 				return val.isChecked;
	 			});
	    }
	  },
    methods:{
    	queryNotice:function(){
	  		this.noticeLoading=true;
	  		api.queryNotice(this.queryParam).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.noticeLoading=false;
						var noticeList = res.data.list;
						noticeList.forEach(function(item){
							item.isChecked=false;
							var obj = JSON.parse(item.noticeContent);
							item.noticeContent = obj;
			  		});
						this.noticeList = this.noticeList.concat(noticeList);
						this.totalPage = res.data.pages;
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
	  	markRead:function(id,index){
	  		this.$confirm('确认标记已读?', '提示', {
	        confirmButtonText: '确定',
	        cancelButtonText: '取消',
	        type: 'warning'
	      }).then(() => {
	        api.noticeMarkRead(id).then(response => {
		    		var res = response.data;
						if(res.status == "0"){
							this.noticeList[index].noticeStatus="1";
							this.getNoticeCount();
						}else {
							this.$message({message: res.msg ,type: 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
	      });
	  	},
	  	loadMore:function(){
	  		if(this.queryParam.pageNum<this.totalPage){
	    		this.queryParam.pageNum++;
	    		this.queryNotice();
	    	}
		  },
		  handleCommand(command) {
		  	if(this.queryParam.noticeType!=command.type){
		  		this.currentType = command.name;
			  	this.queryParam.noticeType=command.type;
			  	this.queryParam.pageNum=1;
			  	this.noticeList=[];
			  	this.queryNotice();
		  	}
		  },
		  batchMarkRead:function(){
	  		this.$confirm('确认全部标记已读?', '提示', {
	        confirmButtonText: '确定',
	        cancelButtonText: '取消',
	        type: 'warning'
	      }).then(() => {
	      	var ids = "";
		  		this.noticeList.forEach(function(val){
		 				if(val.isChecked){
		 					ids+=(ids==""?val.id:","+val.id);
		 				}
		 			});
	        api.noticeMarkRead(ids).then(response => {
		    		var res = response.data;
						if(res.status == "0"){
							this.queryParam.pageNum=1;
					  	this.noticeList=[];
					  	this.queryNotice();
					  	this.getNoticeCount();
						}else {
							this.$message({message: res.msg ,type: 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
	      });
	  	},
	  	toThemePage(id){
		  	this.$router.push({ path: "/themeInner" , query: { themeId: id }});
		  },
		  toArticlePage(id){
		  	this.$router.push({ path: "/articleDetail" , query: { id: id }});
		  },
		  queryTraining(obj){
		  	if(obj.type=='1'){
		  		this.knowledgeTrainDialog.id = obj.trainingId;
		  		this.knowledgeTrainDialog.visible = true;
		  	}else if(obj.type=='2'){
		  		this.$router.push({ path: "/articleDetail" , query: { id: obj.trainingId }});
          // this.handleArticleRead(obj);
		  	}
		  },
		  getNoticeCount:function(){
	  		api.queryNoticeCount().then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.noticeCount = res.data;
						this.$store.commit('setNoticeCount', this.noticeCount['all']);
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
	  	},
      //置为已读
      handleArticleRead(obj){
        api.setArticleRead(obj.trainingId).then(response => {
          var res = response.data;
          if(res.status == "0"){
            this.getNoticeCount();
          }else {
            this.$message({message: res.msg ,type: 'error'});
          }
        })
      }
    }
  }
</script>

<style lang="scss">
	.notice-list{
		.notice-item{
			text-align:left;
			font-size:13px;
			color:#606166;
			padding: 10px 0;
    	border-bottom: 1px solid #eee;
			.left{
				vertical-align: top;
				display:inline-block;
				width: calc(100% - 50px);
				margin-bottom: 10px;
				.mark{
					background: #E6E6E6;
			    color: white;
			    border-radius: 4px;
			    display: inline-block;
			    width: 40px;
			    text-align: center;
			    font-size: 10px;
			    margin-right: 5px;
				}
			}
			.content{
				vertical-align: top;
				line-height: 24px;
			}
			.right{
				display:inline-block;
				vertical-align: top;
				color:#3E8CFF;
				width:40px;
			}
		}
		.empty{
      color:#C0C4CC;
      font-size: 13px;
      margin-top: 20px;
    }
	}
	.notice-header{
		.classify{
			font-size:13px;
			margin-left:20px;
		}
	}
</style>
