<template>
  <div id="video"></div>
</template>
<script>
  export default {
  	props:['id','url'],
    data() {
      return {
        loading:true,
        player:null,
        options: {
          container: '#video', //初始化容器
          variable: 'player', // 设置变量
          loop: false, //循环
          drag: 'start',
          video:null //视频地址
        }
       }
    },
    mounted(){
      this.initData();
    },
    methods: {
      initData() {
        this.options.video=this.url;
        this.player=new ckplayer(this.options);
      }
    }
  }
</script>
