<template>
  <uploader :options="options"
  		@file-added="onFileAdded"
      @file-success="onFileSuccess"
      @file-progress="onFileProgress"
      file-error="onFileError"
      class="inline-block">
    <uploader-btn v-if="type=='1'" class="upload-chunk"><img class="vm pointer" src="../../assets/image/attach.png" alt=""/></uploader-btn>
    <uploader-btn v-else class="upload-chunk">上传附件</uploader-btn>
  </uploader>
</template>
<script>
import httpCore from "@/api/httpCore"
const api = new httpCore();
import SparkMD5 from 'spark-md5';
export default {
	props:['type','tipsId'],
  data() {
     return {
/*			支持的配置项：
			target 目标上传 URL，可以是字符串也可以是函数，如果是函数的话，则会传入 Uploader.File 实例、当前块 Uploader.Chunk 以及是否是测试模式，默认值为 '/'。
			singleFile 单文件上传。覆盖式，如果选择了多个会把之前的取消掉。默认 false。
			chunkSize 分块时按照该值来分。最后一个上传块的大小是可能是大于等于1倍的这个值但是小于两倍的这个值大小，可见这个 Issue #51，默认 1*1024*1024。
			forceChunkSize 是否强制所有的块都是小于等于 chunkSize 的值。默认是 false。
			simultaneousUploads 并发上传数，默认 3。
			fileParameterName 上传文件时文件的参数名，默认 file。
			query 其他额外的参数，这个可以是一个对象或者是一个函数，如果是函数的话，则会传入 Uploader.File 实例、当前块 Uploader.Chunk 以及是否是测试模式，默认为 {}。
			headers 额外的一些请求头，如果是函数的话，则会传入 Uploader.File 实例、当前块 Uploader.Chunk 以及是否是测试模式，默认 {}。
			withCredentials 标准的 CORS 请求是不会带上 cookie 的，如果想要带的话需要设置 withCredentials 为 true，默认 false。
			method 当上传的时候所使用的是方式，可选 multipart、octet，默认 multipart，参考 multipart vs octet。
			testMethod 测试的时候使用的 HTTP 方法，可以是字符串或者函数，如果是函数的话，则会传入 Uploader.File 实例、当前块 Uploader.Chunk，默认 GET。
			uploadMethod 真正上传的时候使用的 HTTP 方法，可以是字符串或者函数，如果是函数的话，则会传入 Uploader.File 实例、当前块 Uploader.Chunk，默认 POST。
			allowDuplicateUploads  如果说一个文件以及上传过了是否还允许再次上传。默认的话如果已经上传了，除非你移除了否则是不会再次重新上传的，所以也就是默认值为 false。
			prioritizeFirstAndLastChunk 对于文件而言是否高优先级发送第一个和最后一个块。一般用来发送到服务端，然后判断是否是合法文件；例如图片或者视频的 meta 数据一般放在文件第一部分，这样可以根据第一个块就能知道是否支持；默认 false。
			testChunks 是否测试每个块是否在服务端已经上传了，主要用来实现秒传、跨浏览器上传等，默认 true。
			preprocess 可选的函数，每个块在测试以及上传前会被调用，参数就是当前上传块实例 Uploader.Chunk，注意在这个函数中你需要调用当前上传块实例的 preprocessFinished 方法，默认 null。
			initFileFn 可选函数用于初始化文件对象，传入的参数就是 Uploader.File 实例。
			readFileFn 可选的函数用于原始文件的读取操作，传入的参数就是 Uploader.File 实例、文件类型、开始字节位置 startByte，结束字节位置 endByte、以及当前块 Uploader.Chunk 实例。并且当完成后应该调用当前块实例的readFinished 方法，且带参数-已读取的 bytes。
			checkChunkUploadedByResponse 可选的函数用于根据 XHR 响应内容检测每个块是否上传成功了，传入的参数是：Uploader.Chunk 实例以及请求响应信息。这样就没必要上传（测试）所有的块了，具体细节原因参考 Issue #1，使用示例.
			generateUniqueIdentifier 可覆盖默认的生成文件唯一标示的函数，默认 null。
			maxChunkRetries 最大自动失败重试上传次数，值可以是任意正整数，如果是 undefined 则代表无限次，默认 0。
			chunkRetryInterval 重试间隔，值可以是任意正整数，如果是 null 则代表立即重试，默认 null。
			progressCallbacksInterval 进度回调间隔，默认是 500。
			speedSmoothingFactor 主要用于计算平均速度，值就是从 0 到 1，如果是 1 那么上传的平均速度就等于当前上传速度，如果说长时间上传的话，建议设置为 0.02，这样剩余时间预估会更精确，这个参数是需要和 progressCallbacksInterval 一起调整的，默认是 0.1。
			successStatuses 认为响应式成功的响应码，默认 [200, 201, 202]。
			permanentErrors 认为是出错的响应码，默认 [404, 415, 500, 501]。
			initialPaused 初始文件 paused 状态，默认 false。
			processResponse 处理请求结果，默认 function (response, cb) { cb(null, response) }。 0.5.2版本后，processResponse 会传入更多参数：(response, cb, Uploader.File, Uploader.Chunk)。
			processParams 处理请求参数，默认 function (params) {return params}，一般用于修改参数名字或者删除参数。0.5.2版本后，processParams 会有更多参数：(params, Uploader.File, Uploader.Chunk, isTest)。*/
      options: {
        maxFileSize:1024*1024*1024,
        target:'/file-upload-service/v1/pass_chunk/chunk',
        query:{application:'lezhi-service'},
        chunkSize: 30 * 1024 * 1024,
        forceChunkSize:true,
        simultaneousUploads:1,
        maxChunkRetries: 1,
        checkChunkUploadedByResponse: function (chunk, message) {
          let objMessage = JSON.parse(message);
          return objMessage.isExit;
        }
      },
      downloadPath:'/file-upload-service/v1/paas_attachments_tool/download?id=',
      typeList: ['audio/aac', //.aac
      'video/x-msvideo',//.avi
      'image/bmp',//.bmp
      'application/x-bzip',//.bz
      'text/css',//.css
      'text/csv',//.csv
      'application/msword',//.doc
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',//.docx
      'application/epub+zip',//.epub
      'image/gif',//.gif
      'text/html',//.htm .html
      'image/vnd.microsoft.icon',//.ico
      'application/java-archive',//.jar
      'image/jpeg',//.jpeg .jpg
      'text/javascript',//.js
      'application/json',//.json
      'udio/mpeg',//.mp3
      'video/mpeg',//.mpeg
      'image/png',//.png
      'application/pdf',//.pdf
      'application/vnd.ms-powerpoint',//.ppt
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',//.pptx
      'application/x-rar-compressed',//.rar
      'application/rtf',//.rtf
      'application/x-sh',//.sh
      'image/svg+xml',//.svg
      'application/x-shockwave-flash',//.swf
      'application/x-tar',//.tar
      'text/plain',//.txt
      'audio/wav',//.wav
      'audio/webm',//.weba
      'video/webm',//.webm
      'image/webp',//.webp
      'application/xhtml+xml',//.xhtml
      'application/vnd.ms-excel',//.xls
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',//.xlsx
      'application/xml',//.xml
      'text/xml',//.xml
      'application/zip',//.zip
      'application/x-zip-compressed',//.zip
      'application/x-7z-compressed',//.7z
      ],
      extList:[
        '.aac',
        '.avi',
        '.bmp',
        '.bz',
        '.css',
        '.csv',
        '.doc',
        '.docx',
        '.epub',
        '.gif',
        '.htm',
        '.html',
        '.ico',
        '.jar',
        '.jpeg',
        '.jpg',
        '.js',
        '.json',
        '.mp3',
        '.mpeg',
        '.png',
        '.pdf',
        '.ppt',
        '.pptx',
        '.rar',
        '.rtf',
        '.sh',
        '.svg',
        '.swf',
        '.tar',
        '.txt',
        '.wav',
        '.weba',
        '.webm',
        '.webp',
        '.xhtml',
        '.xls',
        '.xlsx',
        '.xml',
        '.zip',
        '.7z',
      ]
    }
  },
  methods: {
    onFileAdded(file) {
      if(file.size>this.options.maxFileSize){
        file.cancel();
        this.$message({message: '附件大小不能超过1GB' ,type: 'warning'});
      }else if(this.typeList.indexOf(file.fileType)<0 && this.extList.indexOf(file.fileType)<0){
        file.ignored = true;
        file.cancel();
        // file.chunks=[];
        this.$message({message: '附件格式不允许上传' ,type: 'warning'});
        return false;
      }else{
        this.computeMD5(file);
      }
    },
    computeMD5(file) {
      let fileReader = new FileReader();
      let time = new Date().getTime();
      const chunkSize = 30 * 1024 * 1024;
      let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
      let currentChunk = 0;
      let chunks = Math.ceil(file.size / chunkSize);//计算可分片数
      let spark = new SparkMD5.ArrayBuffer();
      file.pause();
      loadNext();
      fileReader.onload = (e => {
        spark.append(e.target.result);
        if (currentChunk < chunks) {
          currentChunk++;
          loadNext();
        }else{
          let md5 = spark.end();
          this.computeMD5Success(md5, file);
        }
      });
      fileReader.onerror=function (){
        file.cancel();
      };
      function loadNext() {
        let start = currentChunk * chunkSize;
        let end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize;
        fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end));
      }
    },
    computeMD5Success(md5, file) {
      file.uniqueIdentifier = md5;
      file.resume();
    },
    onFileProgress(rootFile, file, chunk) {
    	var tmp = file.progress()*100;
    	var progressPercent = tmp.toFixed(0)+"%";
    	this.$emit('changeAttachProgress',{uniqueIdentifier:file.uniqueIdentifier,uploadProgress:progressPercent,completed:file.completed});
    },
    onFileError(rootFile, file, resp, chunk) {
      this.$message({message: "上传失败" ,type: 'error'});
    },
    onFileSuccess(rootFile, file, resp, chunk) {
      let resObj= JSON.parse(resp);
      if(resObj.complete){
      	var att = {
	  			id: resObj.id,
	  			name: resObj.fileName,
	  			path: this.downloadPath+resObj.id,
	  			tipsId:this.tipsId,
	  			type:"5",
	  			size:resObj.size
	  		};
	  		api.addAttach(att).then(response => {
	    		var res = response.data;
					if(res.status == "0"){
						this.$emit('handleAttachSuccess',att);
					}else {
						this.$message({message: res.msg ,type: 'error'});
					}
				})
				.catch(err => {
					console.log(err);
				});
      }
    }
  }
}
</script>

<style lang="scss">
  .upload-chunk{
    border:none !important;
    padding:0 !important;
    background: transparent !important;
  }
  .upload-chunk:hover{
  	background: transparent !important;
  }
</style>
