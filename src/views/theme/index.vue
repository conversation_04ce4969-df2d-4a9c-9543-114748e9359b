<template>
  <div class="theme_home">
    <div class="theme_title">
      <div>
        <span class="tip1">//</span>
        <span class="title">乐享知识</span>
        <span class="title">快乐工作</span>
        <span class="tip1">//</span>
      </div>
    </div>
    <span>
    	<span :class="queryParam.showType=='1'?'active':''" @click="changeShowType('1')">全部专题</span>
    	&nbsp;/&nbsp;
    	<span :class="queryParam.showType=='2'?'active':''" @click="changeShowType('2')">我关注的专题</span>
    </span>
    <el-button v-if="havePower" @click="createTheme">创建专题</el-button>
    <div v-loading ="loading" element-loading-text = "数据正在加载中">
	    <div class="theme_content" v-if="themeList.length>0">
	      <div class="theme_box" v-for="(item, index) in themeList" :key="index">
	        <div class="image">
	          <div>
	            <img v-if="item.cover" :src="item.cover" alt="" :onerror="errorImg"/>
	          </div>
	        </div>
	        <div class="title" :title="item.name">{{ item.name }}</div>
	        <div class="des" v-clampy="2">{{ item.desc }}</div>
	        <div class="num">
	          <span>{{ item.followCount }}人关注</span>|<span
	            >{{ item.articleCount }}篇文章</span
	          >
	        </div>
	        <el-button @click="toTheme(item.id)">进入专题</el-button>
	      	<div v-if="item.moderatorFlag" class="admin">版主</div>
	      </div>
	      <div class="referesh" v-show="totalPage>1">
	      	<el-pagination
	      		@current-change="changePage"
            :current-page="queryParam.pageNum"
            :page-size="queryParam.pageSize"
				    layout="prev, pager, next"
				    :total="totalCount">
				  </el-pagination>
		    </div>
	    </div>
    	<div v-show="!loading" class="fs-18" style="margin-top: -80px;opacity: 0.2;" v-else>尚未创建过专题</div>
  	</div>
  </div>
</template>
<script>
import httpCore from "@/api/httpCore";
const api = new httpCore();
import errorPic from "@/assets/image/errorPic.png";
export default {
  name: "theme",
  data() {
    return {
      themeList:[],
      queryParam:{
      	pageNum:1,
      	pageSize:8,
      	showType:'1'
      },
      totalPage:0,
      totalCount:0,
      loading:true,
      currentRoles:this.$store.state.loginInfo.roles,
      havePower:false,
      errorImg: 'this.src="' + errorPic + '"',
    };
  },
  mounted(){
  	this.checkRole();
  	this.getThemeList();
  },
  methods:{
  	checkRole:function(){
  		api.checkThemeCreatePower().then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.havePower=res.data;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	getThemeList:function(){
  		this.loading=true;
  		api.queryTheme(this.queryParam).then(response => {
    		var res = response.data;
    		this.loading=false;
				if(res.status == "0"){
					this.themeList = res.data.list;
					this.totalPage = res.data.pages;
					this.totalCount = res.data.total;
				}else {
					this.$message({message: res.msg ,type: 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	toTheme(id) {
      this.$router.push({ path: "/themeInner" , query: { themeId: id }});
    },
    createTheme() {
      this.$router.push("/createTheme");
    },
    changePage:function(val){
    	this.queryParam.pageNum = val;
    	this.themeList=[];
    	this.getThemeList();
    },
    changeShowType:function(curType){
    	if(this.queryParam.showType!=curType){
    		this.queryParam.showType = curType;
    		this.queryParam.pageNum=1;
    		this.themeList=[];
    		this.getThemeList();
    	}
    }
  },
};
</script>
