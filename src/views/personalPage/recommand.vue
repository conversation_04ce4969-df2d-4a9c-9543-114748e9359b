<template>
<div>
  <div>
    <el-input
      placeholder="请输入内容"
      v-model="nameinfo"
      clearable>
    </el-input>
    <el-switch
      v-model="isLike"
      active-text="模糊查询"
      inactive-text="精确查询">
    </el-switch>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>待推荐小知识</span>
        <el-button style="float: right; padding: 3px 0" type="text" onclick="getInterTips">获取待推荐小知识</el-button>
      </div>
      <div v-for="o in 4" :key="o" class="text item">
        {{'列表内容 ' + o }}
      </div>
    </el-card>
    <el-table
      :data="tableList"
      style="width: 100%">
      <el-table-column
        prop="id"
        label="详情编码"
        width="180">
      </el-table-column>
      <el-table-column
        prop="info"
        label="详情内容"
        width="180">
      </el-table-column>
      <el-table-column
        prop="desc"
        label="小知识简介">
      </el-table-column>
      <el-table-column
        prop="number"
        label="推荐人数">
      </el-table-column>
      <el-table-column
        prop="reason"
        label="推荐原因">
      </el-table-column>
      <el-table-column
        prop="point"
        label="综合推荐指数">
      </el-table-column>
    </el-table>
  </div>
  <div>
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="推荐信息">
        <el-input v-model="form.info"></el-input>
      </el-form-item>
      <el-form-item label="推荐备注">
        <el-input v-model="form.remark"></el-input>
      </el-form-item>
      <el-form-item label="推荐人员">
        <el-input v-model="form.members"></el-input>
      </el-form-item>
      <el-form-item label="推荐原因">
        <el-input v-model="form.reason"></el-input>
      </el-form-item>
    </el-form>
    <el-form-item>
      <el-button type="primary" @click="saveInterAdvice">立即创建</el-button>
      <el-button>取消</el-button>
    </el-form-item>
    <div>推荐详情编码，小知识id，推荐信息，推荐原因，评论人员，评论时间，综合推荐指数
      <el-table
        :data="tableInfo"
        style="width: 100%">
        <el-table-column
          prop="date"
          label="推荐详情编码"
          width="180">
        </el-table-column>
        <el-table-column
          prop="name"
          label="小知识id"
          width="180">
        </el-table-column>
        <el-table-column
          prop="address"
          label="推荐信息">
        </el-table-column>
        <el-table-column
          prop="address"
          label="推荐原因">
        </el-table-column>
        <el-table-column
          prop="address"
          label="评论人员">
        </el-table-column>
        <el-table-column
          prop="address"
          label="评论时间">
        </el-table-column>
        <el-table-column
          prop="address"
          label="综合推荐指数">
        </el-table-column>
      </el-table>
    </div>
  </div>
  <div>
    <el-input
      placeholder="请输入内容"
      v-model="id"
      clearable>
    </el-input>
    <el-input
      placeholder="请输入内容"
      v-model="name"
      clearable>
    </el-input>
    <el-input
      type="textarea"
      :rows="2"
      placeholder="删除意见"
      v-model="deleteInfo">
    </el-input>
    <el-button type="info" onclick="deleteInterAdvice">删除推荐意见</el-button>
  </div>
  <div>
    <el-form ref="form" :model="updateInfo" label-width="80px">
      <el-form-item label="推荐信息">
        <el-input v-model="updateInfo.tuijian"></el-input>
      </el-form-item>
      <el-form-item label="推荐人">
        <el-input v-model="updateInfo.number"></el-input>
      </el-form-item>
      <el-form-item label="推荐原因">
        <el-input type="textarea" v-model="updateInfo.because"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="updateAdviceInfo">修改推荐意见信息</el-button>
        <el-button>取消</el-button>
      </el-form-item>
    </el-form>

    <el-button type="primary" @click="judgeUpdateAdvice">校验修改信息</el-button>
    <el-button type="primary" @click="findAllUpdateAdvice">查询修改信息列表</el-button>
  </div>

</div>
</template>

<script>
import httpNew from "@/api/httpNew";
const api = new httpNew();
export default {

  data() {
    return {
      nameinfo: '',
      isLike:'',
      deleteInfo:'',
      id:'',
      form:'',
      name:'',
      tableList:[],
      tableInfo:[],
      auth:false,
      updateInfo:{
        interInfo:'',
        interNumbers:'',
        because:'',
      },
      updateAuth:false
    }
  },
  methods: {
    saveLiTips() {
      var info = {};
      info.name = this.nameinfo;
      info.id = this.id;
      info.isLike = this.isLike;
      api.saveLiTips(info).then(response => {
        if(response.status == "0"){
          this.$message(response.data);
        }
      })
    },
    getInterTips(){
      api.getInterTips(this.id).then(response => {
        if(response.status == "0"){
          this.$message(response.data);
          this.showInterTips(response.data)
        }
      })
    },
    showInterTips(ids){
      if(null == ids || undefined == ids){
        ids = '1'
      }
      api.showInterTips(ids).then(response => {
        if(response.status == "0"){
          this.tableList = response.data;
        }
      })
    },
    saveInterAdvice(){
      var info = {};
      info.tuijianren = this.form.members
      info.tuijianbeizhu = this.form.remark
      info.tuijiantyijian = this.form.reason
      api.saveInterAdvice(info).then(response => {
        if(response.status == "0"){
          this.$message('保存推荐意见成功');
          this.showInterAdvice()
        }
      })
    },
    showInterAdvice(){
      api.showInterAdvice().then(response => {
        if(response.status == "0"){
          let info = response.data
          info.forEach(val => {
            val.time = new Date(val.time);
          })
          this.tableInfo = response.data;
        }
      })
    },
    deleteInterAdvice(){
      var info = {};
      info.id = this.id
      info.name = this.name
      info.tuijiantyijian = this.form.reason
      this.judgeInterAdvice()
      api.deleteInterAdvice(info).then(response => {
        if(response.status == "0"){
          this.$message('删除推荐意见成功');
        }
      })
    },
    judgeInterAdvice(){
      api.judgeInterAdvice().then(response => {
        if(response.status == "0"){
          this.$message('权限校验成功');
          this.auth = true
        }else {
          this.$message('权限校验失败');
          this.auth = false
        }
      })
    },
    saveDeleteAdvice(){
      var data = {};
      data.id = this.id
      data.time = new Date()
      data.info = this.deleteInfo
      api.saveDeleteAdvice(data).then(response => {
        if(response.status == "0"){
          this.checkInterAdvice()
        }
      })
    },
    checkInterAdvice(){
      var data = {};
      data.id = this.id
      api.checkInterAdvice(data).then(response => {
        if(response.status == "0"){
          this.$message("评论删除成功");
        }else {
          this.$message(response.data);
        }
      })
    },
    updateAdviceInfo(){
      var data = {};
      data.id = this.id
      data.tuijian = this.updateInfo.interInfo
      data.because = this.updateInfo.because
      data.number = this.updateInfo.interNumbers
      api.updateAdviceInfo(data).then(response => {
        if(response.status == "0") {
          this.$message("评论修改成功");
          this.judgeUpdateAdvice()
          this.serachUpdateAdvice()
        }
      })
    },
    judgeUpdateAdvice(){
      api.judgeUpdateAdvice(this.user.id).then(response => {
        if(response.status == "0") {
          this.updateAuth = true;
          serachUpdateAdvice()
        }else {
          this.updateAuth = false;
        }
      })
    },

    serachUpdateAdvice(){
      api.serachUpdateAdvice(this.data.id).then(response => {
        if(response.status == "0"){
          this.$message(response.data);
        }else {
          this.$message(response.msg);
        }
      })
    },
    findAllUpdateAdvice(){
      api.findAllUpdateAdvice().then(response => {
        if(response.status == "0"){
          if(response.data.size != 0){
            this.tableList(response.data);
          }
        }else {
          this.$message(response.msg);
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
