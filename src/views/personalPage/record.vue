<template>
  <div>
    <div>
      <el-form ref="form" :model="recordTypeInfo" label-width="80px" id="recordTypeInfoId">
        <el-form-item label="类型">
          <el-input v-model="updateInfo.typekey"></el-input>
        </el-form-item>
        <el-form-item label="类型值">
          <el-input v-model="updateInfo.typeval"></el-input>
        </el-form-item>
        <el-button type="primary" @click="addForm">新增</el-button>
        <el-form-item>
          <el-button type="primary" @click="saveRecordType">添加分类信息</el-button>
          <el-button>取消</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-select v-model="typeSel" placeholder="请选择类型">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div>
      <el-input v-model="allIds" placeholder="请输入内容"></el-input>
      <el-input v-model="types" placeholder="请输入内容"></el-input>
      <el-button type="primary" @click="findRecordInfo">获取分类信息</el-button>
      <el-button type="primary" @click="tableRecordInfo">获取表格数据</el-button>
    </div>
    <div>
      <el-table
        :data="tableData"
        style="width: 100%">
        <el-table-column
          prop="id"
          label="详情编码"
          width="180">
        </el-table-column>
        <el-table-column
          prop="info"
          label="详情内容"
          width="180">
        </el-table-column>
        <el-table-column
          prop="type"
          label="分类信息">
        </el-table-column>
        <el-table-column
          prop="name"
          label="分类名称">
        </el-table-column>
        <el-table-column
          prop="crtTime"
          label="添加时间">
        </el-table-column>
        <el-table-column
          prop="saveTime"
          label="保存时间">
        </el-table-column>
      </el-table>
    </div>
  </div>

</template>

<script>
import httpNew from "@/api/httpNew";
const api = new httpNew();
export default {
  data() {
    return {
      recordTypeInfo:{
        typekey:'',
        typeval:''
      },
      tableData:[],
      allIds:'',
      types:'',
      options:'',
      typeSel:{},
      recordTypeAlls:[]
    }
  },
  mounted() {
    this.showRecordInfo()
  },
  methods: {
    saveRecordType(){
      var data = {};
      data.mapInfos = recordTypeAlls
      api.saveRecordType(data).then(response => {
        if(response.status == "0"){
          this.$message("保存分类信息成功");
        }
      })
    },
    addForm(){
      let info = '<el-form-item label="类型">\n' +
        '          <el-input v-model="updateInfo.typekey"></el-input>\n' +
        '        </el-form-item>\n' +
        '        <el-form-item label="类型值">\n' +
        '          <el-input v-model="updateInfo.typeval"></el-input>\n' +
        '        </el-form-item>'
      this.recordTypeAlls.push(this.recordTypeInfo)
      document.getElementById('recordTypeInfoId').appendChild(info)
    },
    showRecordInfo(){
      var data = {};
      data.page = 0;
      data.size = 1000;
      api.showRecordInfo(data).then(response => {
        if(response.status == "0"){
          response.data.forEach(val=>{
            if(null != val){
              var opt = {
              }
              opt.label = val.typeName;
              opt.value = val.type
              this.options.push(opt)
            }
          })
        }
      })
    },
    findRecordInfo(){
      var data = {};
      data.allIds = this.allIds
      data.types = this.types
      api.findRecordInfo(data).then(response => {
        if(response.status == "0"){
          this.options = [];
          this.options = response.data
        }
      })
    },
    tableRecordInfo(){
      var data = {};
      data.page = this.page;
      data.size = '20';
      api.tableRecordInfo(data).then(response => {
        this.tableData = response.data;
      })
    }
  }
}
</script>

<style scoped>

</style>
