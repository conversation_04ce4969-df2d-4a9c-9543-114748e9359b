<template>
  <div>
    <div>
      <el-input v-model="operatorLogId" placeholder="操作日志id"></el-input>
      <el-input v-model="logType" placeholder="日志分类"></el-input>
      <el-input v-model="loginfo" placeholder="日志内容"></el-input>
      <el-input v-model="topicType" placeholder="话题分类id"></el-input>
      <el-input v-model="operatorId" placeholder="操作人员id"></el-input>
      <el-button onclick="getSaveResult()">查询个人用户日志信息</el-button>
    </div>
    <div>
      <el-input v-model="topicId" placeholder="话题id"></el-input>
      <el-button onclick="getSaveTopic()">修改话题信息</el-button>
    </div>
    <template>
      <el-table
        :data="logData"
        stripe
        style="width: 100%">
        <el-table-column
          prop="operatorLogId"
          label="操作日志id"
          width="180">
        </el-table-column>
        <el-table-column
          prop="logType"
          label="日志分类"
          width="180">
        </el-table-column>
        <el-table-column
          prop="loginfo"
          label="日志内容">
        </el-table-column>
        <el-table-column
          prop="topicType"
          label="话题分类id">
        </el-table-column>
        <el-table-column
          prop="operatorId"
          label="操作人员id">
        </el-table-column>
      </el-table>
    </template>
    <div class="block">
      <el-pagination
        layout="prev, pager, next"
        :total="form.total">
      </el-pagination>
    </div>
    <div>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="规则编码">
          <el-input v-model="form.id"></el-input>
        </el-form-item>
        <el-form-item label="规则名称">
          <el-input type="textarea" v-model="form.rulesname"></el-input>
        </el-form-item>
        <el-form-item label="规则介绍URL">
          <el-input type="textarea" v-model="form.ruleUrl"></el-input>
        </el-form-item>
        <el-form-item label="规则说明">
          <el-input type="textarea" v-model="form.rules"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="createWordRule">立即创敏感词</el-button>
          <el-button>取消</el-button>
        </el-form-item>
      </el-form>
      <el-button type="primary" @click="updateWord">修改敏感词</el-button>
    </div>
    <div>
<!--      评论-->
      <el-select v-model="nameInfo" placeholder="请选择" onclick="showKeyWord">
        <el-option
          v-for="item in names"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <el-select v-model="isLike" placeholder="请选择">
        <el-option label="是" value="true"></el-option>
        <el-option label="否" value="false"></el-option>
      </el-select>
      <el-input
        type="textarea"
        :rows="2"
        placeholder="请输入评论日志"
        v-model="textarea">
      </el-input>
      <el-button onclick="saveTalkLog">保存评论日志信息</el-button>
      <el-input
        type="textarea"
        :rows="2"
        placeholder="修改评论意见"
        v-model="commentMessage">
      </el-input>
      <el-input v-model="record" placeholder="备注"></el-input>
      <el-rate v-model="point"></el-rate>
      <el-button onclick="fixtopicInfo">修改评论意见</el-button>
      <el-button onclick="updateTopicInfo">修改话题评论详情</el-button>
    </div>
    <div>
      <el-form ref="form" :model="commandTopic" label-width="80px">
        <el-form-item label="话题分类">
          <el-input v-model="commandTopic.type"></el-input>
        </el-form-item>
        <el-form-item label="话题推荐人数">
          <el-input v-model="commandTopic.numbers"></el-input>
        </el-form-item>
        <el-form-item label="话题查询时间">
          <el-input v-model="commandTopic.reason"></el-input>
        </el-form-item>
        <el-form-item label="话题综合推荐指数">
          <el-input v-model="commandTopic.point"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="saveWaitCommandTopic">立即创建</el-button>
          <el-button>取消</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-input v-model="topListInfo" placeholder="置顶清单"></el-input>
      <el-button onclick="saveTopicList">添加置顶清单</el-button>
    </div>
    <div>
      <el-table
        :data="commandTopListTable"
        style="width: 100%">
        <el-table-column
          prop="info"
          label="话题推荐信息"
          width="180">
        </el-table-column>
        <el-table-column
          prop="reason"
          label="话题推荐原因"
          width="180">
        </el-table-column>
        <el-table-column
          prop="numbers"
          label="话题推荐评论人员">
        </el-table-column>
        <el-table-column
          prop="point"
          label="话题综合推荐指数">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import httpNew from "@/api/httpNew";
const api = new httpNew();
export default{
  props:['articleId'],
  data(){
    return{
      logData:[],
      commandTopListTable:[],
      textarea:'',
      topListInfo:'',
      commandTopic:{},
      state:'',
      nameInfo:'',
      names:[],
      record:'',
      point:'',
      data:{
        operatorLogId:'',
        logType: '',
        loginfo: '',
        topicType: '',
        operatorId: '',
        topicId:'',
        form:{
          id:'',
          ruleUrl:'',
          rules:'',
          rulesname:'',
          total:''
        }
      },
      id:'',
      keyword:'',
      isLike:'',
      commentMessage:'',
      canWaitcommand:''
    }
  },
  mounted(){
    this.getSaveResult();
  },
  methods:{
    getSaveResult(){
      api.getSaveResult(this.data).then(response => {
        if(response.status == "0"){
          this.$message('保存成功');
        }
      })
    },
    getSaveTopic(){
      this.data.logType = '1'
      api.getSaveTopic(this.data).then(response => {
        if(response.status == "0"){
          this.$message({message: '修改成功',type: 'info'});
        }
      })
    },
    wordRules(){
      api.wordRules(this.form).then(response => {
        if(response.status == "0"){
          this.$message({message: '添加成功',type: 'info'});
        }else {
          this.$message({message: '添加失败',type: 'error'});
        }
      })
    },
    updateWord(){
      this.form.id = 'id'
      api.updateWord(this.form).then(response => {
        if(response.status == "0"){
          this.$message({message: '修改成功',type: 'info'});
        }
      })
    },
    showWord(){
      api.showWord().then(response => {
        if(response.status == "0"){
          response.data.forEach(li => {
            li.id = 'rule:'+li.id;
          })
          this.logData = response.data();
        }
      })
    },
    showKeyWord(){
      api.showkeyWord().then(response => {
        if(response.status == "0"){
          var options = {}
          response.data.forEach(li => {
            options.label = li.name;
            options.value = li.name;
          })
          this.names.push(options)
          this.logData = response.data();
        }
      })
    },
    saveTalkLog(){
      var info = {};
      info.logId = this.form.id;
      info.logInfos = this.textarea;
      info.netId = this.logData[0].id
      api.saveTalkLog().then(response => {
        if(response.status == "0"){
          this.$message({message: '保存日志信息',type: 'info'});
        }
      })
    },
    fixtopicInfo(){
      var message = {};
      message.topicId = this.id;
      message.topicInfo = this.commentMessage;
      message.numbers = '';
      message.info = '';
      api.fixtopicInfo(message).then(response => {
        if(response.status == "0"){
          this.$message('保存日志信息');
        }
      })
    },
    updateTopicInfo(){
      var data = {};
      data.id = this.id;
      data.pingfen = this.point;
      data.yijian = this.commentMessage;
      api.fixtopicInfo(data).then(response => {
        if(response.status == "0"){
          this.$message('保存日志信息');
        }
      })
      this.showTopicUpdateInfo()
    },
    showTopicUpdateInfo(){
      api.fixtopicInfo(this.id).then(response => {
        if(response.status == "0"){
          this.$message(response.data);
        }
      })
    },
    saveWaitCommandTopic(){
      this.commandTopic.id = this.id
      this.findCommandTopic();
      if(this.canWaitcommand){
        api.saveWaitCommandTopic(this.commandTopic).then(response => {
          if(response.status == "0"){
            this.$message("添加待推荐话题成功");
          }
        })
      }else{
        this.$message("无法添加");
      }
    },
    findCommandTopic(){
      api.findCommandTopic(this.commandTopic).then(response => {
        if(response.status == "0"){
          this.canWaitcommand = true;
        }else{
          this.canWaitcommand = false;
        }
      })
    },
    saveTopicList(){
      this.commandTopic.topList = this.topListInfo;
      api.saveTopicList(this.commandTopic).then(response => {
        if(response.status == "0"){
          this.$message("添加话题清单置顶成功");
        }
      })
    },
    showCommandList(){
      let data = {}
      data.page=0;
      data.limit = 100;
      data.type = "topic"
      data.id = this.id
      api.showCommandList(data).then(response => {
        if(response.status == "0"){
          this.commandTopListTable = response.data.data
        }
      })
    },
    deleteCommandTopic(){
      var inf = {};
      inf.netId = this.id
      api.deleteCommandTopic(inf).then(response => {
        if(response.status == "0"){
          this.$message("删除待推荐话题成功");
        }
      })
    }
  }
}
</script>

<style>
</style>

