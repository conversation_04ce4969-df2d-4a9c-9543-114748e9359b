<template>
<div>
  <div>
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="模糊简称">
        <el-input v-model="form.nameLikeInfo"></el-input>
      </el-form-item>
      <el-form-item label="操作人员">
        <el-input v-model="form.createUser"></el-input>
      </el-form-item>
      <el-form-item label="操作时间">
        <el-col :span="11">
          <el-date-picker type="date" placeholder="修改时间" v-model="form.crtTime" style="width: 100%;"></el-date-picker>
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="personInfo">立即创建</el-button>
        <el-button>取消</el-button>
      </el-form-item>
    </el-form>
  </div>
  <div>
    <el-button type="primary" @click="showPersonInfo">查询话题管控配置列表</el-button>
    <template>
      <el-table load="userloading"
        :data="tableTable"
        border
        style="width: 100%">
        <el-table-column
          prop="nameLikeInfo"
          label="管控编码"
          width="180">
        </el-table-column>
        <el-table-column
          prop="author"
          label="管控权限"
          width="180">
        </el-table-column>
        <el-table-column
          prop="user"
          label="管控用户">
        </el-table-column>
      </el-table>
    </template>
  </div>
</div>
</template>

<script>
import httpNew from "@/api/httpNew";
const api = new httpNew();
export default {
  data() {
    return {
      tableTable:[],
      form: {
        nameLikeInfo: '',
        createUser: '',
        crtTime: ''
      },
      userloading:false
    }
  },
  methods: {
    personInfo() {
      api.personInfo(this.form).then(response => {
        if(response.status == "0"){
          this.$message('保存简略用户信息成功');
        }
      })
      this.showPersonInfo()
    },
    showPersonInfo(){
      this.userloading = true
      api.showPersonInfo(this.form.nameLikeInfo).then(response => {
        if(response.status == "0"){
          this.tableTable = response.data;
          this.userloading = false;
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
