<template>
  <div>
    <div>
      <el-input v-model="name" placeholder="请输入粉丝名称"></el-input>
      <el-button onclick="getMyFollowInfo">查询</el-button>
      <el-select v-model="fans" placeholder="请选择">
        <el-option
          v-for="item in fansOption"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <el-button onclick="getMyFollowStatusDetail">获取粉丝详情</el-button>
      <el-descriptions title="粉丝详情">
        <el-descriptions-item label="粉丝详情编码">{{ fanInfo.fanId }}</el-descriptions-item>
        <el-descriptions-item label="粉丝名称">{{ fanInfo.name }}</el-descriptions-item>
        <el-descriptions-item label="头像">{{ fanInfo.advtor }}</el-descriptions-item>
        <el-descriptions-item label="个人介绍">{{fanInfo.introduce}}</el-descriptions-item>
        <el-descriptions-item label="粉丝状态">{{fanInfo.status}}</el-descriptions-item>
        <el-descriptions-item label="修改时间">{{fanInfo.uptTime}}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div>
      <el-switch
        v-model="callFollow"
        active-text="取关"
        inactive-text="回关">
      </el-switch>
      <el-table
        :data="tableData"
        style="width: 100%">
        <el-table-column
          prop="dataId"
          label="数据操作ID"
          width="180">
        </el-table-column>
        <el-table-column
          prop="name"
          label="粉丝名称"
          width="180">
        </el-table-column>
        <el-table-column
          prop="desc"
          label="粉丝介绍">
        </el-table-column>
        <el-table-column
          prop="status"
          label="粉丝状态">
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageInfo.pageNum"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="pageInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageInfo.total">
        </el-pagination>
      </div>
    </div>
    <div>
      <el-input v-model="recordId" placeholder="收藏编码"></el-input>
      <el-input v-model="recordInfo" placeholder="收藏名称"></el-input>
      <el-input v-model="info" type="textarea" placeholder="列表详情"></el-input>
      <el-button onclick="">我的收藏</el-button>
    </div>
    <div>
      <el-button onclick="queryBaseInfo">我的小知识详情</el-button>
      <el-table
        :data="tipsData"
        style="width: 100%">
        <el-table-column
          prop="name"
          label="知识名称"
          width="180">
        </el-table-column>
        <el-table-column
          prop="time"
          label="关注时间"
          width="180">
        </el-table-column>
        <el-table-column
          prop="nums"
          label="关注人数">
        </el-table-column>
        <el-table-column
          prop="crtTime"
          label="创建时间">
        </el-table-column>
      </el-table>
      <div>
        <el-input v-model="article" placeholder="文章"></el-input>
        <el-input v-model="topic" placeholder="话题"></el-input>
        <el-input v-model="tips"  placeholder="小知识"></el-input>
        <el-button onclick="getRecommendInfo">我的推荐信息</el-button>
        <el-button onclick="getRecommendArtTipsList">我的推荐小知识</el-button>
        <el-table
          :data="ownCommand"
          style="width: 100%">
          <el-table-column
            prop="type"
            label="类型"
            width="180">
          </el-table-column>
          <el-table-column
            prop="name"
            label="名称"
            width="180">
          </el-table-column>
          <el-table-column
            prop="time"
            label="发布时间"
            width="180">
          </el-table-column>
          <el-table-column
            prop="nums"
            label="关注人数">
          </el-table-column>
          <el-table-column
            prop="crtTime"
            label="评论信息">
          </el-table-column>
          </el-table>
        </div>
    </div>
    <div>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="专题编码">
          <el-input v-model="form.id"></el-input>
        </el-form-item>
        <el-form-item label="关注人数">
          <el-input v-model="form.rnums"></el-input>
        </el-form-item>
        <el-form-item label="文章数量">
          <el-input v-model="form.counts"></el-input>
        </el-form-item>
        <el-form-item label="投稿人数">
          <el-input v-model="form.cnums"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getMySubjectList">立即查找</el-button>
          <el-button>取消</el-button>
        </el-form-item>
      </el-form>
      <el-autocomplete
        v-model="state"
        :fetch-suggestions="querySearchAsync"
        placeholder="请输入内容"
        @select="handleSelect"
      ></el-autocomplete>
      <el-button onclick="getMySubjectVersionDetail">专题版本详情</el-button>
    </div>
  </div>
</template>

<script>
import httpNew from "@/api/httpNew";
const api = new httpNew();
export default {
  data() {
    return {
      name: '',
      recordId:'',
      article:'',
      topic:'',
      tips:'',
      recordInfo:'',
      tipsData: {},
      ownCommand:{},
      info:'',
      tableData:'',
      id:'',
      fanInfo:{
        fanId:'',
        name:'',
        advtor:'',
        introduce:'',
        status:'',
        uptTime:''
      },
      fansOption: {},
      fans:'',
      callFollow:'',
      pageInfo:{
        pageNum:'',
        pageSize:10,
        total:10
      },
      restaurants:'',
      selectName:'',
      results:''
    }
  },
  mounted() {
    this.getMyFollowOperationRecord()
  },
  methods:{
    querySearchAsync(queryString, cb) {
      this.getMySubjectManageList()
      var results = this.results
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        cb(results);
      }, 3000 * Math.random());
    },
    handleSelect(item) {
      this.selectName = item;
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
    getMyFollowInfo(){
      api.getMyFollowInfo(this.id).then(response => {
        if(response.status == "0"){
          this.$message('查询成功');
          this.getMyFollowList()
          this.getMyFollowOperationRecord()
        }else {
          this.$message('获取粉丝列表失败');
        }
      });
    },
    getMyFollowList(){
      api.getMyFollowList().then(response => {
        if(response.status == "0"){
          var result = response.data.data
          for (let i = 0; i < result.length; i++) {
              var opt = {}
              opt.lab = result[i].option;
              opt.val = result[i].value;
              this.fansOption.push(opt)
          }
        }
      });
    },
    getMyFollowOperationRecord(){
      var pageInfo = this.pageInfo
      api.getMyFollowOperationRecord(pageInfo).then(response => {
        if(response.status == "0"){
          var result = response.data.data
          this.tableData = result
        }
      });
    },
    getMyFollowStatusDetail(){
      var data = {}
      data.id = this.id
      api.getMyFollowStatusDetail(data).then(response => {
        if(response.status == "0"){
          var result = response.data.data
          result.uptTime = new Date(result.uptTime);
          this.fanInfo = result;
        }
      });
    },
    detail(){
      var data = {}
      data.id = this.id
      api.getMyCollectdetail(data).then(response => {
        if(response.status == "0"){
          this.$message('获取收藏详情');
        }
      });
    },
    queryBaseInfo(){
      api.queryBaseInfo().then(response => {
        if(response.status == "0"){
          this.$message('获取基本详情成功');
          this.tipsData = response.data
        }
      });
    },
    getRecommendInfo(){
      var data = {};
      data.recommendType='article';
      data.pageNum=this.pageInfo.pageNum
      data.size=this.pageInfo.size
      api.getRecommendInfo(data).then(response => {
        if(response.status == "0"){
          this.$message(response.data)
        }
      });
    },
    getRecommendTopicDetail(){
      var data = {};
      data.recommendType='topic';
      data.id = this.id
      api.getRecommendTopicDetail(data).then(response => {
        if(response.status == "0"){
          this.$message(response.data)
        }
      });
    },
    getRecommendArtTipsList(){
      var data = {};
      data.recommendType='tips';
      data.id = this.id
      api.getRecommendArtTipsList(data).then(response => {
        if(response.status == "0"){
          this.ownCommand  = response.data
        }
      });
    },
    getMySubjectList(){
      var data = this.form
      data.pageNum=this.pageInfo.pageNum
      data.size=this.pageInfo.size
      api.getMySubjectList(data).then(response => {
        if(response.status == "0"){
          this.ownCommand  = response.data
        }
      });
    },
    getMySubjectManageList(){
      var data = this.form
      api.getMySubjectManageList(data).then(response => {
        if(response.status == "0"){
          this.results  = response.data
        }
      });
    },
    getMySubjectVersionDetail(){
      var data = {};
      data.id = this.id
      data.pageNum=this.pageInfo.pageNum
      data.size=this.pageInfo.size
      api.getMySubjectVersionDetail(data).then(response => {
        if(response.status == "0"){
          this.ownCommand  = response.data
        }
      });
      this.removeMySubject()
    },
    removeMySubject(){
      var data = {};
      data.id = this.id
      api.removeMySubject(data).then(response => {
        if(response.status == "0"){
          this.$message('专题移除成功');
        }else {
          this.$message('专题移除失败');
        }
      });
    },

  }
}
</script>

<style scoped>

</style>
