<template>
  <div class="setUser createTheme themeInner_content">
    <div class="userInfor">
      <div class="user_title">{{themeId?"修改专题":"新建专题"}}</div>
      <div class="avatar_box">
        <div :class="themeForm.cover?'cover-image':'image'">
        	<img v-if="themeForm.cover" :src="themeForm.cover" class="avatar" :onerror="errorImg"/>
          <img v-else src="../../assets/image/bg4.png" alt="" />
        </div>
        <el-upload
          class="avatar-uploader"
	      	accept=".gif,.jpg,.jpeg,.bmp,.png"
	       	:show-file-list="false"
	       	:action="uploadPath"
	       	:on-success="handleAvatarSuccess"
	       	:before-upload="checkAcceptType"
          :on-error="uploadError"
		    >
          <div class="change_avatar">点击上传封面</div>
        </el-upload>
      </div>
      <el-form
        :model="themeForm"
        ref="userForm"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="名称" prop="name"
        	:rules="[
			      { required: true, message: '请输入专题名称', trigger: 'blur' }
			    ]"
				>
          <el-col :span="22">
            <el-input
              v-model="themeForm.name"
              placeholder="填写名称，不超过20个字"
              maxlength="20"
              spellcheck="false"
              show-word-limit
            ></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="描述" prop="desc"
        	:rules="[
			      { required: true, message: '请输入专题描述', trigger: 'blur' }
			    ]"
			  >
          <el-input
            type="textarea"
            v-model="themeForm.desc"
            placeholder="填写描述"
            spellcheck="false"
            maxlength="250"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="指定版主">
					<el-input readonly v-model="adminName">
						<img slot="suffix" @click="openAdminSetTree" style="width:16px;" src="../../assets/image/person-choose.png" alt="" title="选择">
					</el-input>
					 <!--@click="getPersonTree('创建人','personTree','CREATER','createrName')"-->
					<!--<span class="mgl-5 pointer fs-13 color-blue1" @click="clear('CREATER','createrName');">清空</span>-->
        </el-form-item>
        <el-form-item label="是否允许投稿" prop="contribution">
          <el-radio-group v-model="themeForm.contribution">
            <el-radio label="1">允许</el-radio>
            <el-radio label="2">不允许</el-radio>
          </el-radio-group>
        </el-form-item>
        <!--<el-form-item label="是否需要审核" prop="approval">
          <el-radio-group v-model="themeForm.approval">
            <el-radio label="1">需要</el-radio>
            <el-radio label="2">不需要</el-radio>
          </el-radio-group>
        </el-form-item>-->
        <el-button @click="submitForm('userForm')" class="submit_button"
          >保存</el-button
        >
      </el-form>
    </div>
    <div class="nav">{{themeId?"变更专题":"创建专题"}}</div>
    <el-dialog class="tree-dialog" :title="dialog.adminSetTree.title" :visible.sync="dialog.adminSetTree.visible" width="85%" v-if="dialog.adminSetTree.visible">
			<adminSetTree ref="adminSetTree" :param="adminSetTreeData" @selectPersons="selectPersons"></adminSetTree>
		</el-dialog>
  </div>
</template>

<script>
import httpCore from "@/api/httpCore";
import adminSetTree from "./component/adminSetTree";
const api = new httpCore();
import errorPic from "@/assets/image/errorPic.png";
export default {
  name: "createTheme",
  data() {
    return {
    	themeId:'',
      themeForm: { parentId:"", contribution:"1", approval:"1", sequence:"",cover:"" },
      uploadPath:'/file-upload-service/v1/paas_attachments_tool/upload/lezhi-service',
      downloadPath:'/file-upload-service/v1/paas_attachments_tool/download?id=',
      adminName:"",
      dialog: {
        adminSetTree: {
          title: "指定版主",
          visible: false
        }
	    },
	    adminSetTreeData: {
        checkType:'checkBox',//选择类型
        defaultCheckVal:"",//选中的Key值数组
        defaultCheckItem:[]
	    },
	    errorImg: 'this.src="' + errorPic + '"',
      typeList:[
        'image/bmp',//.bmp
        'image/gif',//.gif
        'image/vnd.microsoft.icon',//.ico
        'image/jpeg',//.jpeg .jpg
        'image/png',//.png
        'image/svg+xml',//.svg
        'image/webp',//.webp
      ],
      extList:[
        '.bmp',
        '.gif',
        '.ico',
        '.jpeg','.jpg',
        '.png',
        '.svg',
        '.webp',
      ],
    };
  },
  components:{adminSetTree},
  mounted(){
  	if(this.$route.query.themeId){
  		this.themeId=this.$route.query.themeId;
  		this.getThemeInfo();
  	}
  },
  methods: {
  	getThemeInfo(){
  		api.queryThemeInfo(this.themeId).then(response => {
    		var res = response.data;
				if(res.status == "0"){
					this.themeForm=res.data;
					this.adminName=this.themeForm.moderatorList.reduce(function(total,item){
						item.name = item.accountName;
						return total+=(total?"，"+item.accountName:item.accountName);
					},"");
				}else {
					this.$message({'message': res.msg ,'type': 'error'});
				}
			})
			.catch(err => {
				console.log(err);
			});
  	},
  	openAdminSetTree(){
      this.dialog.adminSetTree.visible = true;
      this.adminSetTreeData.defaultCheckItem = this.themeForm.moderatorList;
  	},
  	selectPersons: function(checkedItems){
      this.adminName=checkedItems.reduce(function(total,item){
				return total+=(total?"，"+item.name:item.name);
			},"");
      this.dialog.adminSetTree.visible = false;
      this.themeForm.moderatorList=checkedItems;
    },
    submitForm(formName) {
    	if(!this.themeForm.cover){
    		this.$message({message:'专题封面未上传',type:'warning'});
    		return false;
    	}
      this.$refs[formName].validate((valid) => {
        if (valid) {
        	//校验成功
        	var data = this.themeForm;
        	api.saveTheme(this.themeId,data).then(response => {
        		var res = response.data;
						if(res.status == "0"){
							this.$message({'message': res.msg,'type': 'success'});
							this.$router.push("/theme");
						}else {
							this.$message({'message': res.msg ,'type': 'error'});
						}
					})
					.catch(err => {
						console.log(err);
					});
        } else {
          return false;
        }
      });
    },
    handleAvatarSuccess(res, file) {
      this.themeForm.cover = this.downloadPath+res.data;
    },
    checkAcceptType(file) {
      // console.log(file);
      if(this.typeList.indexOf(file.type)<0 && this.extList.findIndex(ext => file.name.endsWith(ext))<0){
        this.$message({message: '附件格式不允许上传' ,type: 'warning'});
        return false;
      }
    },
    uploadError(err, file, fileList){
      this.$message({message: '附件上传失败' ,type: 'error'});
    },
    checkSubjectRepeat(){
      api.checkSubjectRepeat({subjectName: this.themeForm.name}).then(res => {
        if (res.status == "0"){
          this.$message.error("专题已存在")
        }
      })
    }
  },
};
</script>
<style lang="scss">
</style>
