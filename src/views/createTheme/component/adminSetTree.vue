<template>
	<div>
		<div class="person-tree">
			<div class="person-tree-left">
				<el-input class="search-input-def" placeholder="输入区域、部门名称、员工姓名进行查询" v-model="searchInfo" @keyup.enter.native="queryTree">
			    <i slot="suffix" @click="queryTree" class="el-icon-search"></i>
			  </el-input>
			  <div style="display: flex;height:100%;border-top:1px solid #E9E9E9;">
			  	<div style="flex:1;">
			  	<el-scrollbar class="tree-scrollbar" style="height:calc(100% - 72px);">
			  		<div v-if="searchFlg" class="tree-title search-title">检索到的部门</div>
			  		<el-tree
			  			:class="searchFlg?'search-tree-def':'person-tree-def'"
							:default-expanded-keys="['zuzhi']"
							:props="defaultProps"
							:data="personTree"
							:check-strictly="checkStrictly"
							:show-checkbox="!deptDisabled"
							:load="loadNode"
	            lazy
							ref="personTree"
							node-key="id"
							:filter-node-method="filterNode"
							:default-checked-keys="defaultCheckVal"
							@check="singleTreeControl"
							@check-change="checkChange"
							:expand-on-click-node="false"
							@node-click="getPersonsOfDept"
							>
							<span class="custom-tree-node" slot-scope="{ node, data }">
		            <span>
		            	<template v-if="data.id=='zuzhi'"><img style="width:16px" src="../../../assets/image/tree-parent-node.png" alt="" /></template>
		            	<template v-else><img style="width:16px" src="../../../assets/image/tree-folder-node.png" alt="" /></template>
		              <span class="pdl-5" :style="data.id==currentDeptId?'color:#1E8EDA;font-weight:bold;':''">{{ node.label }}</span>
		            </span>
		        	</span>
		    		</el-tree>
			  	</el-scrollbar>
			  	</div>
			  	<div style="flex:1;">
			  		<div class="tree-title" style="border-left:1px solid #E9E9E9;"><el-checkbox size="mini" @change="allSelect" v-model="allSelected" :disabled="param.checkType=='radio'"></el-checkbox><span class="mgl-5">{{currentDept}}</span><span class="fr mgr-20" style="color:#909199;">{{getSelectedLength}}/{{personListOfCurrentDept.length}}</span></div>
						<el-scrollbar  class="tree-scrollbar" style="height:calc(100% - 108px);min-height:calc(100% - 108px);border-left:1px solid #C0C4CC;">
							<ul class="person-list">
							<template v-if="personListOfCurrentDept.length>0">
								<li v-for="(obj,index) in personListOfCurrentDept">
									<span style="width:25%;" :style="obj.selected?'':'color:#606166;'" class="person-desc pointer" :title="obj.name" @click="selectPerson(obj,index);"><i :style="obj.selected?'':'color:#1E8EDA;'" class="el-icon-user-solid pdr-10"></i>{{ obj.name}}</span>
			            <span class="person-desc fs-10" :title="obj.id">{{ obj.id }}</span>
			            <span style="width:50%;" class="person-desc fs-10" :title="obj.dept?obj.dept:obj.mobile">{{ obj.dept?obj.dept:obj.mobile }}</span>
								</li>
							</template>
							<li class="pdl-40" v-else>暂无数据</li>
							</ul>
						</el-scrollbar>
			  	</div>
			  </div>
			</div>
			<div class="person-tree-right">
				<div class="tree-title">已选人员<span @click="clearTree()" class="fr color-light-black mgr-10 pointer">清空全部<i class="el-icon-delete pdl-5 fs-14" style="color:#FF6262;font-weight:600;"></i></span></div>
				<el-scrollbar class="tree-scrollbar" style="height:calc(100% - 36px);">
					<ul class="person-list">
					<template v-if="submitNodes!=null && submitNodes.length>0">
						<li v-for="(obj,index) in submitNodes" :key="index">
							<span :title="obj.name">{{ obj.name }}</span>
	            <span class="fr">
	            	开放移动权限
	            	<el-switch
	            		style="margin-top: -5px;"
								  v-model="obj.transfer"
								  active-value="1"
    							inactive-value="2"
								>
								</el-switch>
	            	<i @click="delCheckNodes(obj.id,obj.name)" class="el-icon-error mgl-15 fs-14 pointer" style="color:#909199;background-color:#F5F6F6;"></i>
	            </span>
						</li>
					</template>
					<li class="pdl-40" v-else>暂无数据</li>
					</ul>
				</el-scrollbar>
			</div>
		</div>
		<div class="mgt-30 pdt-20 pdr-20 pdl-20" style="border-top: 1px solid #F4F3F4;margin-left: -20px;margin-right: -20px;overflow:hidden;">
				<span class="fr" style="position:relative;z-index:99;">
					<el-button class="submit-button mgl-10" @click="submitTree">提交</el-button>
				</span>
			</div>
		</div>
	</div>
</template>

<script>
import httpCore from "@/api/httpCore";
const api = new httpCore();
const debounce = (function () {
    let timer = 0
    return function (func, delay) {
      clearTimeout(timer)
      timer = setTimeout(func, delay)
    }
})()
export default {
    name:"commonPersonTree",
//  param{  checkType 【radio,checkbox】 选择类型,
//  	    defaultCheckItem 默认选中值,
//          checkStrictly 父子不互相关联的做法【false:关联,true:不关联】,
//  	    specialType 权限控制【false:不控制,true:控制】,
//          deptId 部门id(权限控制true的场合,必须传值)
//          oneLayer：是否只展示本级不展示子节点【false:都展示,true:只展示本级】
//          deptDisabled 部门是否禁用【true:禁用不可选,false:可选】 
//       }
    props:['param'],
   	data(){
   		return {
	      //部门人员树
   			personTree:[],
   			defaultProps: {
	        id: 'id',
	        label:'name',
	        children: 'children',
	        isLeaf: 'leaf',//叶子节点展开折叠样式控制
	        disabled:'disSel'//父节点非活性样式的控制
	      },
	      defaultCheckVal:[],//初始值
	      checkStrictly:true,//父子不互相关联控制
	      initRootNodePerson:[{
   				id:'zuzhi',
   				name:'组织架构',
   				type:0, //部门
   				flag:1, //有子节点
   				disSel:true
   			}],
   			type:'0',// 0:人员树
   			specialType:false,  // 权限控制
   			specialTypeInit:false,
   			deptId:'',
   			deptDisabled:true,
	      oneLayer:false,
	      
	      currentDeptId:'zuzhi',
   			currentDept:'组织架构',
   			personListOfCurrentDept:[],
   			
   			submitNodes:[],
	        
	      searchInfo:'',
   			searchFlg:false,
   			allSelected:false,
   		}
   	},
   	created(){
    	if(this.param!=undefined){
	    	//部门是否禁用
	    	if(this.param.deptDisabled!=undefined){
	    	    this.deptDisabled=this.param.deptDisabled;
	    	}
   			//树的权限控制
   			if(this.param.specialType!=undefined){
   				this.specialType=this.param.specialType;
   				if(this.specialType){
	   				if(this.param.deptId!=undefined){
	   				   this.deptId=this.param.deptId;
	   				}
   			    }
   			}
   			//父子不互相关联控制
   			if(this.param.checkStrictly!=undefined){
   				this.checkStrictly=this.param.checkStrictly;
   			}
   			//是否只展示本级
	   	  if(this.param.oneLayer!=undefined){
	   	    this.oneLayer=this.param.oneLayer;
	   	  }
   		}
   	},
    mounted(){
 	    if(this.param && this.param.defaultCheckItem){
  	    this.defaultCheckVal=this.param.defaultCheckItem.map(function(item){
  	    	return item.id;
  	    });
  	    this.submitNodes=this.param.defaultCheckItem;
 	    }
   	},
   	computed: {
	    getSelectedLength: function () {
	      var list = this.personListOfCurrentDept.filter(function(val){
   				return val.selected;
   			});
   			return list.length;
	    }
	  },
   	methods: {
   		//人员树结构加载
   		loadNode:function(node, resolve){
   			if(!this.searchFlg && node.level === 0){
   				return resolve(this.initRootNodePerson);
   			}
   			//顶层节点加载
   			if(!this.specialType && !this.searchFlg && node.level == 1){
   				this.loadNodeParent(resolve);
   			}
   			//子节点展开加载
   			if (this.specialType || this.searchFlg || (!this.searchFlg && node.level >= 2)) {
   				this.loadNodeChild(node,resolve);
        }
   		},
   		//顶级节点加载
			loadNodeParent:function(resolve){
				var data={};
			  api.queryOrgList(data).then(resp =>{
					var tempData=resp.data.data.entity;
					this.setLeaf(tempData);
					resolve(tempData);
				}).catch(err =>{
					console.log(err);
				});
			},
   		//叶子节点样式控制(去掉展开样式、禁用控制)
			setLeaf:function(tempData){
				tempData.forEach(item => {
					var leaf=true;
					var disSel=false;
					// flag=0：没有子节点  flag=1：有子节点
					if(item.flag == '1'){
						leaf=false;
					}
					if(this.deptDisabled){
							disSel=true;
					}
					item.disSel=disSel;
					item.leaf=leaf;
				})
				return tempData;
			},
   		//权限树 初期值
			initSpecialTempData:function(tempData){
			   let initTree=[{id:tempData.id,name:tempData.name,code:tempData.code,type:'0',flag:"1",children:[]}];
			   tempData=initTree;
			   return tempData;
			},
			//子节点加载
			loadNodeChild:function(node,resolve){
				var id="";
				var initFlg=false;
				var url="";
				if(this.specialType && !this.specialTypeInit){
					id=this.deptId;
					this.specialTypeInit=true;
					initFlg=true;
					api.selectById({id:id,type:'1'}).then(resp => {
						var tempData=resp.data.data.entity;
						tempData =this.initSpecialTempData(tempData);
						this.setLeaf(tempData);
						resolve(tempData);
					}).catch(err => {
						console.log(err);
					});
				}else{
					id=node.data.id;
					initFlg=false;
					api.queryOrgListById({id:id,type:'1'}).then(resp => {
						var tempData=resp.data.data.entity;
						this.setLeaf(tempData);
						resolve(tempData);
					}).catch(err => {
						console.log(err);
					});
				}
			},
   		//树节点过滤检索
	    filterNode:function(value, data,node) {
	        if (!value) return true;
	        return data.name.indexOf(value) !== -1;
	    },
   		//人员树初期
	    initPersonTree:function(){
        var data =this.initRootNodePerson;
        this.personTree=data;
        this.currentDeptId='zuzhi';
   			this.currentDept='组织架构';
   			this.personListOfCurrentDept=[];
   		},
   		//单选多选控制
			singleTreeControl:function (a,b) {
	    	if(this.param.checkType=='radio'){
	    		if (b.checkedKeys.length > 0) {
	    			this.clearTree();
		        this.$refs.personTree.setCheckedKeys([a.id]);
		    	}
	    		//组织结构树
	   	    var tempCheckData=this.$refs.personTree.getCheckedNodes();
		    	if(tempCheckData.length>0){
	        	this.submitNodes=JSON.parse(JSON.stringify(tempCheckData));
	        }else{
	        	this.submitNodes=[];
	        }
	    	}
			},
   		checkChange:function(data,checked,indeterminate){
				if(this.param.checkType!='radio'){
					if(checked){
						var num = this.submitNodes.filter(item => item.id==data.id).length;
						if(num==0){
							this.submitNodes.push(data);
						}
					}else{
					   this.submitNodes=this.submitNodes.filter(item => !(item.id == data.id));
					}	
				}
			},
   		getPersonsOfDept:function(obj,node){
				api.queryUserByOrgId(obj.id).then(response => {
					var tempData=response.data.data.entity;
					this.personListOfCurrentDept = tempData.map(function(val){
   					val.id=val.account;
   					val.selected = false;
	   				return val;
	   			});
	   			this.initSelectedData();
					this.currentDeptId=obj.id;
					this.currentDept=obj.name;
					this.allSelected=false;
				}).catch(err => {
		      console.log(err);
		    });
   		},
   		//搜索按钮
   		queryTree:function(){
   			var newV=this.searchInfo;
     		if(this.oneLayer){
					this.$refs.personTree.filter(newV);
				}else{
  	 	    if(newV!=undefined && newV.trim()!=""){
  	 	   	   this.searchFlg=true;
  	 	       this.searchNode(newV.trim());	
  	 	    }else if(newV ==undefined || newV==null || newV.trim()==""){
  	 	   	   this.searchFlg=false;
  	 	   	   this.specialTypeInit=false;
  	 	   	   this.initPersonTree();
  	 	    }
	 	    }
   		},
   		searchNode:function(newV){
	 	    var data = {name:newV,type:this.type,specialType:this.specialType,orgId:this.deptId};
		    api.queryOrgListByKeyWord(data)
			   .then(resp => {
			   	  var tempData=resp.data.data.entity;
			   	  this.setLeaf(tempData);
				    var deptTempData = tempData.filter(function(val){
							return val.type=='0';
						});
						this.personTree=deptTempData;
						var personTempData = tempData.filter(function(val){
							return val.type=='1';
						});
						// personTempData.forEach(function(val){
						// 	var nameTemp = val.name;
						// 	var nameArr = nameTemp.split("\(");
						// 	val.name = nameArr[0];
						// 	if(nameArr.length>0){
						// 		val.name = nameArr[0];
						// 		if(nameArr.length>1){
						// 			var deptArr = nameArr[1].split("|");
						// 			if(deptArr.length>1){
						// 				val.dept=deptArr[deptArr.length-2];
						// 			}
						// 		}
						// 	}
						// 	val.selected = false;
						// });
						// this.personListOfCurrentDept=personTempData;
            this.personListOfCurrentDept = personTempData.map(function(val){
            	val.id=val.account;
            	val.selected = false;
            	return val;
            });
						this.initSelectedData();
						this.currentDept="检索到的人员";
						this.allSelected=false;
			   })
			  .catch(err => {
				 console.log(err);
			  });
   		},
   		allSelect:function(){
   			if(this.allSelected){
   				var that = this;
   				this.personListOfCurrentDept.forEach(function(val,index){
   					that.selectPerson(val,index);
   				});
   			}else{
   				var that = this;
   				this.personListOfCurrentDept.forEach(function(val,index){
   					that.delCheckNodes(val.id,val.name);
   				});
   			}
   		},
   		selectPerson:function(obj,index){
   			if(this.param.checkType=='radio'){
   				this.clearTree();
   			}
   			this.$set(this.personListOfCurrentDept[index], 'selected', true);
   			var num = this.submitNodes.filter(item => item.id==obj.id).length;
				if(num==0){
					this.$set(obj,'transfer','2');
					this.submitNodes.push(obj);
				}
   		},
   		//提交按钮
   	  submitTree:function(){
 		  	var checkedItems = this.submitNodes.map(item =>{
	  		   return {id:item.id,name:item.name,transfer:item.transfer};
 		  	})
 		  	this.$emit('selectPersons',checkedItems);
   		},
   		//清空按钮
	  	clearTree:function(){
	  		this.submitNodes=[];
	  		this.$refs.personTree.setCheckedKeys([]);
	  		this.allSelected=false;
   			this.personListOfCurrentDept = this.personListOfCurrentDept.map(function(val){ val.selected=false; return val;});
	  	},
   		//删除按钮
	  	delCheckNodes:function(id,name){
	  		this.submitNodes=this.submitNodes.filter(item => item.id != id);
				this.setUnchecked(id);
	  	},
	  	//把某个节点设成未选中的状态
	  	setUnchecked:function(id){
	  		this.$refs.personTree.setChecked(id,false,false);
	  		this.personListOfCurrentDept = this.personListOfCurrentDept.map(function(val){
   				if(id==val.id){ val.selected=false;}
   				return val;
   			});
	  	},
			initSelectedData:function(){
				var submitNodes = this.submitNodes;
				for(var i=0;i<submitNodes.length;i++){
					this.personListOfCurrentDept = this.personListOfCurrentDept.map(function(val){
	   				if(submitNodes[i].id==val.id){
	   					val.selected=true;
	   				}
	   				return val;
	   			});
				}
			}
   	}
  }
</script>

<style lang="scss" scoped="scoped">
.person-tree{
	display: flex;
	background: #FFFFFF;
	height:450px;
	font-size:12px;
	color:#606166;
	/*::-webkit-scrollbar{
    display: none;
  }*/
}
.person-tree-left{
	flex:2;
	border: 1px solid #C0C4CC;
	border-radius: 2px;
}
.person-tree-right{
	flex:1;
	border: 1px solid #C0C4CC;
	border-radius: 2px;
	margin-left: 20px;
	height:100%;
}
.tree-title{
	height:36px;
	line-height: 36px;
	background: #F5F6F6;
	border-bottom: 1px solid #E9E9E9;
	padding-left: 20px;
}
.search-title{
	position:sticky;
	top:0;
	z-index:2;
}
.person-list{
	padding: 10px 20px;
}
.person-list li{
	padding-left: 20px;
	height:34px;
	line-height:34px;
}
.person-desc{
	display: inline-block;
	color:#C0C4CC;
	width:20%;
	overflow:hidden;/*超出隐藏 */ 
	text-overflow:ellipsis;
	white-space: nowrap;
}
.cancel-button{
	width: 64px;
	height: 32px;
	background: #C0C4CC;
	border-radius: 2px;
	color: #FFFFFF;
	font-size:12px;
}
.submit-button{
	width: 64px;
	height: 32px;
	background: #1E8EDA;
	border-radius: 2px;
	color: #FFFFFF;
	font-size:12px;
}
.choice-item {
  border: 1px solid #0889F6;
  border-radius: 6px;
  color: #0889F6;
  display: inline-block;
  padding: 0px 8px;
  height: 28px;
  line-height: 28px;
  margin-right: 5px;
}
</style>