import cryptoJs from 'crypto-js'
let keyOne = 'sitech0123456csd'
let key  = cryptoJs.enc.Utf8.parse(keyOne);
//class aesUtils{
//加密
 export function EncryptData(word){
	let enc = ''
	if(word!= null && word!=""){
	 	var srcs = cryptoJs.enc.Utf8.parse(word);
	    enc = cryptoJs.AES.encrypt(srcs, key, {
	        mode: cryptoJs.mode.ECB,
	        padding: cryptoJs.pad.Pkcs7
	      })
	    let result={aeskey:cryptoJs.enc.Base64.stringify(enc.ciphertext)};
        return result;
	}else{
	 	return null;
	}
}
// 解密
export function DecryptData(word){
    let dec = cryptoJs.AES.decrypt(word, key, {
      mode: cryptoJs.mode.ECB,
      padding: cryptoJs.pad.Pkcs7
    })
    let decData = cryptoJs.enc.Utf8.stringify(dec)
    return decData.toString();
}
//}
//export default aesUtils
