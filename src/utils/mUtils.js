export const fomatTime = (time) => {
  if(time){
    var newData =  Date.parse(new Date());
    var valueTime = Date.parse(time.replace(/-/g,"/"));
    var diffTime = Math.abs(newData-valueTime);
    if (diffTime >= 7 * 24 * 3600 * 1000) {
      return time;
    } else if (diffTime < 7 * 24 * 3600 * 1000 && diffTime >= 24 * 3600 * 1000) {
      //注释("一周之内");
      var dayNum = Math.floor(diffTime / (24 * 60 * 60 * 1000));
      return dayNum + "天前";
    } else if (diffTime < 24 * 3600 * 1000 && diffTime >= 3600 * 1000) {
      //注释("一天之内");
      var dayNum = Math.floor(diffTime / (60 * 60 * 1000));
      return dayNum + "小时前";
    } else if (diffTime < 3600 * 1000 && diffTime >= 60 * 1000) {
      //注释("一小时之内");
      var dayNum = Math.floor(diffTime / (60 * 1000));
      return dayNum + "分钟前";
    } else if (diffTime < 60 * 1000 && diffTime >= 0) {
      //注释("1分钟之内");
      return "刚刚";
    }else{
    	return time;
    }
  }
}