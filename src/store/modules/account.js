import store from '../index';
// import API5G from "../../utils/serviceAPI";
// import request from "../../utils/request";

const user = {
    state: {
        userCode: sessionStorage.getItem(`userCode`)||``,
        userName: '',
        roleCode: '',
        roleName: ''
    },

    mutations: {
        SET_USERCODE: (state, userCode) => {
            sessionStorage.setItem(`userCode`,userCode);
            state.userCode = userCode
        }
    },

    actions: {
        // Login({ commit }, loginForm) {
        //     return new Promise((resolve, reject) => {
        //         request.post(API5G.login, {
        //             ...loginForm
        //         }).then((r) => {
        //             if (r.data.ROOT.BODY.RETURN_CODE === '0') {
        //                 // sessionStorage.setItem('userCode', r.data.ROOT.BODY.OUT_DATA.LOGIN_NO);
        //                 sessionStorage.setItem('userName', r.data.ROOT.BODY.OUT_DATA.LOGIN_NAME);
        //                 sessionStorage.setItem('groupId', r.data.ROOT.BODY.OUT_DATA.GROUP_ID);
        //                 sessionStorage.setItem('groupName', r.data.ROOT.BODY.OUT_DATA.GROUP_NAME);
        //                 commit('SET_USERCODE', r.data.ROOT.BODY.OUT_DATA.LOGIN_NO);
        //                 // 遍历角色列表将默认角色存入state
        //                 let roles = r.data.ROOT.BODY.OUT_DATA.ROLE_LIST;
        //                 let roleCode = '';
        //                 let roleName = '';
        //                 for (let i in roles) {
        //                     if (roles[i].IS_INIT === 'Y') {
        //                         roleCode = roles[i].ROLE_CODE;
        //                         roleName = roles[i].ROLE_NAME;
        //                     }
        //                 }
        //                 sessionStorage.setItem('roleCode', roleCode);
        //                 sessionStorage.setItem('roleName', roleName);
        //                 sessionStorage.setItem('roleList', JSON.stringify(roles));
        //                 if(r.data.ROOT.BODY.OUT_DATA.REST_DAY <= 3){
        //                     console.log("--------------------------------------------------------------------------------")
        //                     this.$message.warning("账号即将过期，请联系系统管理员");
        //                 }
        //                 //判断密码距离失效时间是否小于三天，如果小于三天就到重置密码的界面，如果大于三天就进入首页
        //                 request.post(API5G.selfService.checkAccountTime,{
        //                     ...loginForm
        //                 }).then(r=>{
        //                     resolve(r.data.ROOT.BODY)
        //                 })
        //             } else {
        //                 reject(r.data.ROOT.BODY.RETURN_MSG)
        //             }
        //         }).catch((error) => {
        //             reject(error)
        //         });
        //     })
        // },

        // 登出
        // LogOut({ commit, state }) {
        //     return new Promise((resolve, reject) => {
        //         request.post(API5G.loginOut, {}).then(r => {
        //             console.log('退出登录', r)
        //             if (r.data.ROOT.BODY.RETURN_CODE === "0") {
        //                 commit('SET_USERCODE', '');
        //                 resolve(r.data.ROOT.BODY);
        //             } else {
        //                 this.$message.error(r.data.ROOT.BODY.RETURN_MSG);
        //             }
        //         })
        //         .catch(error => {
        //             reject(error)
        //         });
        //     })
        // }
    }
}

export default user
