const state = {
   noticeCount: '',
   userAvatar:''
}
// get something
const getters = {
 noticeCount: state => state.noticeCount,
 userAvatar: state => state.userAvatar
}

// commit something
const mutations = {
  setNoticeCount(state,noticeCount) {
    state.noticeCount=noticeCount;
  },
  setUserAvatar(state,userAvatar) {
  	console.log(userAvatar)
    state.userAvatar=userAvatar;
  }
}
export default {
  state,
  getters,
  mutations
}
