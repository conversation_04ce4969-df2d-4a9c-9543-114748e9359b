const state = {
   userId: '',
   account:'',
   userName:'',
   loginPhone:'',
   email:'',
   deptId:'',
   deptName:'',
   roles:[]
}
// get something
const getters = {
 userId: state => state.userId,
 account: state => state.account,
 userName: state => state.userName,
 loginPhone: state => state.loginPhone,
 email: state => state.email,
 deptId:state=>state.deptId,
 deptName:state=>state.deptName,
 roles:state=>state.roles
}

// commit something
const mutations = {
  setLoginInfo(state,res) {
    state.userId=res.id;
    state.account=res.account;
    state.userName=res.name;
    state.loginPhone=res.mobile;
    state.email=res.email;
    state.deptId=res.orgId;
    if(res.sysOrganization != undefined && res.sysOrganization != null){
      state.deptName=res.sysOrganization.name;
    }else{
      state.deptName=""
    }
    if(res.sysRoles&&res.sysRoles.length>0){
    	state.roles=res.sysRoles.map(function(value){
			  return value.id;
			})
    }else{
      state.roles=[];
    }
  }
}
export default {
  state,
  getters,
  mutations
}
