import Vue from 'vue';
import Vuex from 'vuex';
import account from './modules/account';
import setting from './modules/setting';
import getters from "./getters";
import loginInfo from './modules/loginInfo';
import navInfo from './modules/navInfo';
Vue.use(Vuex);

const store = new Vuex.Store({
    modules: {
        account,
        setting,
        loginInfo,
        navInfo
    },
    getters
});

export default store
