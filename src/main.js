// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import 'babel-polyfill';
import Es6Promise from 'es6-promise'
require('es6-promise').polyfill()
Es6Promise.polyfill()
import Vue from 'vue'
import App from './App'
import router from './router'
import ElementUI from 'element-ui';
import store from './store'
import './assets/icon/iconfont.css'

import 'element-ui/lib/theme-chalk/index.css'
import './styles/app.scss'; // global css

import Vue2Emoji from 'vuejs-emoji'
Vue.use(Vue2Emoji)

import clampy from '@clampy-js/vue-clampy';
Vue.use(clampy)

import uploader from 'vue-simple-uploader'
Vue.use(uploader)

//import VueUeditorWrap from 'vue-ueditor-wrap'
//Vue.component('vue-ueditor-wrap', VueUeditorWrap)

Vue.use(ElementUI);
Vue.config.productionTip = false

import {fomatTime} from './utils/mUtils'
Vue.filter('fomatTime', fomatTime);

import VueDOMPurifyHTML from 'vue-dompurify-html'
Vue.use(VueDOMPurifyHTML)

import './permission'

//import QRCode from 'qrcodejs2'

/* eslint-disable no-new */
new Vue({
  el: '#app',
	directives: {
	  clampy
	},
  router,
  store,
  components: { App },
  template: '<App/>'
})
